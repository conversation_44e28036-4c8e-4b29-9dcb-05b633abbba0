#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import os
import sys
import webbrowser
import threading
import time
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
if hasattr(sys, '_MEIPASS'):
    # PyInstaller环境
    bundle_dir = Path(sys._MEIPASS)
    sys.path.insert(0, str(bundle_dir))
    # 设置数据库路径到用户目录
    data_dir = current_dir / "data"
    data_dir.mkdir(exist_ok=True)
    os.environ['DATABASE_URL'] = f"sqlite:///{data_dir / 'restaurant.db'}"
else:
    # 开发环境
    sys.path.insert(0, str(current_dir))

def open_browser():
    """延迟打开浏览器"""
    time.sleep(3)  # 等待服务器启动
    webbrowser.open('http://127.0.0.1:5000')

def main():
    print("🥩 牛排餐厅管理系统 - 便携版")
    print("=" * 50)
    print("正在启动服务器...")
    print("服务器地址: http://127.0.0.1:5000")
    print("按 Ctrl+C 停止服务器")
    print("=" * 50)
    
    # 在后台线程中打开浏览器
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    # 启动Flask应用
    try:
        from app import app
        app.run(host='127.0.0.1', port=5000, debug=False)
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        input("按回车键退出...")

if __name__ == '__main__':
    main()
