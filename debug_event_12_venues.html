<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Find and explore upcoming sports events, tournaments, and championships from around the world.">
    <meta name="keywords" content="sports, events, tournaments, championships, football, basketball, tennis, swimming, athletics, golf, formula 1">
    <title>美国网球公开赛2025 - 体育赛事</title>
    <!-- Favicon -->
    <link rel="icon" href="/static/favicon.ico?q=1747998751" type="image/x-icon">
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/static/css/main.css?q=1747309699">
    <link rel="stylesheet" href="/static/css/custom.css?q=1747728358">
    <!-- Integrated Featured Events CSS -->
    <link rel="stylesheet" href="/static/css/integrated_featured_events.css?q=1748252361">
    
<!-- Leaflet CSS for interactive maps -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin="/">

<style>
    /* 赛事详情图片样式 */
    .event-detail-image {
        transition: transform 0.3s ease;
    }

    .event-image:hover .event-detail-image {
        transform: scale(1.02);
    }

    /* 相关赛事卡片样式 */
    .hover-shadow {
        transition: all 0.3s ease;
        border-radius: 0.75rem;
        overflow: hidden;
        border: none;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .hover-shadow:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 20px rgba(0,0,0,0.15);
    }

    .hover-shadow:hover .related-event-image {
        transform: scale(1.05);
    }

    .related-event-image {
        transition: transform 0.3s ease;
    }

    /* 强制显示图片 */
    .event-detail-image, .related-event-image {
        display: block !important;
        opacity: 1 !important;
        visibility: visible !important;
    }

    /* 徽章样式优化 */
    .badge {
        font-weight: 500;
        letter-spacing: 0.5px;
    }

    /* 标签页内容样式 */
    .tab-content {
        background: white;
        border-radius: 0 0 1rem 1rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .nav-tabs {
        border-bottom: none;
    }

    .nav-tabs .nav-link {
        border: none;
        border-radius: 1rem 1rem 0 0;
        margin-right: 0.5rem;
        background: #f8f9fa;
        color: #6c757d;
        transition: all 0.3s ease;
    }

    .nav-tabs .nav-link.active {
        background: white;
        color: #495057;
        box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
    }
</style>

    <!-- Open Graph tags for social sharing -->
    <meta property="og:title" content="US Open 2025 - Sports Events">
    <meta property="og:description" content="The US Open is the modern version of one of the oldest tennis championships in the world, the U.S. National Championship, which was first contested in 1881. It ">
    <meta property="og:image" content="/static/images/events/us_open_2025.jpg">
    <meta property="og:url" content="http://localhost/events/12">
    <meta property="og:type" content="website">
</head>
<body>
    <!-- Skip to content link for accessibility -->
    <a href="#main-content" class="skip-link">Skip to content</a>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-trophy"></i> 体育赛事
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link " href="/" aria-current="">
                            <i class="fas fa-home me-1"></i> 首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link " href="/events" aria-current="">
                            <i class="fas fa-calendar-alt me-1"></i> 赛事
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="categoriesDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-list me-1"></i> 分类
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="categoriesDropdown">
                            <li><a class="dropdown-item" href="/events?category=1">足球</a></li>
                            <li><a class="dropdown-item" href="/events?category=2">篮球</a></li>
                            <li><a class="dropdown-item" href="/events?category=3">网球</a></li>
                            <li><a class="dropdown-item" href="/events?category=4">游泳</a></li>
                            <li><a class="dropdown-item" href="/events?category=5">田径</a></li>
                            <li><a class="dropdown-item" href="/events?category=6">高尔夫</a></li>
                            <li><a class="dropdown-item" href="/events?category=7">一级方程式</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Coming Soon">
                            <i class="fas fa-newspaper me-1"></i> News
                        </a>
                    </li>
                </ul>
                <div class="d-flex align-items-center">
                    <div class="search-container position-relative me-3">
                        <form class="search-form" action="/events" method="get">
                            <div class="input-group">
                                <input class="form-control search-autocomplete" type="search" name="q" placeholder="搜索赛事" aria-label="搜索赛事" autocomplete="off">
                                <button class="btn btn-primary" type="submit">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </form>
                        <div class="search-results position-absolute w-100 bg-white shadow-lg rounded-bottom p-2" style="display: none; z-index: 1000;"></div>
                    </div>
                    <ul class="navbar-nav">
                        <li class="nav-item">
                            <a class="nav-link" href="/login" data-bs-toggle="tooltip" data-bs-placement="bottom" title="登录">
                                <i class="fas fa-sign-in-alt"></i>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/register" data-bs-toggle="tooltip" data-bs-placement="bottom" title="注册">
                                <i class="fas fa-user-plus"></i>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Language Switcher -->
                <div class="navbar-nav ms-auto">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="languageDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-globe me-1"></i>
                            中文
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="languageDropdown">
                            <li>
                                <a class="dropdown-item " href="/set_language/en">
                                    <i class="fas fa-flag-usa me-2"></i> English
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item active" href="/set_language/zh">
                                    <i class="fas fa-flag me-2" style="color: #de2910;"></i> 中文
                                </a>
                            </li>
                        </ul>
                    </li>
                </div>
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    
        
    

    <!-- Main Content -->
    <main id="main-content" class="py-4">
        
<div class="container">
    <!-- Breadcrumb Navigation -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="/">首页</a></li>
            <li class="breadcrumb-item"><a href="/events">赛事</a></li>
            <li class="breadcrumb-item"><a href="/events?category=3">网球</a></li>
            <li class="breadcrumb-item active" aria-current="page">美国网球公开赛2025</li>
        </ol>
    </nav>

    <!-- Event Header -->
    <div class="row mb-5 event-header">
        <div class="col-lg-8 col-md-7 order-md-1 order-2">
            <div class="d-flex align-items-center mb-3">
                <span class="badge bg-primary me-2">网球</span>
                
                <span class="text-muted">14 天</span>
            </div>
            <h1 class="mb-3">美国网球公开赛2025</h1>
            <div class="event-meta mb-4">
                <div class="event-meta-item">
                    <i class="fas fa-map-marker-alt"></i>
                    <span>美国纽约</span>
                </div>
                <div class="event-meta-item">
                    <i class="fas fa-calendar-alt"></i>
                    <span>2025年八月25日 - 2025年九月7日</span>
                </div>
            </div>
            <div class="countdown mb-4" data-event-date="2025-08-25">
                <!-- Countdown will be inserted by JavaScript -->
            </div>
            <p class="lead">美国网球公开赛是世界上最古老的网球锦标赛之一——美国全国锦标赛的现代版本，该赛事首次举办于1881年。它是一年中第四个也是最后一个大满贯锦标赛，在澳大利亚网球公开赛、法国网球公开赛和温布尔登网球锦标赛之后举行。</p>
            <div class="d-flex mt-4">
                <a href="#schedule" class="btn btn-primary me-2">
                    <i class="fas fa-calendar-alt me-1"></i> 查看赛程
                </a>
                <div class="share-buttons ms-auto">
                    <a href="#" class="share-button share-facebook" aria-label="Share on Facebook">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <a href="#" class="share-button share-twitter" aria-label="Share on Twitter">
                        <i class="fab fa-twitter"></i>
                    </a>
                    <a href="#" class="share-button share-email" aria-label="Share via Email">
                        <i class="fas fa-envelope"></i>
                    </a>
                </div>
            </div>
        </div>
        <div class="col-lg-4 col-md-5 order-md-2 order-1 mb-md-0 mb-4">
            <div class="event-image shadow-lg" style="border-radius: 1rem; overflow: hidden; position: relative;">
                <img src="/static/images/events/us_open_2025.jpg"
                     class="img-fluid event-detail-image"
                     alt="US Open 2025"
                     style="width: 100%; height: 380px; object-fit: cover; display: block !important; opacity: 1 !important; visibility: visible !important; border-radius: 1rem; transition: transform 0.3s ease;"
                     onerror="console.log('Event detail image failed:', this.src); this.src='/static/images/events/us_open_2025.jpg'; this.style.background='linear-gradient(135deg, #f8f9fa, #e9ecef)'; this.style.display='block';"
                     onload="console.log('Event detail image loaded:', this.src); this.style.display='block'; this.style.opacity='1';"
                     loading="lazy">
                
                <div class="position-absolute bottom-0 start-0 m-3">
                    <span class="badge bg-primary px-3 py-2" style="border-radius: 1rem; backdrop-filter: blur(10px); background: rgba(13, 110, 253, 0.9);">
                        <i class="fas fa-tag me-1"></i>网球
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Tabs Navigation -->
    <ul class="nav nav-tabs mb-4" id="eventTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="info-tab" data-bs-toggle="tab" data-bs-target="#info" type="button" role="tab" aria-controls="info" aria-selected="true">
                <i class="fas fa-info-circle me-2"></i>概览
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="schedule-tab" data-bs-toggle="tab" data-bs-target="#schedule" type="button" role="tab" aria-controls="schedule" aria-selected="false">
                <i class="fas fa-calendar-alt me-2"></i>赛程
                
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="venue-tab" data-bs-toggle="tab" data-bs-target="#venue" type="button" role="tab" aria-controls="venue" aria-selected="false">
                <i class="fas fa-map-marked-alt me-2"></i>场馆
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="history-tab" data-bs-toggle="tab" data-bs-target="#history" type="button" role="tab" aria-controls="history" aria-selected="false">
                <i class="fas fa-history me-2"></i>历史
                
                    <span class="badge rounded-pill bg-primary ms-1">3</span>
                
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="comments-tab" data-bs-toggle="tab" data-bs-target="#comments" type="button" role="tab" aria-controls="comments" aria-selected="false">
                <i class="fas fa-comments me-2"></i>Comments
                
            </button>
        </li>
    </ul>

    <!-- Tabs Content -->
    <div class="tab-content" id="eventTabsContent">
        <!-- Basic Information Tab -->
        <div class="tab-pane fade show active" id="info" role="tabpanel" aria-labelledby="info-tab">
            <div class="row">
                <div class="col-lg-8">
                    <div class="card shadow-sm mb-4">
                        <div class="card-body">
                            <h3 class="card-title">关于赛事</h3>
                            <div class="card-text">
                                美国网球公开赛是世界上最古老的网球锦标赛之一——美国全国锦标赛的现代版本，该赛事首次举办于1881年。它是一年中第四个也是最后一个大满贯锦标赛，在澳大利亚网球公开赛、法国网球公开赛和温布尔登网球锦标赛之后举行。
                            </div>

                            
                            <h4 class="mt-4">亮点</h4>
                            <div class="row mt-3">
                                
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="bg-primary rounded-circle p-3 me-3">
                                            <i class="fas fa-city text-white"></i>
                                        </div>
                                        <div>
                                            <h5 class="mb-1">纽约活力</h5>
                                            <p class="mb-0 text-muted">在纽约市中心举行</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="bg-primary rounded-circle p-3 me-3">
                                            <i class="fas fa-moon text-white"></i>
                                        </div>
                                        <div>
                                            <h5 class="mb-1">夜场比赛</h5>
                                            <p class="mb-0 text-muted">以激动人心的夜场比赛氛围著称</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="bg-primary rounded-circle p-3 me-3">
                                            <i class="fas fa-dollar-sign text-white"></i>
                                        </div>
                                        <div>
                                            <h5 class="mb-1">最高奖金</h5>
                                            <p class="mb-0 text-muted">网球界最大的奖金池</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="bg-primary rounded-circle p-3 me-3">
                                            <i class="fas fa-bolt text-white"></i>
                                        </div>
                                        <div>
                                            <h5 class="mb-1">硬地球场速度</h5>
                                            <p class="mb-0 text-muted">快速硬地球场有利于攻击性打法</p>
                                        </div>
                                    </div>
                                </div>
                                
                            </div>
                            
                        </div>
                    </div>

                    <div class="card shadow-sm">
                        <div class="card-body">
                            <h4 class="card-title">相关赛事</h4>
                            <div class="row g-3 mt-2">
                                
                                    
                                        <div class="col-12">
                                            <p class="text-muted">未找到相关赛事。</p>
                                        </div>
                                    
                                
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>赛事详情</h5>
                        </div>
                        <div class="card-body">
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item d-flex align-items-center py-3">
                                    <i class="fas fa-map-marker-alt text-primary me-3"></i>
                                    <div>
                                        <strong>地点</strong>
                                        <div>美国纽约</div>
                                    </div>
                                </li>
                                <li class="list-group-item d-flex align-items-center py-3">
                                    <i class="fas fa-calendar-alt text-primary me-3"></i>
                                    <div>
                                        <strong>日期</strong>
                                        <div>August 25, 2025</div>
                                    </div>
                                </li>
                                <li class="list-group-item d-flex align-items-center py-3">
                                    <i class="fas fa-calendar-check text-primary me-3"></i>
                                    <div>
                                        <strong>日期</strong>
                                        <div>September 07, 2025</div>
                                    </div>
                                </li>
                                <li class="list-group-item d-flex align-items-center py-3">
                                    <i class="fas fa-clock text-primary me-3"></i>
                                    <div>
                                        <strong>持续时间</strong>
                                        <div>14 天</div>
                                    </div>
                                </li>
                                <li class="list-group-item d-flex align-items-center py-3">
                                    <i class="fas fa-tag text-primary me-3"></i>
                                    <div>
                                        <strong>类别</strong>
                                        <div>网球</div>
                                    </div>
                                </li>
                            </ul>
                        </div>
                        <div class="card-footer bg-light">
                            <a href="#" class="btn btn-primary w-100">
                                <i class="fas fa-ticket-alt me-2"></i>获取门票
                            </a>
                        </div>
                    </div>

                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="fas fa-share-alt me-2"></i>分享此赛事</h5>
                        </div>
                        <div class="card-body">
                            <p class="text-muted mb-3">与您的朋友和家人分享此赛事</p>
                            <div class="d-flex justify-content-between">
                                <a href="#" class="btn btn-outline-primary flex-grow-1 me-2">
                                    <i class="fab fa-facebook-f me-2"></i>Facebook
                                </a>
                                <a href="#" class="btn btn-outline-info flex-grow-1 me-2">
                                    <i class="fab fa-twitter me-2"></i>Twitter
                                </a>
                                <a href="#" class="btn btn-outline-success flex-grow-1">
                                    <i class="fas fa-envelope me-2"></i>邮箱
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="card shadow-sm">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="fas fa-bell me-2"></i>提醒</h5>
                        </div>
                        <div class="card-body">
                            <p>不要错过这个赛事！设置提醒以在开始前收到通知。</p>
                            <form>
                                <div class="mb-3">
                                    <label for="reminderEmail" class="form-label">邮箱</label>
                                    <input type="email" class="form-control" id="reminderEmail" placeholder="<EMAIL>">
                                </div>
                                <div class="mb-3">
                                    <label for="reminderTime" class="form-label">提醒我</label>
                                    <select class="form-select" id="reminderTime">
                                        <option value="1">1 天前</option>
                                        <option value="3">3 天前</option>
                                        <option value="7">1 周前</option>
                                        <option value="14">2 周前</option>
                                    </select>
                                </div>
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-bell me-2"></i>设置提醒
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Schedule Tab -->
        <div class="tab-pane fade" id="schedule" role="tabpanel" aria-labelledby="schedule-tab">
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <h3 class="mb-0">event_schedule</h3>
                        <div>
                            <button class="btn btn-outline-primary btn-sm me-2" id="calendarViewBtn">
                                <i class="fas fa-calendar-alt me-1"></i> calendar_view
                            </button>
                            <button class="btn btn-primary btn-sm" id="listViewBtn">
                                <i class="fas fa-list me-1"></i> list_view
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            
                <div class="alert alert-info shadow-sm">
                    <i class="fas fa-info-circle me-2"></i>
                    此赛事暂无赛程信息.
                </div>
                <div class="text-center py-5">
                    <img src="/static/images/no-schedule.png?q=1747728380" alt="No schedule" class="img-fluid mb-4" style="max-width: 200px;">
                    <h4>赛程即将公布</h4>
                    <p class="text-muted">我们正在最终确定此赛事的赛程.</p>
                </div>
            
        </div>

        <!-- Venue Tab -->
        <div class="tab-pane fade" id="venue" role="tabpanel" aria-labelledby="venue-tab">
            <div class="row">
                <div class="col-lg-8">
                    <h3 class="mb-4">event_venue</h3>
                    <div class="card shadow-sm mb-4">
                        <div class="card-body p-0">
                            <!-- Interactive Map (using OpenStreetMap) -->
                            <div id="venue-map" class="rounded" style="height: 400px; width: 100%;"></div>
                        </div>
                    </div>

                    
                    <div class="card shadow-sm mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">venue_information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6><i class="fas fa-building text-primary me-2"></i>facility_details</h6>
                                    <ul class="list-unstyled ms-4 mb-4">
                                        <li class="mb-2"><strong>name:</strong> All England Lawn Tennis Club</li>
                                        <li class="mb-2"><strong>capacity:</strong> 42,000 (total grounds)</li>
                                        <li class="mb-2"><strong>surface:</strong> Grass Courts</li>
                                        <li class="mb-2"><strong>opened:</strong> 1877</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6><i class="fas fa-info-circle text-primary me-2"></i>amenities</h6>
                                    <ul class="list-unstyled ms-4 mb-4">
                                        
                                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Wimbledon Museum</li>
                                        
                                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>草莓与奶油</li>
                                        
                                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Championship Shop</li>
                                        
                                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Henman Hill</li>
                                        
                                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Rolex Debenture Holders</li>
                                        
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                </div>

                <div class="col-lg-4">
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="fas fa-directions me-2"></i>getting_there</h5>
                        </div>
                        <div class="card-body">
                            <h6 class="mb-3">address</h6>
                            <p class="mb-3">美国纽约</p>

                            
                            <h6 class="mb-2"><i class="fas fa-subway text-primary me-2"></i>transportation</h6>
                            <ul class="list-unstyled ms-4 mb-3">
                                
                                <li class="mb-2"><i class="fas fa-arrow-right text-primary me-2"></i>London Underground District Line</li>
                                
                                <li class="mb-2"><i class="fas fa-arrow-right text-primary me-2"></i>Bus Routes 57, 93, 200</li>
                                
                                <li class="mb-2"><i class="fas fa-arrow-right text-primary me-2"></i>Wimbledon Station</li>
                                
                                <li class="mb-2"><i class="fas fa-arrow-right text-primary me-2"></i>Park &amp; Ride</li>
                                
                            </ul>
                            

                            <h6 class="mb-2"><i class="fas fa-car text-primary me-2"></i>by_car</h6>
                            <ul class="list-unstyled ms-4 mb-3">
                                <li class="mb-2">parking_available</li>
                                <li class="mb-2">gps_coordinates: 40.7128° N, 74.0060° W</li>
                            </ul>

                            <a href="https://www.google.com/maps/search/?api=1&query=New%20York%2C%20USA" class="btn btn-primary w-100" target="_blank">
                                <i class="fas fa-map-marked-alt me-2"></i>get_directions
                            </a>
                        </div>
                    </div>

                    <div class="card shadow-sm">
                        <div class="card-header">
                            <h5 class="mb-0">nearby_accommodations</h5>
                        </div>
                        <div class="card-body p-0">
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item d-flex align-items-center py-3">
                                    <i class="fas fa-hotel text-primary me-3"></i>
                                    <div>
                                        <strong>grand_hotel</strong>
                                        <div class="text-muted">0.5 miles_away</div>
                                    </div>
                                    <a href="#" class="btn btn-sm btn-outline-primary ms-auto">book</a>
                                </li>
                                <li class="list-group-item d-flex align-items-center py-3">
                                    <i class="fas fa-hotel text-primary me-3"></i>
                                    <div>
                                        <strong>city_suites</strong>
                                        <div class="text-muted">0.8 miles_away</div>
                                    </div>
                                    <a href="#" class="btn btn-sm btn-outline-primary ms-auto">book</a>
                                </li>
                                <li class="list-group-item d-flex align-items-center py-3">
                                    <i class="fas fa-hotel text-primary me-3"></i>
                                    <div>
                                        <strong>sports_resort</strong>
                                        <div class="text-muted">1.2 miles_away</div>
                                    </div>
                                    <a href="#" class="btn btn-sm btn-outline-primary ms-auto">book</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Historical Data Tab -->
        <div class="tab-pane fade" id="history" role="tabpanel" aria-labelledby="history-tab">
            <h3>historical_data</h3>
            
                <div class="accordion" id="historyAccordion">
                    
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="heading1738939541648">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                        data-bs-target="#collapse1738939541648" aria-expanded="false" aria-controls="collapse1738939541648">
                                    2024 - 美国网球公开赛2024
                                </button>
                            </h2>
                            <div id="collapse1738939541648" class="accordion-collapse collapse" aria-labelledby="heading1738939541648"
                                 data-bs-parent="#historyAccordion">
                                <div class="accordion-body">
                                    <p>2024年美国网球公开赛男单决赛中，雅尼克·辛纳击败泰勒·弗里茨，女单决赛中，阿丽娜·萨巴伦卡击败杰西卡·佩古拉。</p>
                                    <p><strong>result:</strong> Men&#39;s Singles: Jannik Sinner, Women&#39;s Singles: Aryna Sabalenka</p>
                                </div>
                            </div>
                        </div>
                    
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="heading1738939541984">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                        data-bs-target="#collapse1738939541984" aria-expanded="false" aria-controls="collapse1738939541984">
                                    2023 - 美国网球公开赛2023
                                </button>
                            </h2>
                            <div id="collapse1738939541984" class="accordion-collapse collapse" aria-labelledby="heading1738939541984"
                                 data-bs-parent="#historyAccordion">
                                <div class="accordion-body">
                                    <p>2023年美国网球公开赛男单决赛中，诺瓦克·德约科维奇击败丹尼尔·梅德韦杰夫，女单决赛中，科科·高芙击败阿丽娜·萨巴伦卡。</p>
                                    <p><strong>result:</strong> Men&#39;s Singles: Novak Djokovic, Women&#39;s Singles: Coco Gauff</p>
                                </div>
                            </div>
                        </div>
                    
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="heading1738939542096">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                        data-bs-target="#collapse1738939542096" aria-expanded="false" aria-controls="collapse1738939542096">
                                    2022 - 美国网球公开赛2022
                                </button>
                            </h2>
                            <div id="collapse1738939542096" class="accordion-collapse collapse" aria-labelledby="heading1738939542096"
                                 data-bs-parent="#historyAccordion">
                                <div class="accordion-body">
                                    <p>2022年美国网球公开赛男单决赛中，卡洛斯·阿尔卡拉斯击败卡斯珀·鲁德，女单决赛中，伊加·斯维亚特克击败翁斯·贾贝尔。</p>
                                    <p><strong>result:</strong> Men&#39;s Singles: Carlos Alcaraz, Women&#39;s Singles: Iga Świątek</p>
                                </div>
                            </div>
                        </div>
                    
                </div>
            
        </div>

        <!-- Comments Tab -->
        <div class="tab-pane fade" id="comments" role="tabpanel" aria-labelledby="comments-tab">
            <h3>Comments</h3>

            <div class="alert alert-info mb-4">
                Please <a href="/login?next=/events/12">login</a> to leave a comment
            </div>

            
                <div class="alert alert-light">
                    No comments yet. Be the first to comment!
                </div>
            
        </div>
    </div>
</div>

    </main>

    <!-- Back to top button -->
    <button class="back-to-top" aria-label="back_to_top">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- Dark mode toggle -->
    <button class="dark-mode-toggle" aria-label="Toggle dark mode">
        <i class="fas fa-moon"></i>
    </button>

    <!-- Cookie consent banner -->
    <div class="cookie-banner" style="display: none;">
        <p>We use cookies to enhance your experience. By continuing to visit this site you agree to our use of cookies.</p>
        <div class="btn-group">
            <button class="btn btn-primary accept-cookies">Accept</button>
            <button class="btn btn-outline-secondary decline-cookies">Decline</button>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-5 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 col-md-6 mb-4 mb-md-0">
                    <h5 class="mb-3"><i class="fas fa-trophy me-2"></i>体育赛事</h5>
                    <p class="mb-3">您获取全球体育赛事信息和更新的一站式平台。</p>
                    <div class="social-links mb-4">
                        <a href="#" class="me-2 text-white" aria-label="Facebook"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="me-2 text-white" aria-label="Twitter"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="me-2 text-white" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="me-2 text-white" aria-label="YouTube"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>
                <div class="col-lg-2 col-md-6 mb-4 mb-md-0">
                    <h5 class="mb-3">quick_links</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="/" class="text-white"><i class="fas fa-chevron-right me-1"></i>首页</a></li>
                        <li class="mb-2"><a href="/events" class="text-white"><i class="fas fa-chevron-right me-1"></i>赛事</a></li>
                        <li class="mb-2"><a href="#" class="text-white"><i class="fas fa-chevron-right me-1"></i>关于</a></li>
                        <li class="mb-2"><a href="#" class="text-white"><i class="fas fa-chevron-right me-1"></i>联系我们</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-6 mb-4 mb-md-0">
                    <h5 class="mb-3">分类</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="/events?category=1" class="text-white"><i class="fas fa-chevron-right me-1"></i>足球</a></li>
                        <li class="mb-2"><a href="/events?category=2" class="text-white"><i class="fas fa-chevron-right me-1"></i>篮球</a></li>
                        <li class="mb-2"><a href="/events?category=3" class="text-white"><i class="fas fa-chevron-right me-1"></i>网球</a></li>
                        <li class="mb-2"><a href="/events?category=7" class="text-white"><i class="fas fa-chevron-right me-1"></i>一级方程式</a></li>
                    </ul>
                </div>
                <div class="col-lg-4 col-md-6 mb-4 mb-md-0">
                    <h5 class="mb-3">新闻通讯</h5>
                    <p class="mb-3">订阅我们的新闻通讯，获取最新体育赛事更新</p>
                    <form class="newsletter-form">
                        <div class="input-group">
                            <input type="email" class="form-control" placeholder="输入您的邮箱" aria-label="输入您的邮箱" required>
                            <button class="btn btn-primary" type="submit">订阅</button>
                        </div>
                    </form>
                    <div class="mt-4">
                        <h5 class="mb-3">联系我们</h5>
                        <address class="mb-0">
                            <p class="mb-2"><i class="fas fa-map-marker-alt me-2"></i>北京市朝阳区体育场路123号</p>
                            <p class="mb-2"><i class="fas fa-phone me-2"></i>+86 10 1234 5678</p>
                            <p class="mb-0"><i class="fas fa-envelope me-2"></i><EMAIL></p>
                        </address>
                    </div>
                </div>
            </div>
            <hr class="my-4">
            <div class="row align-items-center">
                <div class="col-md-6 text-center text-md-start">
                    <p class="mb-md-0">&copy; 2025 体育赛事. 版权所有.</p>
                </div>
                <div class="col-md-6 text-center text-md-end">
                    <ul class="list-inline mb-0">
                        <li class="list-inline-item"><a href="#" class="text-white">隐私政策</a></li>
                        <li class="list-inline-item">|</li>
                        <li class="list-inline-item"><a href="#" class="text-white">服务条款</a></li>
                        <li class="list-inline-item">|</li>
                        <li class="list-inline-item"><a href="#" class="text-white">Cookie政策</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Ultimate Image Display Fix - Prevent Disappearing -->
    <style>
        /* 强制显示所有图片 - 最高优先级 */
        img, .card-img-top, .event-list-image, .event-detail-image, .category-image, .related-event-image {
            display: block !important;
            opacity: 1 !important;
            visibility: visible !important;
            transition: none !important;
            animation: none !important;
            max-width: 100% !important;
            height: auto !important;
        }

        /* 特定图片类型的强制显示 */
        .category-card img,
        .event-card img,
        .card img,
        img[src*="/static/images/"] {
            display: block !important;
            opacity: 1 !important;
            visibility: visible !important;
            transition: none !important;
            animation: none !important;
        }

        /* 防止任何隐藏机制 */
        .force-show-images img,
        .force-show-images .card-img-top,
        .force-show-images .event-list-image,
        .force-show-images .event-detail-image,
        .force-show-images .category-image,
        .force-show-images .related-event-image {
            display: block !important;
            opacity: 1 !important;
            visibility: visible !important;
            transition: none !important;
            animation: none !important;
        }

        /* 覆盖可能的Bootstrap或其他CSS */
        .d-none img, .invisible img, .opacity-0 img {
            display: block !important;
            opacity: 1 !important;
            visibility: visible !important;
        }

        /* 确保图片容器也显示 */
        .category-card, .event-card, .card {
            display: block !important;
            opacity: 1 !important;
            visibility: visible !important;
        }
    </style>

    <script>
        // 简化的图片显示修复 - 避免性能问题
        (function() {
            'use strict';

            // 立即添加保护类
            document.documentElement.classList.add('force-show-images');

            // 简化的图片保护函数
            function fixImages() {
                try {
                    const images = document.querySelectorAll('img');
                    images.forEach(img => {
                        // 只设置基本的显示样式，不锁定属性
                        if (img.style.display === 'none' || img.style.opacity === '0' || img.style.visibility === 'hidden') {
                            img.style.display = 'block';
                            img.style.opacity = '1';
                            img.style.visibility = 'visible';
                        }

                        // 移除可能的隐藏类
                        img.classList.remove('d-none', 'invisible', 'opacity-0');
                    });
                } catch (error) {
                    console.log('Image fix error:', error);
                }
            }

            // 页面加载时执行
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', fixImages);
            } else {
                fixImages();
            }

            // 页面完全加载后执行
            window.addEventListener('load', fixImages);

            // 延迟执行一次，确保所有动态内容加载完成
            setTimeout(fixImages, 1000);
        })();
    </script>
    <!-- Custom JS -->
    <script src="/static/js/main.js?q=1747309725"></script>
    <script src="/static/js/custom.js?q=1747726551"></script>
    <!-- Page-specific JavaScript -->
    
<!-- Leaflet JS for interactive maps -->
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin="/"></script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize map when venue tab is shown
        const venueTab = document.getElementById('venue-tab');
        if (venueTab) {
            venueTab.addEventListener('shown.bs.tab', function (e) {
                initMap();
            });
        }

        // Initialize map function
        function initMap() {
            // Check if map is already initialized
            if (window.venueMap) return;

            // Create map centered on event location
            
            const lat = 51.4342;
            const lng = -0.2149;
            

            window.venueMap = L.map('venue-map').setView([lat, lng], 13);

            // Add OpenStreetMap tiles
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            }).addTo(window.venueMap);

            // Add marker for event location
            const marker = L.marker([lat, lng]).addTo(window.venueMap);
            marker.bindPopup("<b>US Open 2025</b><br>New York, USA").openPopup();

            // Add nearby points of interest
            const pois = [
                { name: "Grand Hotel", type: "hotel", lat: 40.7158, lng: -74.0090 },
                { name: "City Suites", type: "hotel", lat: 40.7188, lng: -74.0120 },
                { name: "Central Station", type: "transport", lat: 40.7100, lng: -74.0040 },
                { name: "Restaurant Row", type: "food", lat: 40.7148, lng: -74.0020 },
                { name: "Shopping Mall", type: "shopping", lat: 40.7110, lng: -74.0080 }
            ];

            // Add POI markers with different colors based on type
            pois.forEach(poi => {
                let markerColor = 'blue';
                let icon = 'home';

                if (poi.type === 'hotel') {
                    markerColor = 'orange';
                    icon = 'home';
                } else if (poi.type === 'transport') {
                    markerColor = 'green';
                    icon = 'subway';
                } else if (poi.type === 'food') {
                    markerColor = 'red';
                    icon = 'utensils';
                } else if (poi.type === 'shopping') {
                    markerColor = 'purple';
                    icon = 'shopping-bag';
                }

                const poiMarker = L.marker([poi.lat, poi.lng]).addTo(window.venueMap);
                poiMarker.bindPopup(`<b>${poi.name}</b><br>${poi.type}`);
            });

            // Resize map when window is resized
            window.addEventListener('resize', function() {
                window.venueMap.invalidateSize();
            });
        }

        // Initialize tabs with history support
        const triggerTabList = document.querySelectorAll('#eventTabs button');
        triggerTabList.forEach(function (triggerEl) {
            const tabTrigger = new bootstrap.Tab(triggerEl);

            triggerEl.addEventListener('click', function (e) {
                e.preventDefault();
                tabTrigger.show();

                // Update URL hash
                const tabId = triggerEl.getAttribute('data-bs-target');
                window.location.hash = tabId;
            });
        });

        // Show tab based on URL hash
        if (window.location.hash) {
            try {
                const activeTab = document.querySelector(`#eventTabs button[data-bs-target="${window.location.hash}"]`);
                if (activeTab) {
                    const tab = new bootstrap.Tab(activeTab);
                    tab.show();

                    // Initialize map if venue tab is active
                    if (window.location.hash === '#venue') {
                        setTimeout(initMap, 100); // Small delay to ensure the tab is visible
                    }
                }
            } catch (error) {
                console.error('Error showing tab:', error);
            }
        }

        // Initialize countdown timer
        const countdownElement = document.querySelector('.countdown');
        if (countdownElement) {
            const eventDate = new Date(countdownElement.dataset.eventDate).getTime();

            const countdownTimer = setInterval(() => {
                const now = new Date().getTime();
                const distance = eventDate - now;

                if (distance < 0) {
                    clearInterval(countdownTimer);
                    countdownElement.innerHTML = '<div class="alert alert-info">此赛事已开始！</div>';
                    return;
                }

                const days = Math.floor(distance / (1000 * 60 * 60 * 24));
                const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
                const seconds = Math.floor((distance % (1000 * 60)) / 1000);

                countdownElement.innerHTML = `
                    <div class="countdown-item">
                        <span class="countdown-number">${days}</span>
                        <span class="countdown-label">天</span>
                    </div>
                    <div class="countdown-item">
                        <span class="countdown-number">${hours}</span>
                        <span class="countdown-label">小时</span>
                    </div>
                    <div class="countdown-item">
                        <span class="countdown-number">${minutes}</span>
                        <span class="countdown-label">分钟</span>
                    </div>
                    <div class="countdown-item">
                        <span class="countdown-number">${seconds}</span>
                        <span class="countdown-label">秒</span>
                    </div>
                `;
            }, 1000);
        }
    });
</script>


    <!-- Accessibility script -->
    <script>
        // Add 'aria-current' to active navigation items
        document.addEventListener('DOMContentLoaded', function() {
            const currentPath = window.location.pathname;
            document.querySelectorAll('.navbar-nav .nav-link').forEach(link => {
                if (link.getAttribute('href') === currentPath) {
                    link.setAttribute('aria-current', 'page');
                    link.classList.add('active');
                }
            });
        });
    </script>
</body>
</html>