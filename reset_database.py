#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重置数据库并添加所有菜品
"""

from app import app, db
from models import Category, MenuItem, User
from werkzeug.security import generate_password_hash

def reset_database():
    with app.app_context():
        # 删除所有菜品和分类
        MenuItem.query.delete()
        Category.query.delete()
        
        # 重新创建分类
        categories = [
            Category(name='牛排类', description='各种牛排'),
            Category(name='海鲜类', description='新鲜海鲜'),
            Category(name='鸡肉类', description='各式鸡肉料理'),
            Category(name='猪肉类', description='精选猪肉菜品'),
            Category(name='羊肉类', description='优质羊肉料理'),
            Category(name='鱼类', description='各种鱼类料理'),
            Category(name='烧烤类', description='炭火烧烤'),
            Category(name='火锅类', description='各式火锅'),
            Category(name='川菜', description='四川风味菜品'),
            Category(name='粤菜', description='广东风味菜品'),
            Category(name='湘菜', description='湖南风味菜品'),
            Category(name='鲁菜', description='山东风味菜品'),
            Category(name='苏菜', description='江苏风味菜品'),
            Category(name='浙菜', description='浙江风味菜品'),
            Category(name='闽菜', description='福建风味菜品'),
            Category(name='徽菜', description='安徽风味菜品'),
            Category(name='西餐', description='西式料理'),
            Category(name='日料', description='日式料理'),
            Category(name='韩料', description='韩式料理'),
            Category(name='泰菜', description='泰式料理'),
            Category(name='印度菜', description='印度风味'),
            Category(name='意大利菜', description='意式料理'),
            Category(name='法国菜', description='法式料理'),
            Category(name='开胃菜', description='餐前小食'),
            Category(name='汤品', description='各式汤品'),
            Category(name='沙拉', description='新鲜沙拉'),
            Category(name='主食', description='米饭面条类'),
            Category(name='素食', description='素食料理'),
            Category(name='甜品', description='餐后甜品'),
            Category(name='饮品', description='各种饮料'),
            Category(name='酒类', description='红酒白酒啤酒'),
            Category(name='咖啡茶饮', description='咖啡茶类饮品'),
            Category(name='小食', description='休闲小食'),
            Category(name='早餐', description='早餐系列'),
            Category(name='夜宵', description='夜宵系列')
        ]
        
        for category in categories:
            db.session.add(category)
        
        db.session.commit()
        print(f"成功创建 {len(categories)} 个分类！")
        
        # 获取分类ID
        steak_cat = Category.query.filter_by(name='牛排类').first()
        seafood_cat = Category.query.filter_by(name='海鲜类').first()
        chicken_cat = Category.query.filter_by(name='鸡肉类').first()
        pork_cat = Category.query.filter_by(name='猪肉类').first()
        lamb_cat = Category.query.filter_by(name='羊肉类').first()
        fish_cat = Category.query.filter_by(name='鱼类').first()
        bbq_cat = Category.query.filter_by(name='烧烤类').first()
        hotpot_cat = Category.query.filter_by(name='火锅类').first()
        sichuan_cat = Category.query.filter_by(name='川菜').first()
        cantonese_cat = Category.query.filter_by(name='粤菜').first()
        appetizer_cat = Category.query.filter_by(name='开胃菜').first()
        soup_cat = Category.query.filter_by(name='汤品').first()
        salad_cat = Category.query.filter_by(name='沙拉').first()
        dessert_cat = Category.query.filter_by(name='甜品').first()
        drink_cat = Category.query.filter_by(name='饮品').first()
        
        # 添加示例菜品
        dishes = [
            # 牛排类
            MenuItem(name='菲力牛排', description='嫩滑菲力牛排，口感极佳', price=188.0, category_id=steak_cat.id, preparation_time=20),
            MenuItem(name='西冷牛排', description='经典西冷牛排，肉质鲜美', price=158.0, category_id=steak_cat.id, preparation_time=18),
            MenuItem(name='T骨牛排', description='T骨牛排，一次享受两种口感', price=228.0, category_id=steak_cat.id, preparation_time=22),
            MenuItem(name='肋眼牛排', description='肋眼牛排，油花丰富', price=198.0, category_id=steak_cat.id, preparation_time=20),
            MenuItem(name='战斧牛排', description='超大份战斧牛排，适合分享', price=388.0, category_id=steak_cat.id, preparation_time=25),
            
            # 海鲜类
            MenuItem(name='波士顿龙虾', description='新鲜波士顿龙虾，肉质鲜甜', price=298.0, category_id=seafood_cat.id, preparation_time=25),
            MenuItem(name='阿拉斯加帝王蟹', description='阿拉斯加帝王蟹腿，肉质饱满', price=388.0, category_id=seafood_cat.id, preparation_time=20),
            MenuItem(name='烤三文鱼', description='挪威三文鱼，肉质鲜美', price=128.0, category_id=seafood_cat.id, preparation_time=15),
            MenuItem(name='蒜蓉扇贝', description='新鲜扇贝配蒜蓉，鲜香可口', price=88.0, category_id=seafood_cat.id, preparation_time=10),
            MenuItem(name='黄油焗生蚝', description='法式黄油焗生蚝，奶香浓郁', price=68.0, category_id=seafood_cat.id, preparation_time=8),
            
            # 川菜
            MenuItem(name='麻婆豆腐', description='经典川菜麻婆豆腐，麻辣鲜香', price=32.0, category_id=sichuan_cat.id, preparation_time=12),
            MenuItem(name='宫保鸡丁', description='宫保鸡丁，酸甜微辣', price=48.0, category_id=sichuan_cat.id, preparation_time=15),
            MenuItem(name='回锅肉', description='四川回锅肉，香辣下饭', price=58.0, category_id=sichuan_cat.id, preparation_time=18),
            MenuItem(name='水煮牛肉', description='水煮牛肉，麻辣鲜嫩', price=78.0, category_id=sichuan_cat.id, preparation_time=20),
            MenuItem(name='口水鸡', description='四川口水鸡，麻辣鲜香', price=58.0, category_id=sichuan_cat.id, preparation_time=15),
            
            # 粤菜
            MenuItem(name='白切鸡', description='广式白切鸡，清淡鲜美', price=78.0, category_id=cantonese_cat.id, preparation_time=30),
            MenuItem(name='蜜汁叉烧', description='广式蜜汁叉烧，甜香可口', price=88.0, category_id=cantonese_cat.id, preparation_time=25),
            MenuItem(name='白灼虾', description='白灼虾，鲜甜原味', price=98.0, category_id=cantonese_cat.id, preparation_time=8),
            MenuItem(name='干炒牛河', description='干炒牛河，香滑可口', price=38.0, category_id=cantonese_cat.id, preparation_time=12),
            MenuItem(name='虾饺', description='广式虾饺，皮薄馅鲜', price=48.0, category_id=cantonese_cat.id, preparation_time=15),
            
            # 开胃菜
            MenuItem(name='蒜蓉面包', description='香脆蒜蓉面包，开胃首选', price=28.0, category_id=appetizer_cat.id, preparation_time=8),
            MenuItem(name='芝士焗蘑菇', description='新鲜蘑菇配芝士焗制', price=48.0, category_id=appetizer_cat.id, preparation_time=12),
            MenuItem(name='鹅肝酱', description='法式鹅肝酱，搭配吐司享用', price=88.0, category_id=appetizer_cat.id, preparation_time=10),
            MenuItem(name='烟熏三文鱼', description='挪威烟熏三文鱼，配柠檬', price=68.0, category_id=appetizer_cat.id, preparation_time=5),
            
            # 甜品
            MenuItem(name='提拉米苏', description='经典意式提拉米苏，香甜浓郁', price=48.0, category_id=dessert_cat.id, preparation_time=5),
            MenuItem(name='巧克力熔岩蛋糕', description='温热巧克力熔岩蛋糕', price=52.0, category_id=dessert_cat.id, preparation_time=12),
            MenuItem(name='芝士蛋糕', description='纽约风味芝士蛋糕', price=45.0, category_id=dessert_cat.id, preparation_time=5),
            MenuItem(name='冰淇淋拼盘', description='三球冰淇淋拼盘，多种口味', price=38.0, category_id=dessert_cat.id, preparation_time=3),
        ]
        
        for dish in dishes:
            db.session.add(dish)
        
        db.session.commit()
        print(f"成功添加 {len(dishes)} 道菜品！")
        
        # 检查最终结果
        total_categories = Category.query.count()
        total_dishes = MenuItem.query.count()
        print(f"最终结果: {total_categories} 个分类, {total_dishes} 道菜品")

if __name__ == '__main__':
    reset_database()
