#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建真实的排班表
"""

from app import app, db
from models import User, Schedule
from datetime import datetime, date, timedelta
import random

def create_realistic_schedule():
    with app.app_context():
        # 删除现有排班
        Schedule.query.delete()

        # 获取所有员工
        staff_members = User.query.filter(User.role != 'admin').all()

        if not staff_members:
            print("没有找到员工，请先添加员工数据")
            return

        # 按角色分组员工
        managers = [s for s in staff_members if s.role == 'manager']
        waiters = [s for s in staff_members if s.role == 'waiter']
        chefs = [s for s in staff_members if s.role == 'chef']
        cashiers = [s for s in staff_members if s.role == 'cashier']

        print(f"找到员工: 经理{len(managers)}人, 服务员{len(waiters)}人, 厨师{len(chefs)}人, 收银员{len(cashiers)}人")

        # 生成未来30天的排班
        start_date = date.today()
        schedules = []

        for day_offset in range(30):
            current_date = start_date + timedelta(days=day_offset)
            weekday = current_date.weekday()  # 0=周一, 6=周日

            # 周末和工作日的不同安排
            is_weekend = weekday >= 5

            # 早班 (9:00-17:00)
            morning_shift_start = datetime.combine(current_date, datetime.min.time().replace(hour=9))
            morning_shift_end = datetime.combine(current_date, datetime.min.time().replace(hour=17))

            # 晚班 (17:00-23:00)
            evening_shift_start = datetime.combine(current_date, datetime.min.time().replace(hour=17))
            evening_shift_end = datetime.combine(current_date, datetime.min.time().replace(hour=23))

            # 全天班 (9:00-23:00) - 主要用于经理
            full_shift_start = datetime.combine(current_date, datetime.min.time().replace(hour=9))
            full_shift_end = datetime.combine(current_date, datetime.min.time().replace(hour=23))

            # 经理排班 - 每天至少有一个经理
            if managers:
                if is_weekend:
                    # 周末两个经理轮班
                    if len(managers) >= 2:
                        schedules.append(Schedule(
                            employee_id=managers[day_offset % len(managers)].id,
                            date=current_date,
                            shift_start=morning_shift_start.time(),
                            shift_end=evening_shift_start.time(),
                            position='经理-早班'
                        ))
                        schedules.append(Schedule(
                            user_id=managers[(day_offset + 1) % len(managers)].id,
                            shift_date=current_date,
                            start_time=evening_shift_start,
                            end_time=evening_shift_end,
                            shift_type='晚班'
                        ))
                    else:
                        schedules.append(Schedule(
                            user_id=managers[0].id,
                            shift_date=current_date,
                            start_time=full_shift_start,
                            end_time=full_shift_end,
                            shift_type='全天'
                        ))
                else:
                    # 工作日一个经理全天
                    schedules.append(Schedule(
                        user_id=managers[day_offset % len(managers)].id,
                        shift_date=current_date,
                        start_time=full_shift_start,
                        end_time=full_shift_end,
                        shift_type='全天'
                    ))

            # 服务员排班
            if waiters:
                waiter_count = 4 if is_weekend else 3  # 周末多安排一个服务员

                # 早班服务员
                morning_waiters = min(2, len(waiters))
                for i in range(morning_waiters):
                    waiter_index = (day_offset * 2 + i) % len(waiters)
                    schedules.append(Schedule(
                        user_id=waiters[waiter_index].id,
                        shift_date=current_date,
                        start_time=morning_shift_start,
                        end_time=morning_shift_end,
                        shift_type='早班'
                    ))

                # 晚班服务员
                evening_waiters = min(waiter_count - morning_waiters, len(waiters))
                for i in range(evening_waiters):
                    waiter_index = (day_offset * 2 + morning_waiters + i) % len(waiters)
                    # 避免同一个服务员早晚班都上
                    if waiters[waiter_index].id not in [s.user_id for s in schedules if s.shift_date == current_date and s.shift_type == '早班']:
                        schedules.append(Schedule(
                            user_id=waiters[waiter_index].id,
                            shift_date=current_date,
                            start_time=evening_shift_start,
                            end_time=evening_shift_end,
                            shift_type='晚班'
                        ))

            # 厨师排班
            if chefs:
                chef_count = 3 if is_weekend else 2  # 周末多安排一个厨师

                # 主厨通常全天
                if len(chefs) > 0:
                    schedules.append(Schedule(
                        user_id=chefs[0].id,  # 假设第一个是主厨
                        shift_date=current_date,
                        start_time=full_shift_start,
                        end_time=full_shift_end,
                        shift_type='全天'
                    ))

                # 其他厨师分早晚班
                if len(chefs) > 1:
                    for i in range(1, min(chef_count, len(chefs))):
                        chef_index = (day_offset + i) % len(chefs)
                        if chef_index != 0:  # 不是主厨
                            shift_type = '早班' if i % 2 == 1 else '晚班'
                            start_time = morning_shift_start if shift_type == '早班' else evening_shift_start
                            end_time = morning_shift_end if shift_type == '早班' else evening_shift_end

                            schedules.append(Schedule(
                                user_id=chefs[chef_index].id,
                                shift_date=current_date,
                                start_time=start_time,
                                end_time=end_time,
                                shift_type=shift_type
                            ))

            # 收银员排班
            if cashiers:
                # 每天至少一个收银员
                cashier_count = 2 if is_weekend else 1

                for i in range(min(cashier_count, len(cashiers))):
                    cashier_index = (day_offset + i) % len(cashiers)
                    if cashier_count == 1:
                        # 工作日全天
                        schedules.append(Schedule(
                            user_id=cashiers[cashier_index].id,
                            shift_date=current_date,
                            start_time=full_shift_start,
                            end_time=full_shift_end,
                            shift_type='全天'
                        ))
                    else:
                        # 周末分早晚班
                        shift_type = '早班' if i == 0 else '晚班'
                        start_time = morning_shift_start if shift_type == '早班' else evening_shift_start
                        end_time = morning_shift_end if shift_type == '早班' else evening_shift_end

                        schedules.append(Schedule(
                            user_id=cashiers[cashier_index].id,
                            shift_date=current_date,
                            start_time=start_time,
                            end_time=end_time,
                            shift_type=shift_type
                        ))

        # 添加所有排班到数据库
        for schedule in schedules:
            db.session.add(schedule)

        db.session.commit()
        print(f"成功创建 {len(schedules)} 条排班记录！")

        # 统计信息
        total_days = 30
        total_shifts = len(schedules)
        avg_shifts_per_day = total_shifts / total_days

        print(f"排班统计:")
        print(f"- 总天数: {total_days} 天")
        print(f"- 总班次: {total_shifts} 个")
        print(f"- 平均每天班次: {avg_shifts_per_day:.1f} 个")

        # 按角色统计
        role_stats = {}
        for schedule in schedules:
            user = User.query.get(schedule.user_id)
            if user.role not in role_stats:
                role_stats[user.role] = 0
            role_stats[user.role] += 1

        print("按角色统计班次:")
        for role, count in role_stats.items():
            print(f"- {role}: {count} 个班次")

if __name__ == '__main__':
    create_realistic_schedule()
