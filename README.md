# 牛排餐厅管理系统

基于Flask框架开发的完整餐厅管理系统，包含库存管理、POS收银、预订管理、员工管理和客户关系管理等功能模块。

## 功能特性

### 1. 库存管理系统
- ✅ 原料库存实时监控
- ✅ 自动库存预警（低库存/紧急库存）
- ✅ 库存入库/出库管理
- ✅ 菜品管理（添加、编辑、启用/禁用）
- ✅ 供应商信息管理

### 2. POS收银系统
- ✅ 桌位状态管理
- ✅ 订单创建和管理
- ✅ 菜品选择和定制
- ✅ 多种支付方式支持
- ✅ 订单历史查询

### 3. 预订与桌位管理系统
- ✅ 在线预订功能
- ✅ 智能桌位分配
- ✅ 预订时间冲突检测
- ✅ 预订状态管理
- ✅ 客户预订历史

### 4. 员工排班与考勤系统
- ✅ 员工信息管理
- ✅ 排班计划制定
- ✅ 打卡考勤功能
- ✅ 工时统计和加班计算
- ✅ 考勤报表生成

### 5. 客户关系管理系统
- ✅ 客户信息管理
- ✅ 消费历史记录
- ✅ 积分系统
- ✅ 生日提醒
- ✅ 客户分类管理（VIP、普通、新客户）

## 技术栈

- **后端框架**: Flask 2.2.5
- **数据库**: SQLAlchemy 1.4.48 + SQLite
- **用户认证**: Flask-Login
- **表单处理**: Flask-WTF + WTForms
- **数据库迁移**: Flask-Migrate
- **前端框架**: Bootstrap 5.1.3
- **图标库**: Font Awesome 6.0
- **图表库**: Chart.js
- **JavaScript库**: jQuery 3.6.0

## 安装和运行

### 1. 克隆项目
```bash
git clone <repository-url>
cd FlaskProject
```

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 运行应用
```bash
python app.py
```

### 4. 访问系统
打开浏览器访问: http://127.0.0.1:5000

## 默认账户

系统初始化时会自动创建管理员账户：

- **用户名**: admin
- **密码**: admin123
- **角色**: 管理员

## 项目结构

```
FlaskProject/
├── app.py                 # 主应用文件
├── config.py             # 配置文件
├── models.py             # 数据库模型
├── requirements.txt      # 依赖包列表
├── routes/              # 路由模块
│   ├── __init__.py
│   ├── auth.py          # 认证路由
│   ├── main.py          # 主页路由
│   ├── inventory.py     # 库存管理路由
│   ├── pos.py           # POS收银路由
│   ├── reservation.py   # 预订管理路由
│   ├── staff.py         # 员工管理路由
│   └── customer.py      # 客户管理路由
├── templates/           # HTML模板
│   ├── base.html        # 基础模板
│   ├── index.html       # 首页模板
│   ├── dashboard.html   # 仪表板模板
│   ├── auth/           # 认证相关模板
│   ├── pos/            # POS相关模板
│   ├── inventory/      # 库存相关模板
│   ├── reservation/    # 预订相关模板
│   ├── staff/          # 员工相关模板
│   └── customer/       # 客户相关模板
└── static/             # 静态文件
```

## 主要功能模块

### 仪表板
- 今日营业数据统计
- 收入趋势图表
- 热门菜品排行
- 库存预警提醒
- 快速操作入口

### 库存管理
- 原料库存监控
- 库存预警系统
- 入库出库操作
- 菜品管理
- 库存报表

### POS收银
- 桌位状态显示
- 订单创建管理
- 菜品选择下单
- 结账支付处理
- 订单历史查询

### 预订管理
- 客户在线预订
- 桌位智能分配
- 预订状态管理
- 预订冲突检测
- 预订确认通知

### 员工管理
- 员工信息维护
- 排班计划制定
- 考勤打卡功能
- 工时统计报表
- 权限角色管理

### 客户管理
- 客户档案管理
- 消费历史记录
- 积分系统管理
- 生日提醒功能
- 客户分析报告

## 数据库设计

系统包含以下主要数据表：

- `user` - 用户/员工信息
- `customer` - 客户信息
- `category` - 菜品分类
- `menu_item` - 菜品信息
- `ingredient` - 原料库存
- `table` - 桌位信息
- `reservation` - 预订记录
- `order` - 订单信息
- `order_item` - 订单项目
- `attendance` - 考勤记录
- `schedule` - 排班计划

## 开发说明

### 添加新功能
1. 在 `models.py` 中定义数据模型
2. 在 `routes/` 目录下创建对应的路由文件
3. 在 `templates/` 目录下创建对应的模板文件
4. 在 `app.py` 中注册新的蓝图

### 数据库迁移
```bash
flask db init
flask db migrate -m "描述信息"
flask db upgrade
```

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

## 联系方式

如有问题或建议，请联系开发团队。
