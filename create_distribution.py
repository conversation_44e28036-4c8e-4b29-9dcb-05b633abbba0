#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建分发包
"""

import os
import shutil
import zipfile
from pathlib import Path

def create_distribution():
    """创建分发包"""
    
    print("📦 创建分发包...")
    
    portable_dir = Path("SteakhouseManager_Portable")
    if not portable_dir.exists():
        print("❌ 便携版目录不存在，请先运行 build_portable.py")
        return False
    
    # 创建ZIP压缩包
    zip_filename = "牛排餐厅管理系统_便携版.zip"
    
    with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for file_path in portable_dir.rglob('*'):
            if file_path.is_file():
                # 计算相对路径
                arcname = file_path.relative_to(portable_dir.parent)
                zipf.write(file_path, arcname)
                print(f"  添加文件: {arcname}")
    
    # 获取文件大小
    zip_size = os.path.getsize(zip_filename) / (1024 * 1024)  # MB
    
    print(f"""
🎉 分发包创建完成！

📁 文件名: {zip_filename}
📊 文件大小: {zip_size:.1f} MB
📍 位置: {Path(zip_filename).absolute()}

📋 包含内容:
   - SteakhouseManager.exe (50MB) - 主程序
   - README.md - 详细使用说明
   - 启动系统.bat - 快速启动脚本

🚀 分发说明:
   1. 将ZIP文件发送给用户
   2. 用户解压到任意目录
   3. 双击 SteakhouseManager.exe 或 启动系统.bat 即可运行
   4. 无需安装Python或任何依赖

💡 系统要求:
   - Windows 7/8/10/11 (64位)
   - 至少 100MB 可用磁盘空间
   - 至少 512MB 可用内存

🔐 默认登录:
   - 管理员: admin / admin123
   - 总经理: general_manager / gm123456
   - 行政总厨: executive_chef / chef123456
""")
    
    return True

if __name__ == '__main__':
    create_distribution()
