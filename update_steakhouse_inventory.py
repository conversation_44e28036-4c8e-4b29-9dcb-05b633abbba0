#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新牛排餐厅库存管理 - 与西餐菜品匹配
"""

from app import app, db
from models import Ingredient

def update_steakhouse_inventory():
    with app.app_context():
        # 清除现有库存
        Ingredient.query.delete()

        # 定义西餐牛排餐厅库存
        inventory_items = [
            # 牛肉类 - 主要原料
            {'name': '澳洲菲力牛排', 'category': '牛肉类', 'unit': 'kg', 'current_stock': 25, 'min_stock': 10, 'max_stock': 50, 'unit_price': 180.00},
            {'name': '澳洲西冷牛排', 'category': '牛肉类', 'unit': 'kg', 'current_stock': 30, 'min_stock': 15, 'max_stock': 60, 'unit_price': 150.00},
            {'name': '澳洲肋眼牛排', 'category': '牛肉类', 'unit': 'kg', 'current_stock': 28, 'min_stock': 12, 'max_stock': 55, 'unit_price': 160.00},
            {'name': '美国T骨牛排', 'category': '牛肉类', 'unit': 'kg', 'current_stock': 20, 'min_stock': 8, 'max_stock': 40, 'unit_price': 200.00},
            {'name': '美国纽约客牛排', 'category': '牛肉类', 'unit': 'kg', 'current_stock': 22, 'min_stock': 10, 'max_stock': 45, 'unit_price': 170.00},
            {'name': '日本A5和牛', 'category': '牛肉类', 'unit': 'kg', 'current_stock': 5, 'min_stock': 2, 'max_stock': 10, 'unit_price': 800.00},
            {'name': '澳洲M9和牛', 'category': '牛肉类', 'unit': 'kg', 'current_stock': 8, 'min_stock': 3, 'max_stock': 15, 'unit_price': 450.00},
            {'name': '美国Prime牛肉', 'category': '牛肉类', 'unit': 'kg', 'current_stock': 15, 'min_stock': 5, 'max_stock': 30, 'unit_price': 220.00},
            {'name': '战斧牛排', 'category': '牛肉类', 'unit': '块', 'current_stock': 12, 'min_stock': 5, 'max_stock': 20, 'unit_price': 350.00},
            {'name': '牛尾', 'category': '牛肉类', 'unit': 'kg', 'current_stock': 10, 'min_stock': 5, 'max_stock': 20, 'unit_price': 80.00},

            # 海鲜类 - 高端配菜
            {'name': '波士顿龙虾', 'category': '海鲜类', 'unit': '只', 'current_stock': 15, 'min_stock': 8, 'max_stock': 25, 'unit_price': 120.00},
            {'name': '阿拉斯加帝王蟹', 'category': '海鲜类', 'unit': 'kg', 'current_stock': 8, 'min_stock': 3, 'max_stock': 15, 'unit_price': 180.00},
            {'name': '挪威三文鱼', 'category': '海鲜类', 'unit': 'kg', 'current_stock': 12, 'min_stock': 5, 'max_stock': 20, 'unit_price': 85.00},
            {'name': '新鲜扇贝', 'category': '海鲜类', 'unit': 'kg', 'current_stock': 6, 'min_stock': 3, 'max_stock': 12, 'unit_price': 95.00},
            {'name': '阿拉斯加银鳕鱼', 'category': '海鲜类', 'unit': 'kg', 'current_stock': 10, 'min_stock': 4, 'max_stock': 18, 'unit_price': 110.00},

            # 高端食材 - 奢华原料
            {'name': '法国鹅肝', 'category': '高端食材', 'unit': 'kg', 'current_stock': 2, 'min_stock': 1, 'max_stock': 5, 'unit_price': 600.00},
            {'name': '俄罗斯鱼子酱', 'category': '高端食材', 'unit': 'g', 'current_stock': 200, 'min_stock': 50, 'max_stock': 500, 'unit_price': 8.00},
            {'name': '意大利帕尔马火腿', 'category': '高端食材', 'unit': 'kg', 'current_stock': 3, 'min_stock': 1, 'max_stock': 6, 'unit_price': 280.00},
            {'name': '黑松露', 'category': '高端食材', 'unit': 'g', 'current_stock': 150, 'min_stock': 50, 'max_stock': 300, 'unit_price': 12.00},
            {'name': '白松露', 'category': '高端食材', 'unit': 'g', 'current_stock': 80, 'min_stock': 30, 'max_stock': 150, 'unit_price': 20.00},

            # 蔬菜类 - 新鲜配菜
            {'name': '新鲜芦笋', 'category': '蔬菜类', 'unit': 'kg', 'current_stock': 8, 'min_stock': 3, 'max_stock': 15, 'unit_price': 25.00},
            {'name': '有机菠菜', 'category': '蔬菜类', 'unit': 'kg', 'current_stock': 5, 'min_stock': 2, 'max_stock': 10, 'unit_price': 15.00},
            {'name': '新鲜蘑菇', 'category': '蔬菜类', 'unit': 'kg', 'current_stock': 10, 'min_stock': 4, 'max_stock': 20, 'unit_price': 18.00},
            {'name': '土豆', 'category': '蔬菜类', 'unit': 'kg', 'current_stock': 25, 'min_stock': 10, 'max_stock': 50, 'unit_price': 8.00},
            {'name': '洋葱', 'category': '蔬菜类', 'unit': 'kg', 'current_stock': 15, 'min_stock': 5, 'max_stock': 30, 'unit_price': 6.00},
            {'name': '芝麻菜', 'category': '蔬菜类', 'unit': 'kg', 'current_stock': 3, 'min_stock': 1, 'max_stock': 8, 'unit_price': 35.00},
            {'name': '樱桃番茄', 'category': '蔬菜类', 'unit': 'kg', 'current_stock': 6, 'min_stock': 2, 'max_stock': 12, 'unit_price': 20.00},
            {'name': '混合时蔬', 'category': '蔬菜类', 'unit': 'kg', 'current_stock': 12, 'min_stock': 5, 'max_stock': 25, 'unit_price': 22.00},

            # 乳制品类 - 西餐必需
            {'name': '法国黄油', 'category': '乳制品类', 'unit': 'kg', 'current_stock': 8, 'min_stock': 3, 'max_stock': 15, 'unit_price': 45.00},
            {'name': '淡奶油', 'category': '乳制品类', 'unit': 'L', 'current_stock': 12, 'min_stock': 5, 'max_stock': 25, 'unit_price': 28.00},
            {'name': '帕尔马芝士', 'category': '乳制品类', 'unit': 'kg', 'current_stock': 4, 'min_stock': 2, 'max_stock': 8, 'unit_price': 120.00},
            {'name': '马斯卡彭芝士', 'category': '乳制品类', 'unit': 'kg', 'current_stock': 3, 'min_stock': 1, 'max_stock': 6, 'unit_price': 80.00},
            {'name': '新鲜牛奶', 'category': '乳制品类', 'unit': 'L', 'current_stock': 20, 'min_stock': 8, 'max_stock': 40, 'unit_price': 12.00},

            # 调料香料 - 西餐调味
            {'name': '海盐', 'category': '调料香料', 'unit': 'kg', 'current_stock': 5, 'min_stock': 2, 'max_stock': 10, 'unit_price': 25.00},
            {'name': '黑胡椒粒', 'category': '调料香料', 'unit': 'kg', 'current_stock': 2, 'min_stock': 1, 'max_stock': 5, 'unit_price': 80.00},
            {'name': '迷迭香', 'category': '调料香料', 'unit': 'kg', 'current_stock': 1, 'min_stock': 0.5, 'max_stock': 3, 'unit_price': 120.00},
            {'name': '百里香', 'category': '调料香料', 'unit': 'kg', 'current_stock': 1, 'min_stock': 0.5, 'max_stock': 3, 'unit_price': 100.00},
            {'name': '香草精', 'category': '调料香料', 'unit': 'L', 'current_stock': 2, 'min_stock': 1, 'max_stock': 5, 'unit_price': 150.00},
            {'name': '橄榄油', 'category': '调料香料', 'unit': 'L', 'current_stock': 10, 'min_stock': 4, 'max_stock': 20, 'unit_price': 85.00},
            {'name': '意式醋汁', 'category': '调料香料', 'unit': 'L', 'current_stock': 3, 'min_stock': 1, 'max_stock': 8, 'unit_price': 60.00},

            # 面包类 - 西餐主食
            {'name': '法式面包', 'category': '面包类', 'unit': '个', 'current_stock': 20, 'min_stock': 8, 'max_stock': 40, 'unit_price': 12.00},
            {'name': '吐司面包', 'category': '面包类', 'unit': '个', 'current_stock': 15, 'min_stock': 6, 'max_stock': 30, 'unit_price': 8.00},
            {'name': '意式面包', 'category': '面包类', 'unit': '个', 'current_stock': 12, 'min_stock': 5, 'max_stock': 25, 'unit_price': 15.00},

            # 酒水类 - 精选酒款
            {'name': '法国波尔多红酒', 'category': '酒水类', 'unit': '瓶', 'current_stock': 24, 'min_stock': 10, 'max_stock': 50, 'unit_price': 180.00},
            {'name': '意大利基安帝红酒', 'category': '酒水类', 'unit': '瓶', 'current_stock': 18, 'min_stock': 8, 'max_stock': 35, 'unit_price': 130.00},
            {'name': '澳洲设拉子红酒', 'category': '酒水类', 'unit': '瓶', 'current_stock': 20, 'min_stock': 8, 'max_stock': 40, 'unit_price': 100.00},
            {'name': '智利赤霞珠红酒', 'category': '酒水类', 'unit': '瓶', 'current_stock': 25, 'min_stock': 10, 'max_stock': 50, 'unit_price': 75.00},
            {'name': '法国香槟', 'category': '酒水类', 'unit': '瓶', 'current_stock': 12, 'min_stock': 5, 'max_stock': 25, 'unit_price': 220.00},

            # 咖啡茶叶 - 精品饮品
            {'name': '意式咖啡豆', 'category': '咖啡茶叶', 'unit': 'kg', 'current_stock': 8, 'min_stock': 3, 'max_stock': 15, 'unit_price': 120.00},
            {'name': '英式红茶', 'category': '咖啡茶叶', 'unit': 'kg', 'current_stock': 2, 'min_stock': 1, 'max_stock': 5, 'unit_price': 180.00},
            {'name': '新鲜橙子', 'category': '水果类', 'unit': 'kg', 'current_stock': 15, 'min_stock': 5, 'max_stock': 30, 'unit_price': 18.00},
            {'name': '柠檬', 'category': '水果类', 'unit': 'kg', 'current_stock': 8, 'min_stock': 3, 'max_stock': 15, 'unit_price': 15.00},

            # 甜品原料 - 精致甜品
            {'name': '比利时黑巧克力', 'category': '甜品原料', 'unit': 'kg', 'current_stock': 5, 'min_stock': 2, 'max_stock': 10, 'unit_price': 85.00},
            {'name': '马卡龙粉', 'category': '甜品原料', 'unit': 'kg', 'current_stock': 3, 'min_stock': 1, 'max_stock': 8, 'unit_price': 120.00},
            {'name': '香草冰淇淋', 'category': '甜品原料', 'unit': 'L', 'current_stock': 8, 'min_stock': 3, 'max_stock': 15, 'unit_price': 45.00},
            {'name': '蜂蜜', 'category': '甜品原料', 'unit': 'kg', 'current_stock': 3, 'min_stock': 1, 'max_stock': 8, 'unit_price': 60.00},
        ]

        # 添加库存项目
        added_count = 0
        for item_data in inventory_items:
            ingredient = Ingredient(
                name=item_data['name'],
                unit=item_data['unit'],
                current_stock=item_data['current_stock'],
                min_stock=item_data['min_stock'],
                unit_cost=item_data['unit_price'],
                supplier=f"{item_data['category']}供应商"
            )
            db.session.add(ingredient)
            added_count += 1

        db.session.commit()

        print(f"成功更新 {added_count} 个库存项目！")

        # 统计各分类库存数量
        category_stats = {}
        for item_data in inventory_items:
            category = item_data['category']
            if category not in category_stats:
                category_stats[category] = 0
            category_stats[category] += 1

        print("\n库存分类统计:")
        for category, count in category_stats.items():
            print(f"- {category}: {count} 种原料")

        print(f"\n总库存种类: {sum(category_stats.values())} 种")
        print("库存策略: 与西餐菜品完全匹配，支持高端牛排餐厅运营")

if __name__ == '__main__':
    update_steakhouse_inventory()
