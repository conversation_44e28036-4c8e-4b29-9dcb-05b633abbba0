#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进排班系统 - 确保周一到周四合理排班
"""

from app import app, db
from models import User, Schedule
from datetime import datetime, date, timedelta, time

def improve_schedule_system():
    with app.app_context():
        # 删除现有排班
        Schedule.query.delete()
        
        # 获取所有员工
        all_staff = User.query.filter(User.role != 'admin').all()
        
        if not all_staff:
            print("没有找到员工，请先添加员工数据")
            return
        
        # 按职位分组员工
        managers = [s for s in all_staff if s.role == 'manager']
        chefs = [s for s in all_staff if s.role == 'chef']
        waiters = [s for s in all_staff if s.role == 'waiter']
        cashiers = [s for s in all_staff if s.role == 'cashier']
        support_staff = [s for s in all_staff if s.role == 'employee']
        
        print(f"员工统计: 管理{len(managers)}人, 厨师{len(chefs)}人, 服务员{len(waiters)}人, 收银/吧台{len(cashiers)}人, 后勤{len(support_staff)}人")
        
        # 生成未来30天的排班
        start_date = date.today()
        schedules = []
        
        for day_offset in range(30):
            current_date = start_date + timedelta(days=day_offset)
            weekday = current_date.weekday()  # 0=周一, 6=周日
            is_weekend = weekday >= 5
            
            print(f"安排 {current_date} (周{'一二三四五六日'[weekday]}) 的排班...")
            
            # 管理层排班 - 每天都有管理人员
            if managers:
                if weekday < 5:  # 周一到周五
                    # 总经理 - 工作日上班
                    schedules.append(Schedule(
                        employee_id=managers[0].id,
                        date=current_date,
                        shift_start=time(9, 0),
                        shift_end=time(18, 0),
                        position='总经理-日班'
                    ))
                    
                    # 副总经理或楼面经理 - 晚班监督
                    manager_index = (day_offset % (len(managers) - 1)) + 1
                    schedules.append(Schedule(
                        employee_id=managers[manager_index].id,
                        date=current_date,
                        shift_start=time(16, 0),
                        shift_end=time(23, 30),
                        position='值班经理-晚班'
                    ))
                else:  # 周末
                    # 周末值班经理
                    manager_index = day_offset % len(managers)
                    schedules.append(Schedule(
                        employee_id=managers[manager_index].id,
                        date=current_date,
                        shift_start=time(10, 0),
                        shift_end=time(23, 30),
                        position='值班经理-全天'
                    ))
            
            # 厨房团队排班 - 每天都营业
            if chefs:
                # 行政总厨 - 每天监督
                schedules.append(Schedule(
                    employee_id=chefs[0].id,
                    date=current_date,
                    shift_start=time(8, 0),
                    shift_end=time(20, 0),
                    position='行政总厨-全天'
                ))
                
                # 副主厨 - 晚班主力
                if len(chefs) > 1:
                    schedules.append(Schedule(
                        employee_id=chefs[1].id,
                        date=current_date,
                        shift_start=time(14, 0),
                        shift_end=time(23, 0),
                        position='副主厨-晚班'
                    ))
                
                # 烤肉主厨 - 晚餐时段
                if len(chefs) > 2:
                    schedules.append(Schedule(
                        employee_id=chefs[2].id,
                        date=current_date,
                        shift_start=time(16, 0),
                        shift_end=time(23, 0),
                        position='烤肉主厨-晚班'
                    ))
                
                # 甜品主厨 - 下午到晚上
                if len(chefs) > 3:
                    schedules.append(Schedule(
                        employee_id=chefs[3].id,
                        date=current_date,
                        shift_start=time(13, 0),
                        shift_end=time(22, 0),
                        position='甜品主厨-下午晚班'
                    ))
                
                # 配菜厨师 - 轮班确保每天有人
                for i in range(4, len(chefs)):
                    chef_index = i
                    # 根据日期和厨师编号决定班次
                    shift_pattern = (day_offset + i) % 3
                    
                    if shift_pattern == 0:  # 早班
                        schedules.append(Schedule(
                            employee_id=chefs[chef_index].id,
                            date=current_date,
                            shift_start=time(8, 0),
                            shift_end=time(15, 0),
                            position='配菜厨师-早班'
                        ))
                    elif shift_pattern == 1:  # 晚班
                        schedules.append(Schedule(
                            employee_id=chefs[chef_index].id,
                            date=current_date,
                            shift_start=time(16, 0),
                            shift_end=time(23, 0),
                            position='配菜厨师-晚班'
                        ))
                    # shift_pattern == 2 休息
            
            # 前厅服务团队排班 - 根据客流调整
            if waiters:
                # 确定当天需要的服务员数量
                if is_weekend:
                    needed_waiters = 6  # 周末客流多
                elif weekday in [0, 1, 2, 3]:  # 周一到周四
                    needed_waiters = 4  # 工作日客流适中
                else:  # 周五
                    needed_waiters = 5  # 周五客流较多
                
                # 首席服务员 - 每天晚班
                schedules.append(Schedule(
                    employee_id=waiters[0].id,
                    date=current_date,
                    shift_start=time(16, 0),
                    shift_end=time(23, 30),
                    position='首席服务员-晚班'
                ))
                
                # 侍酒师 - 晚餐时段
                if len(waiters) > 1:
                    schedules.append(Schedule(
                        employee_id=waiters[1].id,
                        date=current_date,
                        shift_start=time(17, 30),
                        shift_end=time(22, 30),
                        position='侍酒师-晚班'
                    ))
                
                # 其他服务员轮班
                waiter_schedule_count = 0
                for i in range(2, len(waiters)):
                    if waiter_schedule_count >= needed_waiters - 2:  # 减去首席和侍酒师
                        break
                    
                    waiter_index = i
                    shift_pattern = (day_offset + i) % 4
                    
                    if shift_pattern == 0:  # 午班
                        schedules.append(Schedule(
                            employee_id=waiters[waiter_index].id,
                            date=current_date,
                            shift_start=time(10, 30),
                            shift_end=time(16, 0),
                            position='服务员-午班'
                        ))
                        waiter_schedule_count += 1
                    elif shift_pattern == 1:  # 晚班
                        schedules.append(Schedule(
                            employee_id=waiters[waiter_index].id,
                            date=current_date,
                            shift_start=time(16, 30),
                            shift_end=time(23, 0),
                            position='服务员-晚班'
                        ))
                        waiter_schedule_count += 1
                    elif shift_pattern == 2 and is_weekend:  # 周末加班
                        schedules.append(Schedule(
                            employee_id=waiters[waiter_index].id,
                            date=current_date,
                            shift_start=time(11, 0),
                            shift_end=time(18, 0),
                            position='服务员-全天'
                        ))
                        waiter_schedule_count += 1
                    # shift_pattern == 3 休息
            
            # 吧台/收银团队排班 - 每天都需要
            if cashiers:
                # 首席调酒师 - 每天晚班
                schedules.append(Schedule(
                    employee_id=cashiers[0].id,
                    date=current_date,
                    shift_start=time(17, 0),
                    shift_end=time(23, 30),
                    position='首席调酒师-晚班'
                ))
                
                # 调酒师 - 周末或繁忙时段
                if len(cashiers) > 1 and (is_weekend or weekday == 4):  # 周五到周日
                    schedules.append(Schedule(
                        employee_id=cashiers[1].id,
                        date=current_date,
                        shift_start=time(19, 0),
                        shift_end=time(23, 0),
                        position='调酒师-晚班'
                    ))
                
                # 收银主管 - 每天全天
                if len(cashiers) > 2:
                    schedules.append(Schedule(
                        employee_id=cashiers[2].id,
                        date=current_date,
                        shift_start=time(11, 0),
                        shift_end=time(22, 0),
                        position='收银主管-全天'
                    ))
                
                # 收银员 - 轮班
                for i in range(3, len(cashiers)):
                    cashier_index = i
                    if (day_offset + i) % 2 == 0:  # 午班
                        schedules.append(Schedule(
                            employee_id=cashiers[cashier_index].id,
                            date=current_date,
                            shift_start=time(11, 0),
                            shift_end=time(17, 0),
                            position='收银员-午班'
                        ))
                    else:  # 晚班
                        schedules.append(Schedule(
                            employee_id=cashiers[cashier_index].id,
                            date=current_date,
                            shift_start=time(17, 0),
                            shift_end=time(23, 0),
                            position='收银员-晚班'
                        ))
            
            # 后勤支持团队排班 - 每天都需要
            if support_staff:
                for i, staff in enumerate(support_staff):
                    if 'cleaner' in staff.username:  # 清洁员
                        if i % 2 == 0:  # 早班清洁
                            schedules.append(Schedule(
                                employee_id=staff.id,
                                date=current_date,
                                shift_start=time(7, 0),
                                shift_end=time(12, 0),
                                position='清洁员-早班'
                            ))
                        else:  # 晚班清洁
                            schedules.append(Schedule(
                                employee_id=staff.id,
                                date=current_date,
                                shift_start=time(23, 30),
                                shift_end=time(23, 59),
                                position='清洁员-夜班'
                            ))
                    elif 'security' in staff.username:  # 保安
                        schedules.append(Schedule(
                            employee_id=staff.id,
                            date=current_date,
                            shift_start=time(18, 0),
                            shift_end=time(23, 59),
                            position='保安-夜班'
                        ))
        
        # 添加所有排班到数据库
        for schedule in schedules:
            db.session.add(schedule)
        
        db.session.commit()
        print(f"成功创建 {len(schedules)} 条改进排班记录！")
        
        # 按星期统计排班
        weekday_stats = {}
        for schedule in schedules:
            weekday = schedule.date.weekday()
            weekday_name = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'][weekday]
            if weekday_name not in weekday_stats:
                weekday_stats[weekday_name] = 0
            weekday_stats[weekday_name] += 1
        
        print("\n按星期统计排班:")
        for day, count in weekday_stats.items():
            print(f"- {day}: {count} 个班次")
        
        # 统计职位班次
        position_stats = {}
        for schedule in schedules:
            position = schedule.position
            if position not in position_stats:
                position_stats[position] = 0
            position_stats[position] += 1
        
        print("\n按职位统计班次:")
        for position, count in sorted(position_stats.items()):
            print(f"- {position}: {count} 个班次")
        
        print(f"\n排班改进特色:")
        print("- 周一到周四正常营业排班")
        print("- 周末增加人手应对客流")
        print("- 管理层每天都有人值班")
        print("- 厨房团队保证菜品质量")
        print("- 前厅服务根据客流调整")
        print("- 后勤支持全周无休")

if __name__ == '__main__':
    improve_schedule_system()
