#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增加更多桌位
"""

from app import app, db
from models import Table

def add_more_tables():
    with app.app_context():
        # 检查现有桌位数量
        existing_tables = Table.query.count()
        print(f"当前桌位数量: {existing_tables}")
        
        # 定义新增桌位配置
        new_tables = [
            # 2人桌 (适合情侣、商务会谈)
            {'number': 'T01', 'capacity': 2, 'status': 'available'},
            {'number': 'T02', 'capacity': 2, 'status': 'available'},
            {'number': 'T03', 'capacity': 2, 'status': 'available'},
            {'number': 'T04', 'capacity': 2, 'status': 'available'},
            {'number': 'T05', 'capacity': 2, 'status': 'available'},
            {'number': 'T06', 'capacity': 2, 'status': 'available'},
            
            # 4人桌 (适合家庭、朋友聚餐)
            {'number': 'T07', 'capacity': 4, 'status': 'available'},
            {'number': 'T08', 'capacity': 4, 'status': 'available'},
            {'number': 'T09', 'capacity': 4, 'status': 'available'},
            {'number': 'T10', 'capacity': 4, 'status': 'available'},
            {'number': 'T11', 'capacity': 4, 'status': 'available'},
            {'number': 'T12', 'capacity': 4, 'status': 'available'},
            {'number': 'T13', 'capacity': 4, 'status': 'available'},
            {'number': 'T14', 'capacity': 4, 'status': 'available'},
            
            # 6人桌 (适合商务宴请、家庭聚会)
            {'number': 'T15', 'capacity': 6, 'status': 'available'},
            {'number': 'T16', 'capacity': 6, 'status': 'available'},
            {'number': 'T17', 'capacity': 6, 'status': 'available'},
            {'number': 'T18', 'capacity': 6, 'status': 'available'},
            
            # 8人桌 (适合大型聚会、庆祝活动)
            {'number': 'T19', 'capacity': 8, 'status': 'available'},
            {'number': 'T20', 'capacity': 8, 'status': 'available'},
            
            # VIP包间 (私密性强，适合重要客户)
            {'number': 'VIP01', 'capacity': 4, 'status': 'available'},
            {'number': 'VIP02', 'capacity': 6, 'status': 'available'},
            {'number': 'VIP03', 'capacity': 8, 'status': 'available'},
            
            # 吧台座位 (适合单人用餐、快餐)
            {'number': 'BAR01', 'capacity': 1, 'status': 'available'},
            {'number': 'BAR02', 'capacity': 1, 'status': 'available'},
            {'number': 'BAR03', 'capacity': 1, 'status': 'available'},
            {'number': 'BAR04', 'capacity': 1, 'status': 'available'},
            
            # 露台座位 (适合浪漫晚餐、景观用餐)
            {'number': 'OUT01', 'capacity': 2, 'status': 'available'},
            {'number': 'OUT02', 'capacity': 2, 'status': 'available'},
            {'number': 'OUT03', 'capacity': 4, 'status': 'available'},
            {'number': 'OUT04', 'capacity': 4, 'status': 'available'},
        ]
        
        # 清除现有桌位
        Table.query.delete()
        
        # 添加新桌位
        added_count = 0
        for table_data in new_tables:
            table = Table(
                number=table_data['number'],
                capacity=table_data['capacity'],
                status=table_data['status']
            )
            db.session.add(table)
            added_count += 1
        
        db.session.commit()
        
        print(f"成功添加 {added_count} 个桌位！")
        
        # 统计桌位类型
        table_stats = {}
        for table_data in new_tables:
            capacity = table_data['capacity']
            if capacity not in table_stats:
                table_stats[capacity] = 0
            table_stats[capacity] += 1
        
        print("\n桌位配置统计:")
        for capacity, count in sorted(table_stats.items()):
            if capacity == 1:
                print(f"- 吧台座位: {count} 个")
            else:
                print(f"- {capacity}人桌: {count} 个")
        
        print(f"\n总桌位数: {sum(table_stats.values())} 个")
        print("桌位类型包括: 普通桌位、VIP包间、吧台座位、露台座位")

if __name__ == '__main__':
    add_more_tables()
