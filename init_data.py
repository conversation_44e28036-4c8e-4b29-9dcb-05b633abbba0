#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
便携版数据初始化
"""

import os
import shutil
from pathlib import Path

def init_portable_data():
    """初始化便携版数据"""
    
    # 创建data目录
    data_dir = Path("data")
    data_dir.mkdir(exist_ok=True)
    
    # 复制现有数据库
    source_db = Path("instance/restaurant.db")
    target_db = data_dir / "restaurant.db"
    
    if source_db.exists() and not target_db.exists():
        shutil.copy2(source_db, target_db)
        print(f"✅ 数据库已复制到: {target_db}")
    elif target_db.exists():
        print(f"✅ 数据库已存在: {target_db}")
    else:
        print("⚠️ 源数据库不存在，将创建新数据库")

if __name__ == '__main__':
    init_portable_data()
