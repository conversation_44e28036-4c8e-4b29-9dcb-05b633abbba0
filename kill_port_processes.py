#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
终止占用端口的进程
"""

import subprocess
import sys
import os

def kill_processes_on_port(port=5000):
    """终止占用指定端口的进程"""
    try:
        if os.name == 'nt':  # Windows
            # 查找占用端口的进程
            result = subprocess.run(['netstat', '-ano'], capture_output=True, text=True)
            lines = result.stdout.split('\n')
            
            pids_to_kill = []
            for line in lines:
                if f':{port}' in line and 'LISTENING' in line:
                    parts = line.split()
                    if len(parts) >= 5:
                        pid = parts[-1]
                        if pid.isdigit():
                            pids_to_kill.append(pid)
            
            # 终止进程
            for pid in pids_to_kill:
                try:
                    subprocess.run(['taskkill', '/F', '/PID', pid], check=True)
                    print(f"已终止进程 PID: {pid}")
                except subprocess.CalledProcessError:
                    print(f"无法终止进程 PID: {pid}")
                    
        else:  # Unix/Linux/Mac
            # 查找占用端口的进程
            result = subprocess.run(['lsof', '-ti', f':{port}'], capture_output=True, text=True)
            pids = result.stdout.strip().split('\n')
            
            for pid in pids:
                if pid.isdigit():
                    try:
                        subprocess.run(['kill', '-9', pid], check=True)
                        print(f"已终止进程 PID: {pid}")
                    except subprocess.CalledProcessError:
                        print(f"无法终止进程 PID: {pid}")
        
        print(f"端口 {port} 清理完成")
        
    except Exception as e:
        print(f"清理端口时出错: {e}")

if __name__ == '__main__':
    port = 5000
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print("端口号必须是数字")
            sys.exit(1)
    
    print(f"正在清理端口 {port}...")
    kill_processes_on_port(port)
