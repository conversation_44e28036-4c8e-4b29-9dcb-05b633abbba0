#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
AllSportsNow 便携版构建脚本
一键创建完整的便携版应用程序
"""

import os
import sys
import subprocess
import shutil
import zipfile
from pathlib import Path
import time

class PortableBuilder:
    def __init__(self):
        self.project_dir = Path.cwd()
        self.build_dir = self.project_dir / "build_portable"
        self.dist_dir = self.project_dir / "dist"
        self.output_dir = self.dist_dir / "AllSportsNow"
        
    def print_step(self, step, message):
        """打印构建步骤"""
        print(f"\n{'='*60}")
        print(f"步骤 {step}: {message}")
        print(f"{'='*60}")
        
    def install_dependencies(self):
        """安装必要的依赖"""
        self.print_step(1, "安装构建依赖")
        
        dependencies = [
            "pyinstaller",
            "pillow",
        ]
        
        for dep in dependencies:
            try:
                __import__(dep.replace("-", "_"))
                print(f"✅ {dep} 已安装")
            except ImportError:
                print(f"📦 正在安装 {dep}...")
                subprocess.check_call([sys.executable, "-m", "pip", "install", dep])
                print(f"✅ {dep} 安装完成")
    
    def create_icons(self):
        """创建图标文件"""
        self.print_step(2, "创建应用程序图标")
        
        try:
            subprocess.check_call([sys.executable, "create_icon.py"])
            print("✅ 图标创建完成")
        except Exception as e:
            print(f"⚠️ 图标创建失败: {e}")
            print("将使用默认图标")
    
    def create_spec_file(self):
        """创建PyInstaller规格文件"""
        self.print_step(3, "创建构建规格文件")
        
        spec_content = '''# -*- mode: python ; coding: utf-8 -*-

import os
from pathlib import Path

block_cipher = None

# 数据文件收集
datas = [
    ('templates', 'templates'),
    ('static', 'static'),
    ('languages.py', '.'),
]

# 隐藏导入
hiddenimports = [
    'flask',
    'werkzeug',
    'jinja2',
    'markupsafe',
    'itsdangerous',
    'click',
    'blinker',
    'importlib_metadata',
    'zipp',
    'email.mime.text',
    'email.mime.multipart',
    'email.mime.base',
    'urllib.parse',
    'urllib.request',
    'json',
    'datetime',
    'threading',
    'webbrowser',
    'socket',
]

a = Analysis(
    ['portable_app.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'matplotlib',
        'numpy',
        'pandas',
        'scipy',
        'PyQt5',
        'PyQt6',
        'PySide2',
        'PySide6',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='AllSportsNow',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='static/images/allsportsnow-logo.ico' if os.path.exists('static/images/allsportsnow-logo.ico') else None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='AllSportsNow',
)
'''
        
        with open('AllSportsNow.spec', 'w', encoding='utf-8') as f:
            f.write(spec_content)
        
        print("✅ 规格文件创建完成: AllSportsNow.spec")
    
    def build_executable(self):
        """构建可执行文件"""
        self.print_step(4, "构建可执行文件")
        
        print("⏳ 正在构建，这可能需要几分钟...")
        
        try:
            cmd = [
                sys.executable, "-m", "PyInstaller",
                "--clean",
                "--noconfirm",
                "AllSportsNow.spec"
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ 可执行文件构建成功")
                return True
            else:
                print("❌ 构建失败:")
                print(result.stderr)
                return False
                
        except Exception as e:
            print(f"❌ 构建过程出错: {e}")
            return False
    
    def create_additional_files(self):
        """创建附加文件"""
        self.print_step(5, "创建附加文件")
        
        if not self.output_dir.exists():
            print("❌ 输出目录不存在")
            return False
        
        # 创建README文件
        readme_content = '''# AllSportsNow 便携版

## 🎯 关于
AllSportsNow 是一个现代化的体育赛事信息平台，提供全球体育赛事的最新信息和详细数据。

## 🚀 快速开始
1. 双击 `AllSportsNow.exe` 启动应用
2. 浏览器会自动打开并显示网站
3. 如果浏览器没有自动打开，请手动访问显示的地址

## ✨ 功能特色
- 🏆 全球体育赛事信息
- 🌍 中英文双语支持  
- 📱 响应式设计
- 🔍 智能搜索功能
- 📅 详细赛程安排
- 🏟️ 场馆信息
- 📊 历史数据

## 🎮 支持的体育项目
- ⚽ 足球 (FIFA世界杯、欧冠、英超、西甲、意甲)
- 🏀 篮球 (NBA、欧洲篮球联赛)
- 🎾 网球 (四大满贯)
- 🏊 游泳 (世界锦标赛、奥运会)
- 🏃 田径 (世界锦标赛、钻石联赛)
- ⛳ 高尔夫 (四大满贯)
- 🏎️ 一级方程式

## 💻 系统要求
- Windows 7/8/10/11 (64位)
- 内存: 512MB以上
- 硬盘空间: 200MB以上
- 无需安装Python或其他依赖

## 🛠️ 故障排除
如果遇到问题:
1. 确保端口5000-5009未被占用
2. 检查防火墙设置
3. 以管理员身份运行
4. 查看控制台错误信息

## 📞 技术支持
版本: 1.0.0
构建日期: ''' + time.strftime('%Y-%m-%d') + '''

---
© 2025 AllSportsNow. All rights reserved.
'''
        
        # 创建启动批处理文件
        batch_content = '''@echo off
chcp 65001 >nul
title AllSportsNow - 全球体育赛事平台
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                                                              ║
echo ║     🏆 AllSportsNow - 全球体育赛事信息平台                    ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 正在启动服务器...
echo 浏览器将自动打开...
echo 按 Ctrl+C 停止服务器
echo.

AllSportsNow.exe
if errorlevel 1 (
    echo.
    echo ❌ 启动失败，请检查错误信息
    pause
)
'''
        
        # 创建使用说明
        instructions = '''🎉 AllSportsNow 便携版使用说明

📁 文件说明:
- AllSportsNow.exe     : 主程序 (双击启动)
- start.bat            : 批处理启动文件
- README.md            : 详细说明文档
- _internal/           : 程序依赖文件 (请勿删除)

🚀 启动方法:
方法1: 双击 AllSportsNow.exe
方法2: 双击 start.bat

🌐 访问地址:
程序会自动选择可用端口 (5000-5009)
浏览器会自动打开对应地址

✨ 功能特色:
- 全球体育赛事信息
- 中英文双语支持
- 响应式设计
- 智能搜索
- 详细赛程和场馆信息

❌ 停止服务:
在命令行窗口按 Ctrl+C

📞 技术支持:
如有问题请查看 README.md 文件

版本: 1.0.0
构建日期: ''' + time.strftime('%Y-%m-%d %H:%M:%S') + '''
'''
        
        # 保存文件
        files_to_create = [
            ('README.md', readme_content),
            ('start.bat', batch_content),
            ('使用说明.txt', instructions),
        ]
        
        for filename, content in files_to_create:
            file_path = self.output_dir / filename
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ 创建文件: {filename}")
        
        return True
    
    def create_zip_package(self):
        """创建ZIP压缩包"""
        self.print_step(6, "创建ZIP压缩包")
        
        zip_path = self.dist_dir / f"AllSportsNow_Portable_v1.0.0_{time.strftime('%Y%m%d')}.zip"
        
        try:
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for root, dirs, files in os.walk(self.output_dir):
                    for file in files:
                        file_path = Path(root) / file
                        arc_path = file_path.relative_to(self.output_dir)
                        zipf.write(file_path, arc_path)
            
            print(f"✅ ZIP包创建完成: {zip_path}")
            print(f"📦 文件大小: {zip_path.stat().st_size / 1024 / 1024:.1f} MB")
            return True
            
        except Exception as e:
            print(f"❌ ZIP包创建失败: {e}")
            return False
    
    def cleanup(self):
        """清理临时文件"""
        self.print_step(7, "清理临时文件")
        
        temp_files = [
            'AllSportsNow.spec',
            'build',
        ]
        
        for item in temp_files:
            path = Path(item)
            try:
                if path.is_file():
                    path.unlink()
                    print(f"✅ 删除文件: {item}")
                elif path.is_dir():
                    shutil.rmtree(path)
                    print(f"✅ 删除目录: {item}")
            except Exception as e:
                print(f"⚠️ 清理失败 {item}: {e}")
    
    def build(self):
        """执行完整构建流程"""
        print("🚀 开始构建 AllSportsNow 便携版")
        print(f"📁 项目目录: {self.project_dir}")
        print(f"📦 输出目录: {self.output_dir}")
        
        start_time = time.time()
        
        try:
            # 执行构建步骤
            self.install_dependencies()
            self.create_icons()
            self.create_spec_file()
            
            if not self.build_executable():
                return False
            
            if not self.create_additional_files():
                return False
            
            self.create_zip_package()
            self.cleanup()
            
            # 构建完成
            elapsed_time = time.time() - start_time
            
            print(f"\n{'='*60}")
            print("🎉 构建完成！")
            print(f"{'='*60}")
            print(f"⏱️ 构建时间: {elapsed_time:.1f} 秒")
            print(f"📁 输出目录: {self.output_dir}")
            print(f"🚀 启动文件: {self.output_dir / 'AllSportsNow.exe'}")
            print(f"📦 压缩包: {self.dist_dir}")
            print("\n✅ 便携版应用程序已准备就绪！")
            print("💡 您可以将整个文件夹复制到任何Windows计算机上运行")
            
            return True
            
        except Exception as e:
            print(f"\n❌ 构建失败: {e}")
            return False

def main():
    """主函数"""
    builder = PortableBuilder()
    success = builder.build()
    
    if success:
        print("\n🎊 恭喜！AllSportsNow 便携版构建成功！")
    else:
        print("\n😞 构建失败，请检查错误信息")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
