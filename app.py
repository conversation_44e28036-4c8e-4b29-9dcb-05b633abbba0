from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
from flask_login import <PERSON><PERSON><PERSON><PERSON><PERSON>, login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash
from datetime import datetime, date, time
import os

from config import Config
from models import db, User, Customer, MenuItem, Category, Table, Reservation, Order, OrderItem, Ingredient, Attendance, Schedule

def create_app():
    app = Flask(__name__)
    app.config.from_object(Config)

    # 初始化扩展
    db.init_app(app)

    # 初始化登录管理器
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'
    login_manager.login_message = '请先登录以访问此页面。'

    @login_manager.user_loader
    def load_user(user_id):
        return User.query.get(int(user_id))

    # 创建数据库表
    with app.app_context():
        db.create_all()
        init_data()

    # 注册蓝图
    from routes.auth import auth_bp
    from routes.main import main_bp
    from routes.inventory import inventory_bp
    from routes.pos import pos_bp
    from routes.reservation import reservation_bp
    from routes.staff import staff_bp
    from routes.customer import customer_bp

    app.register_blueprint(auth_bp, url_prefix='/auth')
    app.register_blueprint(main_bp)
    app.register_blueprint(inventory_bp, url_prefix='/inventory')
    app.register_blueprint(pos_bp, url_prefix='/pos')
    app.register_blueprint(reservation_bp, url_prefix='/reservation')
    app.register_blueprint(staff_bp, url_prefix='/staff')
    app.register_blueprint(customer_bp, url_prefix='/customer')

    return app

def init_data():
    """初始化基础数据"""
    # 检查是否已有管理员用户
    if not User.query.filter_by(role='admin').first():
        admin = User(
            username='admin',
            email='<EMAIL>',
            name='系统管理员',
            role='admin'
        )
        admin.set_password('admin123')
        db.session.add(admin)

    # 初始化菜品分类
    if not Category.query.first():
        categories = [
            Category(name='牛排类', description='各种牛排'),
            Category(name='开胃菜', description='餐前小食'),
            Category(name='汤品', description='各式汤品'),
            Category(name='沙拉', description='新鲜沙拉'),
            Category(name='甜品', description='餐后甜品'),
            Category(name='饮品', description='各种饮料')
        ]
        for category in categories:
            db.session.add(category)

    # 初始化桌位
    if not Table.query.first():
        tables = [
            Table(number='T01', capacity=2, location='大厅'),
            Table(number='T02', capacity=4, location='大厅'),
            Table(number='T03', capacity=4, location='大厅'),
            Table(number='T04', capacity=6, location='大厅'),
            Table(number='VIP01', capacity=8, location='包间'),
            Table(number='VIP02', capacity=10, location='包间'),
        ]
        for table in tables:
            db.session.add(table)

    # 初始化基础菜品
    if not MenuItem.query.first():
        # 牛排类菜品
        steak_category = Category.query.filter_by(name='牛排类').first()
        appetizer_category = Category.query.filter_by(name='开胃菜').first()
        soup_category = Category.query.filter_by(name='汤品').first()
        salad_category = Category.query.filter_by(name='沙拉').first()
        dessert_category = Category.query.filter_by(name='甜品').first()
        drink_category = Category.query.filter_by(name='饮品').first()

        menu_items = []

        # 牛排类
        if steak_category:
            menu_items.extend([
                MenuItem(name='菲力牛排', description='嫩滑菲力牛排，口感极佳，选用优质牛肉', price=188.0, category_id=steak_category.id, preparation_time=20),
                MenuItem(name='西冷牛排', description='经典西冷牛排，肉质鲜美，纹理清晰', price=158.0, category_id=steak_category.id, preparation_time=18),
                MenuItem(name='T骨牛排', description='T骨牛排，一次享受两种口感，菲力与西冷的完美结合', price=228.0, category_id=steak_category.id, preparation_time=22),
                MenuItem(name='肋眼牛排', description='肋眼牛排，油花丰富，口感浓郁', price=198.0, category_id=steak_category.id, preparation_time=20),
                MenuItem(name='战斧牛排', description='超大份战斧牛排，适合2-3人分享', price=388.0, category_id=steak_category.id, preparation_time=25),
                MenuItem(name='和牛牛排', description='顶级和牛，入口即化的极致体验', price=588.0, category_id=steak_category.id, preparation_time=18),
            ])

        # 开胃菜
        if appetizer_category:
            menu_items.extend([
                MenuItem(name='蒜蓉面包', description='香脆蒜蓉面包，开胃首选', price=28.0, category_id=appetizer_category.id, preparation_time=8),
                MenuItem(name='芝士焗蘑菇', description='新鲜蘑菇配芝士焗制，香浓可口', price=48.0, category_id=appetizer_category.id, preparation_time=12),
                MenuItem(name='鹅肝酱', description='法式鹅肝酱，搭配吐司享用', price=88.0, category_id=appetizer_category.id, preparation_time=10),
                MenuItem(name='烟熏三文鱼', description='挪威烟熏三文鱼，配柠檬和洋葱', price=68.0, category_id=appetizer_category.id, preparation_time=5),
                MenuItem(name='炸洋葱圈', description='酥脆洋葱圈，配特制蘸酱', price=38.0, category_id=appetizer_category.id, preparation_time=10),
            ])

        # 汤品
        if soup_category:
            menu_items.extend([
                MenuItem(name='牛骨汤', description='浓郁牛骨汤，营养丰富', price=38.0, category_id=soup_category.id, preparation_time=5),
                MenuItem(name='蘑菇浓汤', description='奶油蘑菇浓汤，口感顺滑', price=32.0, category_id=soup_category.id, preparation_time=8),
                MenuItem(name='法式洋葱汤', description='经典法式洋葱汤，配芝士', price=42.0, category_id=soup_category.id, preparation_time=10),
                MenuItem(name='海鲜汤', description='新鲜海鲜熬制的清汤', price=58.0, category_id=soup_category.id, preparation_time=12),
            ])

        # 沙拉
        if salad_category:
            menu_items.extend([
                MenuItem(name='凯撒沙拉', description='经典凯撒沙拉，配帕玛森芝士', price=48.0, category_id=salad_category.id, preparation_time=8),
                MenuItem(name='田园沙拉', description='新鲜蔬菜沙拉，健康清爽', price=38.0, category_id=salad_category.id, preparation_time=6),
                MenuItem(name='牛排沙拉', description='烤牛排配混合蔬菜沙拉', price=78.0, category_id=salad_category.id, preparation_time=15),
                MenuItem(name='水果沙拉', description='时令水果沙拉，酸甜可口', price=42.0, category_id=salad_category.id, preparation_time=5),
            ])

        # 甜品
        if dessert_category:
            menu_items.extend([
                MenuItem(name='提拉米苏', description='经典意式提拉米苏，香甜浓郁', price=48.0, category_id=dessert_category.id, preparation_time=5),
                MenuItem(name='巧克力熔岩蛋糕', description='温热巧克力熔岩蛋糕，配香草冰淇淋', price=52.0, category_id=dessert_category.id, preparation_time=12),
                MenuItem(name='芝士蛋糕', description='纽约风味芝士蛋糕，口感丰富', price=45.0, category_id=dessert_category.id, preparation_time=5),
                MenuItem(name='冰淇淋拼盘', description='三球冰淇淋拼盘，多种口味', price=38.0, category_id=dessert_category.id, preparation_time=3),
            ])

        # 饮品
        if drink_category:
            menu_items.extend([
                MenuItem(name='红酒', description='精选红酒，配牛排绝佳', price=128.0, category_id=drink_category.id, preparation_time=2),
                MenuItem(name='白酒', description='清爽白酒，适合海鲜', price=118.0, category_id=drink_category.id, preparation_time=2),
                MenuItem(name='啤酒', description='冰镇啤酒，清爽解腻', price=28.0, category_id=drink_category.id, preparation_time=1),
                MenuItem(name='果汁', description='新鲜果汁，多种口味可选', price=25.0, category_id=drink_category.id, preparation_time=3),
                MenuItem(name='咖啡', description='现磨咖啡，香浓醇厚', price=32.0, category_id=drink_category.id, preparation_time=5),
                MenuItem(name='茶', description='精选茶叶，清香怡人', price=28.0, category_id=drink_category.id, preparation_time=3),
                MenuItem(name='气泡水', description='进口气泡水，清爽解腻', price=18.0, category_id=drink_category.id, preparation_time=1),
            ])

        for item in menu_items:
            db.session.add(item)

    # 初始化原料库存
    if not Ingredient.query.first():
        ingredients = [
            Ingredient(name='牛肉-菲力', unit='kg', current_stock=50.0, min_stock=10.0, unit_cost=80.0),
            Ingredient(name='牛肉-西冷', unit='kg', current_stock=40.0, min_stock=8.0, unit_cost=60.0),
            Ingredient(name='土豆', unit='kg', current_stock=100.0, min_stock=20.0, unit_cost=5.0),
            Ingredient(name='洋葱', unit='kg', current_stock=30.0, min_stock=5.0, unit_cost=3.0),
        ]
        for ingredient in ingredients:
            db.session.add(ingredient)

    db.session.commit()

app = create_app()

if __name__ == '__main__':
    app.run(debug=True)
