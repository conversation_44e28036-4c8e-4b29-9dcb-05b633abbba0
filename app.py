from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
from flask_login import Lo<PERSON><PERSON><PERSON><PERSON>, login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash
from datetime import datetime, date, time
import os

from config import Config
from models import db, User, Customer, MenuItem, Category, Table, Reservation, Order, OrderItem, Ingredient, Attendance, Schedule

def create_app():
    app = Flask(__name__)
    app.config.from_object(Config)

    # 初始化扩展
    db.init_app(app)

    # 初始化登录管理器
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'
    login_manager.login_message = '请先登录以访问此页面。'

    @login_manager.user_loader
    def load_user(user_id):
        return User.query.get(int(user_id))

    # 创建数据库表
    with app.app_context():
        db.create_all()
        init_data()

    # 注册蓝图
    from routes.auth import auth_bp
    from routes.main import main_bp
    from routes.inventory import inventory_bp
    from routes.pos import pos_bp
    from routes.staff import staff_bp
    from routes.customer import customer_bp

    app.register_blueprint(auth_bp, url_prefix='/auth')
    app.register_blueprint(main_bp)
    app.register_blueprint(inventory_bp, url_prefix='/inventory')
    app.register_blueprint(pos_bp, url_prefix='/pos')
    app.register_blueprint(staff_bp, url_prefix='/staff')
    app.register_blueprint(customer_bp, url_prefix='/customer')

    return app

def init_data():
    """初始化基础数据"""
    # 检查是否已有管理员用户
    if not User.query.filter_by(role='admin').first():
        admin = User(
            username='admin',
            email='<EMAIL>',
            name='系统管理员',
            role='admin'
        )
        admin.set_password('admin123')
        db.session.add(admin)

    # 初始化菜品分类
    if not Category.query.first():
        categories = [
            Category(name='牛排类', description='各种牛排'),
            Category(name='海鲜类', description='新鲜海鲜'),
            Category(name='鸡肉类', description='各式鸡肉料理'),
            Category(name='猪肉类', description='精选猪肉菜品'),
            Category(name='羊肉类', description='优质羊肉料理'),
            Category(name='鱼类', description='各种鱼类料理'),
            Category(name='烧烤类', description='炭火烧烤'),
            Category(name='火锅类', description='各式火锅'),
            Category(name='川菜', description='四川风味菜品'),
            Category(name='粤菜', description='广东风味菜品'),
            Category(name='湘菜', description='湖南风味菜品'),
            Category(name='鲁菜', description='山东风味菜品'),
            Category(name='苏菜', description='江苏风味菜品'),
            Category(name='浙菜', description='浙江风味菜品'),
            Category(name='闽菜', description='福建风味菜品'),
            Category(name='徽菜', description='安徽风味菜品'),
            Category(name='西餐', description='西式料理'),
            Category(name='日料', description='日式料理'),
            Category(name='韩料', description='韩式料理'),
            Category(name='泰菜', description='泰式料理'),
            Category(name='印度菜', description='印度风味'),
            Category(name='意大利菜', description='意式料理'),
            Category(name='法国菜', description='法式料理'),
            Category(name='开胃菜', description='餐前小食'),
            Category(name='汤品', description='各式汤品'),
            Category(name='沙拉', description='新鲜沙拉'),
            Category(name='主食', description='米饭面条类'),
            Category(name='素食', description='素食料理'),
            Category(name='甜品', description='餐后甜品'),
            Category(name='饮品', description='各种饮料'),
            Category(name='酒类', description='红酒白酒啤酒'),
            Category(name='咖啡茶饮', description='咖啡茶类饮品'),
            Category(name='小食', description='休闲小食'),
            Category(name='早餐', description='早餐系列'),
            Category(name='夜宵', description='夜宵系列')
        ]
        for category in categories:
            db.session.add(category)

    # 初始化桌位
    if not Table.query.first():
        tables = [
            Table(number='T01', capacity=2, location='大厅'),
            Table(number='T02', capacity=4, location='大厅'),
            Table(number='T03', capacity=4, location='大厅'),
            Table(number='T04', capacity=6, location='大厅'),
            Table(number='VIP01', capacity=8, location='包间'),
            Table(number='VIP02', capacity=10, location='包间'),
        ]
        for table in tables:
            db.session.add(table)

    # 初始化基础菜品
    if not MenuItem.query.first():
        # 获取所有分类
        steak_category = Category.query.filter_by(name='牛排类').first()
        seafood_category = Category.query.filter_by(name='海鲜类').first()
        chicken_category = Category.query.filter_by(name='鸡肉类').first()
        pork_category = Category.query.filter_by(name='猪肉类').first()
        lamb_category = Category.query.filter_by(name='羊肉类').first()
        fish_category = Category.query.filter_by(name='鱼类').first()
        bbq_category = Category.query.filter_by(name='烧烤类').first()
        hotpot_category = Category.query.filter_by(name='火锅类').first()
        sichuan_category = Category.query.filter_by(name='川菜').first()
        cantonese_category = Category.query.filter_by(name='粤菜').first()
        hunan_category = Category.query.filter_by(name='湘菜').first()
        shandong_category = Category.query.filter_by(name='鲁菜').first()
        jiangsu_category = Category.query.filter_by(name='苏菜').first()
        zhejiang_category = Category.query.filter_by(name='浙菜').first()
        fujian_category = Category.query.filter_by(name='闽菜').first()
        anhui_category = Category.query.filter_by(name='徽菜').first()
        western_category = Category.query.filter_by(name='西餐').first()
        japanese_category = Category.query.filter_by(name='日料').first()
        korean_category = Category.query.filter_by(name='韩料').first()
        thai_category = Category.query.filter_by(name='泰菜').first()
        indian_category = Category.query.filter_by(name='印度菜').first()
        italian_category = Category.query.filter_by(name='意大利菜').first()
        french_category = Category.query.filter_by(name='法国菜').first()
        appetizer_category = Category.query.filter_by(name='开胃菜').first()
        soup_category = Category.query.filter_by(name='汤品').first()
        salad_category = Category.query.filter_by(name='沙拉').first()
        staple_category = Category.query.filter_by(name='主食').first()
        vegetarian_category = Category.query.filter_by(name='素食').first()
        dessert_category = Category.query.filter_by(name='甜品').first()
        drink_category = Category.query.filter_by(name='饮品').first()
        wine_category = Category.query.filter_by(name='酒类').first()
        coffee_category = Category.query.filter_by(name='咖啡茶饮').first()
        snack_category = Category.query.filter_by(name='小食').first()
        breakfast_category = Category.query.filter_by(name='早餐').first()
        latenight_category = Category.query.filter_by(name='夜宵').first()

        menu_items = []

        # 牛排类 (12种)
        if steak_category:
            menu_items.extend([
                MenuItem(name='菲力牛排', description='嫩滑菲力牛排，口感极佳，选用优质牛肉', price=188.0, category_id=steak_category.id, preparation_time=20),
                MenuItem(name='西冷牛排', description='经典西冷牛排，肉质鲜美，纹理清晰', price=158.0, category_id=steak_category.id, preparation_time=18),
                MenuItem(name='T骨牛排', description='T骨牛排，一次享受两种口感，菲力与西冷的完美结合', price=228.0, category_id=steak_category.id, preparation_time=22),
                MenuItem(name='肋眼牛排', description='肋眼牛排，油花丰富，口感浓郁', price=198.0, category_id=steak_category.id, preparation_time=20),
                MenuItem(name='战斧牛排', description='超大份战斧牛排，适合2-3人分享', price=388.0, category_id=steak_category.id, preparation_time=25),
                MenuItem(name='和牛牛排', description='顶级和牛，入口即化的极致体验', price=588.0, category_id=steak_category.id, preparation_time=18),
                MenuItem(name='纽约客牛排', description='纽约客牛排，肉质紧实，口感浓郁', price=168.0, category_id=steak_category.id, preparation_time=19),
                MenuItem(name='牛小排', description='精选牛小排，肉质鲜嫩多汁', price=148.0, category_id=steak_category.id, preparation_time=25),
                MenuItem(name='牛仔骨', description='美式牛仔骨，豪迈大份量', price=268.0, category_id=steak_category.id, preparation_time=30),
                MenuItem(name='澳洲牛排', description='澳洲进口牛排，草饲牛肉香味浓郁', price=218.0, category_id=steak_category.id, preparation_time=20),
                MenuItem(name='黑椒牛排', description='经典黑椒牛排，香辣开胃', price=138.0, category_id=steak_category.id, preparation_time=16),
                MenuItem(name='蒜香牛排', description='蒜香牛排，香气扑鼻', price=128.0, category_id=steak_category.id, preparation_time=15),
            ])

        # 海鲜类 (10种)
        if seafood_category:
            menu_items.extend([
                MenuItem(name='波士顿龙虾', description='新鲜波士顿龙虾，肉质鲜甜', price=298.0, category_id=seafood_category.id, preparation_time=25),
                MenuItem(name='阿拉斯加帝王蟹', description='阿拉斯加帝王蟹腿，肉质饱满', price=388.0, category_id=seafood_category.id, preparation_time=20),
                MenuItem(name='烤三文鱼', description='挪威三文鱼，肉质鲜美', price=128.0, category_id=seafood_category.id, preparation_time=15),
                MenuItem(name='香煎鳕鱼', description='深海鳕鱼，肉质细嫩', price=98.0, category_id=seafood_category.id, preparation_time=12),
                MenuItem(name='蒜蓉扇贝', description='新鲜扇贝配蒜蓉，鲜香可口', price=88.0, category_id=seafood_category.id, preparation_time=10),
                MenuItem(name='黄油焗生蚝', description='法式黄油焗生蚝，奶香浓郁', price=68.0, category_id=seafood_category.id, preparation_time=8),
                MenuItem(name='椒盐虾', description='椒盐大虾，外酥内嫩', price=78.0, category_id=seafood_category.id, preparation_time=12),
                MenuItem(name='清蒸石斑鱼', description='新鲜石斑鱼，清蒸保持原味', price=168.0, category_id=seafood_category.id, preparation_time=18),
                MenuItem(name='蒜蓉粉丝蒸扇贝', description='经典粤式做法，鲜美可口', price=98.0, category_id=seafood_category.id, preparation_time=15),
                MenuItem(name='海鲜拼盘', description='多种海鲜组合，丰富多样', price=258.0, category_id=seafood_category.id, preparation_time=20),
            ])

        # 鸡肉类 (8种)
        if chicken_category:
            menu_items.extend([
                MenuItem(name='香草烤鸡', description='法式香草烤鸡，香气浓郁', price=88.0, category_id=chicken_category.id, preparation_time=25),
                MenuItem(name='柠檬鸡胸', description='柠檬汁腌制鸡胸，清香爽口', price=68.0, category_id=chicken_category.id, preparation_time=15),
                MenuItem(name='蜜汁鸡翅', description='蜜汁烤鸡翅，甜香可口', price=48.0, category_id=chicken_category.id, preparation_time=20),
                MenuItem(name='奥尔良鸡腿', description='奥尔良风味烤鸡腿', price=58.0, category_id=chicken_category.id, preparation_time=22),
                MenuItem(name='白切鸡', description='传统白切鸡，配姜蓉蘸料', price=78.0, category_id=chicken_category.id, preparation_time=30),
                MenuItem(name='口水鸡', description='四川口水鸡，麻辣鲜香', price=68.0, category_id=chicken_category.id, preparation_time=15),
                MenuItem(name='宫保鸡丁', description='经典宫保鸡丁，酸甜微辣', price=58.0, category_id=chicken_category.id, preparation_time=12),
                MenuItem(name='椰子鸡', description='海南椰子鸡，清甜滋补', price=98.0, category_id=chicken_category.id, preparation_time=35),
            ])

        # 猪肉类 (8种)
        if pork_category:
            menu_items.extend([
                MenuItem(name='蜜汁叉烧', description='广式蜜汁叉烧，甜香可口', price=78.0, category_id=pork_category.id, preparation_time=25),
                MenuItem(name='红烧肉', description='经典红烧肉，肥瘦相间', price=68.0, category_id=pork_category.id, preparation_time=45),
                MenuItem(name='糖醋里脊', description='酸甜糖醋里脊，外酥内嫩', price=58.0, category_id=pork_category.id, preparation_time=15),
                MenuItem(name='回锅肉', description='四川回锅肉，香辣下饭', price=48.0, category_id=pork_category.id, preparation_time=12),
                MenuItem(name='东坡肉', description='杭州名菜东坡肉，肥而不腻', price=88.0, category_id=pork_category.id, preparation_time=60),
                MenuItem(name='咕噜肉', description='广式咕噜肉，酸甜开胃', price=58.0, category_id=pork_category.id, preparation_time=18),
                MenuItem(name='白切肉', description='白切肉配蒜泥，清淡爽口', price=48.0, category_id=pork_category.id, preparation_time=20),
                MenuItem(name='烤猪排', description='香烤猪排，外焦内嫩', price=68.0, category_id=pork_category.id, preparation_time=25),
            ])

        # 羊肉类 (6种)
        if lamb_category:
            menu_items.extend([
                MenuItem(name='烤羊排', description='新疆烤羊排，香嫩多汁', price=128.0, category_id=lamb_category.id, preparation_time=30),
                MenuItem(name='红焖羊肉', description='红焖羊肉，软烂香浓', price=98.0, category_id=lamb_category.id, preparation_time=45),
                MenuItem(name='孜然羊肉', description='新疆孜然羊肉，香辣可口', price=78.0, category_id=lamb_category.id, preparation_time=15),
                MenuItem(name='羊肉串', description='正宗羊肉串，炭火烧烤', price=48.0, category_id=lamb_category.id, preparation_time=12),
                MenuItem(name='手抓羊肉', description='西北手抓羊肉，原汁原味', price=108.0, category_id=lamb_category.id, preparation_time=40),
                MenuItem(name='羊蝎子', description='北京羊蝎子，滋补美味', price=88.0, category_id=lamb_category.id, preparation_time=50),
            ])

        # 开胃菜 (10种)
        if appetizer_category:
            menu_items.extend([
                MenuItem(name='蒜蓉面包', description='香脆蒜蓉面包，开胃首选', price=28.0, category_id=appetizer_category.id, preparation_time=8),
                MenuItem(name='芝士焗蘑菇', description='新鲜蘑菇配芝士焗制，香浓可口', price=48.0, category_id=appetizer_category.id, preparation_time=12),
                MenuItem(name='鹅肝酱', description='法式鹅肝酱，搭配吐司享用', price=88.0, category_id=appetizer_category.id, preparation_time=10),
                MenuItem(name='烟熏三文鱼', description='挪威烟熏三文鱼，配柠檬和洋葱', price=68.0, category_id=appetizer_category.id, preparation_time=5),
                MenuItem(name='炸洋葱圈', description='酥脆洋葱圈，配特制蘸酱', price=38.0, category_id=appetizer_category.id, preparation_time=10),
                MenuItem(name='芝士拼盘', description='进口芝士拼盘，配红酒享用', price=78.0, category_id=appetizer_category.id, preparation_time=5),
                MenuItem(name='火腿拼盘', description='西班牙火腿拼盘，口感丰富', price=98.0, category_id=appetizer_category.id, preparation_time=8),
                MenuItem(name='鱼子酱', description='俄罗斯鱼子酱，奢华享受', price=188.0, category_id=appetizer_category.id, preparation_time=3),
                MenuItem(name='蟹肉沙拉', description='新鲜蟹肉沙拉，清爽开胃', price=68.0, category_id=appetizer_category.id, preparation_time=10),
                MenuItem(name='意式布鲁斯凯塔', description='意式烤面包片，配番茄罗勒', price=38.0, category_id=appetizer_category.id, preparation_time=8),
            ])

        # 汤品 (8种)
        if soup_category:
            menu_items.extend([
                MenuItem(name='牛骨汤', description='浓郁牛骨汤，营养丰富', price=38.0, category_id=soup_category.id, preparation_time=5),
                MenuItem(name='蘑菇浓汤', description='奶油蘑菇浓汤，口感顺滑', price=32.0, category_id=soup_category.id, preparation_time=8),
                MenuItem(name='法式洋葱汤', description='经典法式洋葱汤，配芝士', price=42.0, category_id=soup_category.id, preparation_time=10),
                MenuItem(name='海鲜汤', description='新鲜海鲜熬制的清汤', price=58.0, category_id=soup_category.id, preparation_time=12),
                MenuItem(name='番茄汤', description='意式番茄汤，酸甜开胃', price=28.0, category_id=soup_category.id, preparation_time=8),
                MenuItem(name='玉米浓汤', description='香甜玉米浓汤，营养丰富', price=26.0, category_id=soup_category.id, preparation_time=10),
                MenuItem(name='鸡汤', description='老母鸡汤，滋补养生', price=48.0, category_id=soup_category.id, preparation_time=15),
                MenuItem(name='冬瓜汤', description='清淡冬瓜汤，去火降燥', price=22.0, category_id=soup_category.id, preparation_time=12),
            ])

        # 沙拉 (8种)
        if salad_category:
            menu_items.extend([
                MenuItem(name='凯撒沙拉', description='经典凯撒沙拉，配帕玛森芝士', price=48.0, category_id=salad_category.id, preparation_time=8),
                MenuItem(name='田园沙拉', description='新鲜蔬菜沙拉，健康清爽', price=38.0, category_id=salad_category.id, preparation_time=6),
                MenuItem(name='牛排沙拉', description='烤牛排配混合蔬菜沙拉', price=78.0, category_id=salad_category.id, preparation_time=15),
                MenuItem(name='水果沙拉', description='时令水果沙拉，酸甜可口', price=42.0, category_id=salad_category.id, preparation_time=5),
                MenuItem(name='希腊沙拉', description='希腊风味沙拉，配橄榄和芝士', price=52.0, category_id=salad_category.id, preparation_time=8),
                MenuItem(name='金枪鱼沙拉', description='金枪鱼沙拉，高蛋白低脂', price=58.0, category_id=salad_category.id, preparation_time=10),
                MenuItem(name='鸡肉沙拉', description='烤鸡肉沙拉，营养均衡', price=48.0, category_id=salad_category.id, preparation_time=12),
                MenuItem(name='虾仁沙拉', description='新鲜虾仁沙拉，清爽美味', price=68.0, category_id=salad_category.id, preparation_time=10),
            ])

        # 主食 (10种)
        if staple_category:
            menu_items.extend([
                MenuItem(name='意大利面', description='经典意大利面，多种口味可选', price=48.0, category_id=staple_category.id, preparation_time=15),
                MenuItem(name='海鲜焗饭', description='海鲜焗饭，料足味美', price=68.0, category_id=staple_category.id, preparation_time=20),
                MenuItem(name='牛肉炒饭', description='牛肉炒饭，香气扑鼻', price=38.0, category_id=staple_category.id, preparation_time=12),
                MenuItem(name='蛋炒饭', description='经典蛋炒饭，简单美味', price=28.0, category_id=staple_category.id, preparation_time=8),
                MenuItem(name='海鲜面', description='海鲜拉面，汤鲜面滑', price=58.0, category_id=staple_category.id, preparation_time=18),
                MenuItem(name='牛肉面', description='红烧牛肉面，汤浓肉香', price=42.0, category_id=staple_category.id, preparation_time=15),
                MenuItem(name='鸡丝面', description='鸡丝拌面，清淡爽口', price=32.0, category_id=staple_category.id, preparation_time=12),
                MenuItem(name='培根意面', description='培根奶油意面，香浓可口', price=52.0, category_id=staple_category.id, preparation_time=16),
                MenuItem(name='海鲜披萨', description='海鲜披萨，芝士拉丝', price=78.0, category_id=staple_category.id, preparation_time=25),
                MenuItem(name='玛格丽特披萨', description='经典玛格丽特披萨，简约美味', price=58.0, category_id=staple_category.id, preparation_time=20),
            ])

        # 素食 (8种)
        if vegetarian_category:
            menu_items.extend([
                MenuItem(name='麻婆豆腐', description='经典麻婆豆腐，麻辣鲜香', price=32.0, category_id=vegetarian_category.id, preparation_time=12),
                MenuItem(name='红烧茄子', description='红烧茄子，软糯香甜', price=28.0, category_id=vegetarian_category.id, preparation_time=15),
                MenuItem(name='宫保杏鲍菇', description='宫保杏鲍菇，口感丰富', price=38.0, category_id=vegetarian_category.id, preparation_time=10),
                MenuItem(name='干煸四季豆', description='干煸四季豆，香辣下饭', price=26.0, category_id=vegetarian_category.id, preparation_time=12),
                MenuItem(name='清炒菠菜', description='清炒菠菜，清淡营养', price=22.0, category_id=vegetarian_category.id, preparation_time=8),
                MenuItem(name='蒜蓉西兰花', description='蒜蓉西兰花，清香爽脆', price=24.0, category_id=vegetarian_category.id, preparation_time=8),
                MenuItem(name='素炒豆芽', description='素炒豆芽，清脆爽口', price=18.0, category_id=vegetarian_category.id, preparation_time=6),
                MenuItem(name='凉拌黄瓜', description='凉拌黄瓜，清爽解腻', price=16.0, category_id=vegetarian_category.id, preparation_time=5),
            ])

        # 甜品 (10种)
        if dessert_category:
            menu_items.extend([
                MenuItem(name='提拉米苏', description='经典意式提拉米苏，香甜浓郁', price=48.0, category_id=dessert_category.id, preparation_time=5),
                MenuItem(name='巧克力熔岩蛋糕', description='温热巧克力熔岩蛋糕，配香草冰淇淋', price=52.0, category_id=dessert_category.id, preparation_time=12),
                MenuItem(name='芝士蛋糕', description='纽约风味芝士蛋糕，口感丰富', price=45.0, category_id=dessert_category.id, preparation_time=5),
                MenuItem(name='冰淇淋拼盘', description='三球冰淇淋拼盘，多种口味', price=38.0, category_id=dessert_category.id, preparation_time=3),
                MenuItem(name='马卡龙', description='法式马卡龙，精致甜美', price=58.0, category_id=dessert_category.id, preparation_time=3),
                MenuItem(name='布丁', description='焦糖布丁，嫩滑香甜', price=32.0, category_id=dessert_category.id, preparation_time=5),
                MenuItem(name='慕斯蛋糕', description='巧克力慕斯蛋糕，轻盈香浓', price=42.0, category_id=dessert_category.id, preparation_time=5),
                MenuItem(name='舒芙蕾', description='法式舒芙蕾，蓬松香甜', price=48.0, category_id=dessert_category.id, preparation_time=15),
                MenuItem(name='水果塔', description='季节水果塔，酸甜可口', price=38.0, category_id=dessert_category.id, preparation_time=8),
                MenuItem(name='千层蛋糕', description='千层蛋糕，层次丰富', price=45.0, category_id=dessert_category.id, preparation_time=5),
            ])

        # 饮品 (8种)
        if drink_category:
            menu_items.extend([
                MenuItem(name='鲜榨橙汁', description='新鲜橙汁，维C丰富', price=25.0, category_id=drink_category.id, preparation_time=3),
                MenuItem(name='苹果汁', description='新鲜苹果汁，清甜爽口', price=22.0, category_id=drink_category.id, preparation_time=3),
                MenuItem(name='西瓜汁', description='夏日西瓜汁，清热解暑', price=20.0, category_id=drink_category.id, preparation_time=3),
                MenuItem(name='柠檬蜂蜜茶', description='柠檬蜂蜜茶，酸甜养颜', price=28.0, category_id=drink_category.id, preparation_time=5),
                MenuItem(name='气泡水', description='进口气泡水，清爽解腻', price=18.0, category_id=drink_category.id, preparation_time=1),
                MenuItem(name='可乐', description='经典可乐，冰爽畅快', price=15.0, category_id=drink_category.id, preparation_time=1),
                MenuItem(name='雪碧', description='柠檬味汽水，清新怡人', price=15.0, category_id=drink_category.id, preparation_time=1),
                MenuItem(name='矿泉水', description='天然矿泉水，纯净甘甜', price=8.0, category_id=drink_category.id, preparation_time=1),
            ])

        # 酒类 (12种)
        if wine_category:
            menu_items.extend([
                MenuItem(name='法国红酒', description='法国波尔多红酒，醇厚浓郁', price=188.0, category_id=wine_category.id, preparation_time=2),
                MenuItem(name='意大利红酒', description='意大利基安帝红酒，果香浓郁', price=158.0, category_id=wine_category.id, preparation_time=2),
                MenuItem(name='澳洲红酒', description='澳洲设拉子红酒，口感丰富', price=128.0, category_id=wine_category.id, preparation_time=2),
                MenuItem(name='智利红酒', description='智利赤霞珠红酒，性价比高', price=98.0, category_id=wine_category.id, preparation_time=2),
                MenuItem(name='香槟', description='法国香槟，庆祝首选', price=288.0, category_id=wine_category.id, preparation_time=3),
                MenuItem(name='白葡萄酒', description='德国雷司令白酒，清香甘甜', price=118.0, category_id=wine_category.id, preparation_time=2),
                MenuItem(name='威士忌', description='苏格兰威士忌，醇厚回甘', price=168.0, category_id=wine_category.id, preparation_time=2),
                MenuItem(name='白兰地', description='法国干邑白兰地，香醇优雅', price=198.0, category_id=wine_category.id, preparation_time=2),
                MenuItem(name='伏特加', description='俄罗斯伏特加，纯净烈性', price=138.0, category_id=wine_category.id, preparation_time=2),
                MenuItem(name='朗姆酒', description='加勒比朗姆酒，热带风情', price=128.0, category_id=wine_category.id, preparation_time=2),
                MenuItem(name='青岛啤酒', description='经典青岛啤酒，清爽甘醇', price=28.0, category_id=wine_category.id, preparation_time=1),
                MenuItem(name='进口啤酒', description='德国进口啤酒，麦香浓郁', price=38.0, category_id=wine_category.id, preparation_time=1),
            ])

        # 咖啡茶饮 (10种)
        if coffee_category:
            menu_items.extend([
                MenuItem(name='美式咖啡', description='经典美式咖啡，香浓醇厚', price=32.0, category_id=coffee_category.id, preparation_time=5),
                MenuItem(name='拿铁咖啡', description='意式拿铁，奶香浓郁', price=38.0, category_id=coffee_category.id, preparation_time=6),
                MenuItem(name='卡布奇诺', description='意式卡布奇诺，奶泡丰富', price=36.0, category_id=coffee_category.id, preparation_time=6),
                MenuItem(name='摩卡咖啡', description='巧克力摩卡，甜香可口', price=42.0, category_id=coffee_category.id, preparation_time=7),
                MenuItem(name='浓缩咖啡', description='意式浓缩，香浓提神', price=28.0, category_id=coffee_category.id, preparation_time=3),
                MenuItem(name='龙井茶', description='西湖龙井，清香淡雅', price=48.0, category_id=coffee_category.id, preparation_time=5),
                MenuItem(name='铁观音', description='福建铁观音，香韵悠长', price=52.0, category_id=coffee_category.id, preparation_time=5),
                MenuItem(name='普洱茶', description='云南普洱，醇厚回甘', price=58.0, category_id=coffee_category.id, preparation_time=6),
                MenuItem(name='英式红茶', description='英式下午茶，配奶配糖', price=35.0, category_id=coffee_category.id, preparation_time=4),
                MenuItem(name='花草茶', description='混合花草茶，清香怡人', price=32.0, category_id=coffee_category.id, preparation_time=4),
            ])

        # 鱼类 (12种)
        if fish_category:
            menu_items.extend([
                MenuItem(name='清蒸鲈鱼', description='新鲜鲈鱼清蒸，保持原味', price=88.0, category_id=fish_category.id, preparation_time=20),
                MenuItem(name='红烧带鱼', description='红烧带鱼，咸鲜下饭', price=58.0, category_id=fish_category.id, preparation_time=18),
                MenuItem(name='糖醋鲤鱼', description='糖醋鲤鱼，酸甜可口', price=78.0, category_id=fish_category.id, preparation_time=25),
                MenuItem(name='水煮鱼', description='四川水煮鱼，麻辣鲜香', price=98.0, category_id=fish_category.id, preparation_time=22),
                MenuItem(name='酸菜鱼', description='酸菜鱼，酸辣开胃', price=88.0, category_id=fish_category.id, preparation_time=20),
                MenuItem(name='剁椒鱼头', description='湖南剁椒鱼头，香辣下饭', price=108.0, category_id=fish_category.id, preparation_time=30),
                MenuItem(name='松鼠桂鱼', description='江苏名菜松鼠桂鱼，造型精美', price=128.0, category_id=fish_category.id, preparation_time=35),
                MenuItem(name='西湖醋鱼', description='杭州名菜西湖醋鱼，酸甜鲜美', price=118.0, category_id=fish_category.id, preparation_time=25),
                MenuItem(name='红烧黄鱼', description='红烧黄鱼，肉质鲜嫩', price=68.0, category_id=fish_category.id, preparation_time=18),
                MenuItem(name='清蒸多宝鱼', description='清蒸多宝鱼，肉质细嫩', price=158.0, category_id=fish_category.id, preparation_time=22),
                MenuItem(name='香煎银鳕鱼', description='香煎银鳕鱼，外焦内嫩', price=138.0, category_id=fish_category.id, preparation_time=15),
                MenuItem(name='蒸蛋羹鱼片', description='蒸蛋羹鱼片，嫩滑营养', price=48.0, category_id=fish_category.id, preparation_time=20),
            ])

        # 烧烤类 (15种)
        if bbq_category:
            menu_items.extend([
                MenuItem(name='烤羊肉串', description='新疆烤羊肉串，香嫩多汁', price=48.0, category_id=bbq_category.id, preparation_time=12),
                MenuItem(name='烤牛肉串', description='烤牛肉串，肉质鲜美', price=52.0, category_id=bbq_category.id, preparation_time=12),
                MenuItem(name='烤鸡翅', description='蜜汁烤鸡翅，外焦内嫩', price=38.0, category_id=bbq_category.id, preparation_time=15),
                MenuItem(name='烤鸡腿', description='香烤鸡腿，肉质鲜嫩', price=42.0, category_id=bbq_category.id, preparation_time=18),
                MenuItem(name='烤排骨', description='蜜汁烤排骨，香甜可口', price=68.0, category_id=bbq_category.id, preparation_time=25),
                MenuItem(name='烤鱿鱼', description='炭火烤鱿鱼，鲜香Q弹', price=58.0, category_id=bbq_category.id, preparation_time=10),
                MenuItem(name='烤扇贝', description='蒜蓉烤扇贝，鲜美可口', price=78.0, category_id=bbq_category.id, preparation_time=8),
                MenuItem(name='烤生蚝', description='炭火烤生蚝，鲜甜多汁', price=68.0, category_id=bbq_category.id, preparation_time=8),
                MenuItem(name='烤茄子', description='烤茄子配蒜泥，香嫩可口', price=28.0, category_id=bbq_category.id, preparation_time=12),
                MenuItem(name='烤韭菜', description='烤韭菜，香味浓郁', price=18.0, category_id=bbq_category.id, preparation_time=8),
                MenuItem(name='烤玉米', description='烤玉米，香甜可口', price=15.0, category_id=bbq_category.id, preparation_time=15),
                MenuItem(name='烤土豆', description='烤土豆片，外酥内糯', price=22.0, category_id=bbq_category.id, preparation_time=20),
                MenuItem(name='烤馒头片', description='烤馒头片，香脆可口', price=12.0, category_id=bbq_category.id, preparation_time=5),
                MenuItem(name='烤鸭舌', description='烤鸭舌，Q弹有嚼劲', price=38.0, category_id=bbq_category.id, preparation_time=10),
                MenuItem(name='烤鸡心', description='烤鸡心，嫩滑鲜美', price=32.0, category_id=bbq_category.id, preparation_time=8),
            ])

        # 火锅类 (10种)
        if hotpot_category:
            menu_items.extend([
                MenuItem(name='四川麻辣火锅', description='正宗四川麻辣火锅，麻辣鲜香', price=128.0, category_id=hotpot_category.id, preparation_time=30),
                MenuItem(name='重庆老火锅', description='重庆老火锅，麻辣过瘾', price=138.0, category_id=hotpot_category.id, preparation_time=30),
                MenuItem(name='清汤火锅', description='清汤火锅，清淡养生', price=88.0, category_id=hotpot_category.id, preparation_time=25),
                MenuItem(name='番茄火锅', description='番茄火锅，酸甜开胃', price=98.0, category_id=hotpot_category.id, preparation_time=25),
                MenuItem(name='菌菇火锅', description='菌菇火锅，鲜美营养', price=108.0, category_id=hotpot_category.id, preparation_time=30),
                MenuItem(name='海鲜火锅', description='海鲜火锅，鲜甜可口', price=168.0, category_id=hotpot_category.id, preparation_time=25),
                MenuItem(name='羊蝎子火锅', description='羊蝎子火锅，滋补暖胃', price=148.0, category_id=hotpot_category.id, preparation_time=45),
                MenuItem(name='鸳鸯火锅', description='鸳鸯火锅，一锅两味', price=118.0, category_id=hotpot_category.id, preparation_time=30),
                MenuItem(name='酸菜白肉火锅', description='东北酸菜白肉火锅，酸香开胃', price=128.0, category_id=hotpot_category.id, preparation_time=35),
                MenuItem(name='椰子鸡火锅', description='海南椰子鸡火锅，清甜滋补', price=138.0, category_id=hotpot_category.id, preparation_time=40),
            ])

        for item in menu_items:
            db.session.add(item)

    # 初始化原料库存
    if not Ingredient.query.first():
        ingredients = [
            Ingredient(name='牛肉-菲力', unit='kg', current_stock=50.0, min_stock=10.0, unit_cost=80.0),
            Ingredient(name='牛肉-西冷', unit='kg', current_stock=40.0, min_stock=8.0, unit_cost=60.0),
            Ingredient(name='土豆', unit='kg', current_stock=100.0, min_stock=20.0, unit_cost=5.0),
            Ingredient(name='洋葱', unit='kg', current_stock=30.0, min_stock=5.0, unit_cost=3.0),
        ]
        for ingredient in ingredients:
            db.session.add(ingredient)

    db.session.commit()

app = create_app()

if __name__ == '__main__':
    app.run(debug=True)
