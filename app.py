from flask import Flask, render_template, request, redirect, url_for, jsonify, session
from datetime import datetime, timedelta
import random
import json
import os

app = Flask(__name__)
app.secret_key = 'steakhouse_secret_key_2023'
app.config['TEMPLATES_AUTO_RELOAD'] = True

# 数据文件路径
DATA_DIR = 'data'
if not os.path.exists(DATA_DIR):
    os.makedirs(DATA_DIR)


# 加载数据
def load_data(filename):
    filepath = os.path.join(DATA_DIR, filename)
    if os.path.exists(filepath):
        with open(filepath, 'r') as f:
            return json.load(f)
    return []


# 保存数据
def save_data(data, filename):
    filepath = os.path.join(DATA_DIR, filename)
    with open(filepath, 'w') as f:
        json.dump(data, f, indent=2)


# 初始化数据
def init_data():
    # 库存数据
    if not os.path.exists(os.path.join(DATA_DIR, 'ingredients.json')):
        ingredients = [
            {"id": 1, "name": "菲力牛排", "quantity": 50, "threshold": 10, "unit": "份"},
            {"id": 2, "name": "肋眼牛排", "quantity": 40, "threshold": 10, "unit": "份"},
            {"id": 3, "name": "西冷牛排", "quantity": 45, "threshold": 10, "unit": "份"},
            {"id": 4, "name": "T骨牛排", "quantity": 35, "threshold": 10, "unit": "份"},
            {"id": 5, "name": "土豆", "quantity": 100, "threshold": 20, "unit": "kg"},
            {"id": 6, "name": "蔬菜沙拉", "quantity": 80, "threshold": 20, "unit": "份"},
            {"id": 7, "name": "红酒", "quantity": 30, "threshold": 5, "unit": "瓶"},
            {"id": 8, "name": "橄榄油", "quantity": 15, "threshold": 5, "unit": "L"},
        ]
        save_data(ingredients, 'ingredients.json')

    # 订单数据
    if not os.path.exists(os.path.join(DATA_DIR, 'orders.json')):
        orders = [
            {"id": 1, "table": "A1", "items": [{"name": "菲力牛排", "qty": 2}, {"name": "红酒", "qty": 1}],
             "total": 360, "status": "已完成", "time": "2023-05-15 18:30", "special_requests": "牛排五分熟"},
            {"id": 2, "table": "B2", "items": [{"name": "肋眼牛排", "qty": 1}, {"name": "沙拉", "qty": 2}],
             "total": 220, "status": "准备中", "time": "2023-05-15 19:15"},
            {"id": 3, "table": "C3", "items": [{"name": "西冷牛排", "qty": 3}, {"name": "红酒", "qty": 2}],
             "total": 480, "status": "已下单", "time": "2023-05-15 20:00"},
        ]
        save_data(orders, 'orders.json')

    # 预订数据
    if not os.path.exists(os.path.join(DATA_DIR, 'reservations.json')):
        reservations = [
            {"id": 1, "name": "张先生", "phone": "13800138000", "date": "2023-05-20",
             "time": "18:00", "people": 4, "table": "C3", "status": "已确认"},
            {"id": 2, "name": "李女士", "phone": "13900139000", "date": "2023-05-21",
             "time": "19:30", "people": 2, "table": "A2", "status": "待确认"},
            {"id": 3, "name": "王先生", "phone": "13700137000", "date": "2023-05-22",
             "time": "20:00", "people": 6, "table": "VIP1", "status": "已确认"},
        ]
        save_data(reservations, 'reservations.json')

    # 员工数据
    if not os.path.exists(os.path.join(DATA_DIR, 'employees.json')):
        employees = [
            {"id": 1, "name": "王小明", "position": "主厨", "schedule": [
                {"date": "2023-05-20", "start": "09:00", "end": "17:00", "status": "已打卡"},
                {"date": "2023-05-21", "start": "10:00", "end": "18:00", "status": "未打卡"}
            ]},
            {"id": 2, "name": "陈小红", "position": "服务员", "schedule": [
                {"date": "2023-05-20", "start": "10:00", "end": "18:00", "status": "已打卡"},
                {"date": "2023-05-21", "start": "11:00", "end": "19:00", "status": "未打卡"}
            ]},
            {"id": 3, "name": "赵小刚", "position": "经理", "schedule": [
                {"date": "2023-05-20", "start": "11:00", "end": "20:00", "status": "已打卡"},
                {"date": "2023-05-21", "start": "12:00", "end": "21:00", "status": "未打卡"}
            ]},
        ]
        save_data(employees, 'employees.json')

    # 客户数据
    if not os.path.exists(os.path.join(DATA_DIR, 'customers.json')):
        customers = [
            {"id": 1, "name": "张先生", "phone": "13800138000", "email": "<EMAIL>",
             "birthday": "1990-05-20", "points": 500, "preferences": "靠窗座位", "last_visit": "2023-05-15"},
            {"id": 2, "name": "李女士", "phone": "13900139000", "email": "<EMAIL>",
             "birthday": "1985-08-15", "points": 1200, "preferences": "安静区域", "last_visit": "2023-05-10"},
            {"id": 3, "name": "王先生", "phone": "13700137000", "email": "<EMAIL>",
             "birthday": "1978-11-30", "points": 800, "preferences": "包间", "last_visit": "2023-05-12"},
        ]
        save_data(customers, 'customers.json')


# 初始化数据
init_data()


# 登录路由
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']

        # 简单验证
        if username == 'admin' and password == 'steakhouse123':
            session['logged_in'] = True
            return redirect(url_for('index'))
        else:
            return render_template('login.html', error="用户名或密码错误")

    return render_template('login.html')


# 登出路由
@app.route('/logout')
def logout():
    session.pop('logged_in', None)
    return redirect(url_for('login'))


# 首页
@app.route('/')
def index():
    if not session.get('logged_in'):
        return redirect(url_for('login'))

    # 加载数据用于仪表盘
    ingredients = load_data('ingredients.json')
    orders = load_data('orders.json')
    reservations = load_data('reservations.json')
    employees = load_data('employees.json')
    customers = load_data('customers.json')

    # 低库存警报
    low_stock = [item for item in ingredients if item['quantity'] < item['threshold']]

    # 即将过生日的客户
    today = datetime.now().strftime("%m-%d")
    upcoming_birthdays = []
    for customer in customers:
        if customer['birthday']:
            bday = customer['birthday'][5:]  # 提取月份和日期
            if bday == today:
                upcoming_birthdays.append(customer)

    # 最新订单（取前5个）
    latest_orders = sorted(orders, key=lambda x: x['time'], reverse=True)[:5]

    # 今日预订
    today_str = datetime.now().strftime("%Y-%m-%d")
    today_reservations = [r for r in reservations if r['date'] == today_str]

    return render_template('index.html',
                           low_stock_count=len(low_stock),
                           orders_count=len(orders),
                           reservations_count=len(today_reservations),
                           employees_count=len(employees),
                           upcoming_birthdays=upcoming_birthdays,
                           latest_orders=latest_orders,
                           today_reservations=today_reservations)


# 库存管理
@app.route('/inventory')
def inventory():
    if not session.get('logged_in'):
        return redirect(url_for('login'))

    ingredients = load_data('ingredients.json')
    low_stock = [item for item in ingredients if item['quantity'] < item['threshold']]
    return render_template('inventory.html', ingredients=ingredients, low_stock=low_stock)


@app.route('/update_inventory', methods=['POST'])
def update_inventory():
    if not session.get('logged_in'):
        return redirect(url_for('login'))

    ingredients = load_data('ingredients.json')
    for item in ingredients:
        new_qty = int(request.form.get(f"qty_{item['id']}", item['quantity']))
        item['quantity'] = new_qty
    save_data(ingredients, 'ingredients.json')
    return redirect(url_for('inventory'))


# POS系统
@app.route('/pos')
def pos():
    if not session.get('logged_in'):
        return redirect(url_for('login'))

    orders = load_data('orders.json')
    menu_items = [
        {"id": 1, "name": "菲力牛排", "price": 180},
        {"id": 2, "name": "肋眼牛排", "price": 160},
        {"id": 3, "name": "西冷牛排", "price": 150},
        {"id": 4, "name": "T骨牛排", "price": 200},
        {"id": 5, "name": "蔬菜沙拉", "price": 30},
        {"id": 6, "name": "薯条", "price": 25},
        {"id": 7, "name": "红酒", "price": 80},
        {"id": 8, "name": "果汁", "price": 20},
    ]
    return render_template('pos.html', orders=orders, menu_items=menu_items)


@app.route('/create_order', methods=['POST'])
def create_order():
    if not session.get('logged_in'):
        return redirect(url_for('login'))

    orders = load_data('orders.json')
    menu_items = [
        {"id": 1, "name": "菲力牛排", "price": 180},
        {"id": 2, "name": "肋眼牛排", "price": 160},
        {"id": 3, "name": "西冷牛排", "price": 150},
        {"id": 4, "name": "T骨牛排", "price": 200},
        {"id": 5, "name": "蔬菜沙拉", "price": 30},
        {"id": 6, "name": "薯条", "price": 25},
        {"id": 7, "name": "红酒", "price": 80},
        {"id": 8, "name": "果汁", "price": 20},
    ]

    # 解析订单项
    items = []
    total = 0
    for key, value in request.form.items():
        if key.startswith('item_'):
            item_id = int(key.split('_')[1])
            qty = int(value)
            if qty > 0:
                item = next((i for i in menu_items if i['id'] == item_id), None)
                if item:
                    items.append({"name": item['name'], "qty": qty})
                    total += item['price'] * qty

    if not items:
        return jsonify(success=False, message="订单不能为空")

    new_order = {
        "id": max([o['id'] for o in orders], default=0) + 1,
        "table": request.form['table'],
        "items": items,
        "total": total,
        "status": "新订单",
        "time": datetime.now().strftime("%Y-%m-%d %H:%M"),
        "special_requests": request.form.get('requests', '')
    }

    # 更新库存
    ingredients = load_data('ingredients.json')
    for item in new_order['items']:
        for ing in ingredients:
            if ing['name'] == item['name']:
                ing['quantity'] -= item['qty']
                break

    save_data(ingredients, 'ingredients.json')

    orders.append(new_order)
    save_data(orders, 'orders.json')
    return jsonify(success=True, order_id=new_order['id'])


@app.route('/update_order_status/<int:order_id>/<status>')
def update_order_status(order_id, status):
    if not session.get('logged_in'):
        return redirect(url_for('login'))

    orders = load_data('orders.json')
    for order in orders:
        if order['id'] == order_id:
            order['status'] = status
            break
    save_data(orders, 'orders.json')
    return redirect(url_for('pos'))


# 预订管理
@app.route('/reservations')
def reservations():
    if not session.get('logged_in'):
        return redirect(url_for('login'))

    reservations = load_data('reservations.json')
    return render_template('reservations.html', reservations=reservations)


@app.route('/create_reservation', methods=['POST'])
def create_reservation():
    if not session.get('logged_in'):
        return redirect(url_for('login'))

    reservations = load_data('reservations.json')

    # 桌位分配逻辑
    tables = ['A1', 'A2', 'B1', 'B2', 'C1', 'C2', 'C3', 'VIP1', 'VIP2']
    people = int(request.form['people'])

    # 根据人数选择合适的桌位
    if people <= 2:
        table_options = ['A1', 'A2', 'B1', 'B2']
    elif people <= 4:
        table_options = ['C1', 'C2', 'C3']
    else:
        table_options = ['VIP1', 'VIP2']

    # 检查该时段是否已有预订
    existing_reservations = [r for r in reservations
                             if r['date'] == request.form['date']
                             and r['time'] == request.form['time']]

    occupied_tables = [r['table'] for r in existing_reservations]
    available_tables = [t for t in table_options if t not in occupied_tables]

    if available_tables:
        table = random.choice(available_tables)
    else:
        # 如果没有合适桌位，分配第一个可用桌位
        all_tables = set(tables)
        occupied = set(occupied_tables)
        available = list(all_tables - occupied)
        table = available[0] if available else "待分配"

    new_reservation = {
        "id": max([r['id'] for r in reservations], default=0) + 1,
        "name": request.form['name'],
        "phone": request.form['phone'],
        "date": request.form['date'],
        "time": request.form['time'],
        "people": people,
        "table": table,
        "status": "待确认"
    }

    reservations.append(new_reservation)
    save_data(reservations, 'reservations.json')
    return redirect(url_for('reservations'))


@app.route('/update_reservation_status/<int:res_id>/<status>')
def update_reservation_status(res_id, status):
    if not session.get('logged_in'):
        return redirect(url_for('login'))

    reservations = load_data('reservations.json')
    for res in reservations:
        if res['id'] == res_id:
            res['status'] = status
            break
    save_data(reservations, 'reservations.json')
    return redirect(url_for('reservations'))


# 员工管理
@app.route('/staff')
def staff():
    if not session.get('logged_in'):
        return redirect(url_for('login'))

    employees = load_data('employees.json')
    return render_template('staff.html', employees=employees)


@app.route('/add_schedule', methods=['POST'])
def add_schedule():
    if not session.get('logged_in'):
        return redirect(url_for('login'))

    employees = load_data('employees.json')
    emp_id = int(request.form['employee_id'])

    for emp in employees:
        if emp['id'] == emp_id:
            new_schedule = {
                "date": request.form['date'],
                "start": request.form['start'],
                "end": request.form['end'],
                "status": "未打卡"
            }
            emp['schedule'].append(new_schedule)
            break

    save_data(employees, 'employees.json')
    return redirect(url_for('staff'))


# 客户管理
@app.route('/customers')
def customers():
    if not session.get('logged_in'):
        return redirect(url_for('login'))

    customers = load_data('customers.json')

    # 计算即将过生日的客户
    today = datetime.now()
    next_week = today + timedelta(days=7)
    upcoming_birthdays = []

    for customer in customers:
        if customer['birthday']:
            bday = datetime.strptime(customer['birthday'], "%Y-%m-%d")
            bday_this_year = bday.replace(year=today.year)
            if today <= bday_this_year <= next_week:
                upcoming_birthdays.append(customer)

    return render_template('customers.html', customers=customers, upcoming_birthdays=upcoming_birthdays)


@app.route('/add_points/<int:customer_id>', methods=['POST'])
def add_points(customer_id):
    if not session.get('logged_in'):
        return redirect(url_for('login'))

    customers = load_data('customers.json')
    points = int(request.form['points'])

    for customer in customers:
        if customer['id'] == customer_id:
            customer['points'] += points
            break

    save_data(customers, 'customers.json')
    return redirect(url_for('customers'))


@app.route('/send_promotion/<int:customer_id>')
def send_promotion(customer_id):
    if not session.get('logged_in'):
        return redirect(url_for('login'))

    # 在实际应用中这里会发送邮件或短信
    return redirect(url_for('customers'))


if __name__ == '__main__':
    app.run(debug=True)
if __name__ == '__main__':
        app.run(debug=True, port=5001)  # 使用5001端口