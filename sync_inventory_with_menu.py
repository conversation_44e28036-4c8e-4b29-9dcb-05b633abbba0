#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
同步库存管理与菜品类型 - 牛排餐厅专用
"""

from app import app, db
from models import Ingredient, Category

def sync_inventory_with_menu():
    with app.app_context():
        # 删除现有库存
        Ingredient.query.delete()
        
        # 牛排餐厅专业原料库存
        ingredients = [
            # 牛肉类原料
            Ingredient(name='澳洲菲力牛排', unit='块', current_stock=50, min_stock=10, unit_cost=45.0, supplier='澳洲牛肉供应商'),
            Ingredient(name='澳洲西冷牛排', unit='块', current_stock=60, min_stock=15, unit_cost=35.0, supplier='澳洲牛肉供应商'),
            Ingredient(name='澳洲肋眼牛排', unit='块', current_stock=45, min_stock=12, unit_cost=42.0, supplier='澳洲牛肉供应商'),
            Ingredient(name='美国T骨牛排', unit='块', current_stock=30, min_stock=8, unit_cost=55.0, supplier='美国牛肉供应商'),
            Ingredient(name='美国纽约客牛排', unit='块', current_stock=35, min_stock=10, unit_cost=48.0, supplier='美国牛肉供应商'),
            Ingredient(name='日本和牛A5菲力', unit='块', current_stock=8, min_stock=2, unit_cost=180.0, supplier='日本和牛进口商'),
            Ingredient(name='日本和牛A5西冷', unit='块', current_stock=6, min_stock=2, unit_cost=200.0, supplier='日本和牛进口商'),
            Ingredient(name='澳洲和牛M9菲力', unit='块', current_stock=12, min_stock=3, unit_cost=120.0, supplier='澳洲和牛供应商'),
            Ingredient(name='澳洲和牛M9肋眼', unit='块', current_stock=10, min_stock=3, unit_cost=135.0, supplier='澳洲和牛供应商'),
            Ingredient(name='美国安格斯黑牛', unit='块', current_stock=25, min_stock=8, unit_cost=85.0, supplier='美国安格斯供应商'),
            Ingredient(name='干式熟成牛排', unit='块', current_stock=15, min_stock=5, unit_cost=95.0, supplier='熟成牛肉专家'),
            Ingredient(name='战斧牛排', unit='块', current_stock=12, min_stock=3, unit_cost=150.0, supplier='特殊部位供应商'),
            Ingredient(name='牛小排', unit='公斤', current_stock=20, min_stock=5, unit_cost=65.0, supplier='牛肉批发商'),
            Ingredient(name='牛仔骨', unit='块', current_stock=18, min_stock=5, unit_cost=75.0, supplier='美式牛肉供应商'),
            
            # 海鲜类原料
            Ingredient(name='波士顿龙虾', unit='只', current_stock=25, min_stock=8, unit_cost=85.0, supplier='海鲜进口商'),
            Ingredient(name='阿拉斯加帝王蟹', unit='公斤', current_stock=15, min_stock=5, unit_cost=180.0, supplier='阿拉斯加海鲜'),
            Ingredient(name='挪威三文鱼', unit='公斤', current_stock=30, min_stock=10, unit_cost=45.0, supplier='挪威海鲜'),
            Ingredient(name='智利海鲈鱼', unit='公斤', current_stock=20, min_stock=8, unit_cost=38.0, supplier='智利海鲜'),
            Ingredient(name='新鲜扇贝', unit='公斤', current_stock=12, min_stock=5, unit_cost=55.0, supplier='本地海鲜市场'),
            Ingredient(name='法国生蚝', unit='打', current_stock=20, min_stock=8, unit_cost=25.0, supplier='法国海鲜进口'),
            Ingredient(name='阿拉斯加银鳕鱼', unit='公斤', current_stock=18, min_stock=6, unit_cost=68.0, supplier='阿拉斯加海鲜'),
            Ingredient(name='大虾', unit='公斤', current_stock=25, min_stock=10, unit_cost=35.0, supplier='本地海鲜市场'),
            
            # 高端食材
            Ingredient(name='法国鹅肝', unit='块', current_stock=8, min_stock=2, unit_cost=120.0, supplier='法国食材进口商'),
            Ingredient(name='俄罗斯鱼子酱', unit='罐', current_stock=5, min_stock=1, unit_cost=200.0, supplier='奢华食材供应商'),
            Ingredient(name='黑松露', unit='克', current_stock=200, min_stock=50, unit_cost=8.0, supplier='意大利松露供应商'),
            Ingredient(name='白松露', unit='克', current_stock=100, min_stock=25, unit_cost=15.0, supplier='意大利松露供应商'),
            Ingredient(name='帕尔马火腿', unit='公斤', current_stock=8, min_stock=2, unit_cost=85.0, supplier='意大利食材进口'),
            Ingredient(name='帕玛森芝士', unit='公斤', current_stock=10, min_stock=3, unit_cost=45.0, supplier='意大利芝士进口'),
            
            # 蔬菜类
            Ingredient(name='新鲜芦笋', unit='公斤', current_stock=15, min_stock=5, unit_cost=18.0, supplier='有机蔬菜农场'),
            Ingredient(name='嫩菠菜', unit='公斤', current_stock=20, min_stock=8, unit_cost=12.0, supplier='有机蔬菜农场'),
            Ingredient(name='混合蘑菇', unit='公斤', current_stock=25, min_stock=10, unit_cost=22.0, supplier='蘑菇专业供应商'),
            Ingredient(name='芝麻菜', unit='公斤', current_stock=12, min_stock=5, unit_cost=15.0, supplier='有机蔬菜农场'),
            Ingredient(name='新鲜玉米', unit='根', current_stock=50, min_stock=20, unit_cost=3.0, supplier='本地农场'),
            Ingredient(name='红薯', unit='公斤', current_stock=30, min_stock=10, unit_cost=8.0, supplier='本地农场'),
            Ingredient(name='土豆', unit='公斤', current_stock=100, min_stock=30, unit_cost=5.0, supplier='本地农场'),
            Ingredient(name='胡萝卜', unit='公斤', current_stock=40, min_stock=15, unit_cost=6.0, supplier='本地农场'),
            Ingredient(name='西兰花', unit='公斤', current_stock=25, min_stock=10, unit_cost=10.0, supplier='有机蔬菜农场'),
            Ingredient(name='洋葱', unit='公斤', current_stock=50, min_stock=20, unit_cost=4.0, supplier='本地农场'),
            Ingredient(name='大蒜', unit='公斤', current_stock=15, min_stock=5, unit_cost=12.0, supplier='本地农场'),
            Ingredient(name='新鲜番茄', unit='公斤', current_stock=30, min_stock=12, unit_cost=8.0, supplier='有机蔬菜农场'),
            
            # 调料香料
            Ingredient(name='海盐', unit='公斤', current_stock=20, min_stock=5, unit_cost=15.0, supplier='法国海盐进口'),
            Ingredient(name='黑胡椒', unit='公斤', current_stock=5, min_stock=2, unit_cost=35.0, supplier='香料进口商'),
            Ingredient(name='迷迭香', unit='公斤', current_stock=3, min_stock=1, unit_cost=25.0, supplier='香草供应商'),
            Ingredient(name='百里香', unit='公斤', current_stock=3, min_stock=1, unit_cost=28.0, supplier='香草供应商'),
            Ingredient(name='橄榄油', unit='升', current_stock=50, min_stock=15, unit_cost=25.0, supplier='意大利橄榄油进口'),
            Ingredient(name='黄油', unit='公斤', current_stock=30, min_stock=10, unit_cost=18.0, supplier='法国黄油进口'),
            Ingredient(name='淡奶油', unit='升', current_stock=40, min_stock=15, unit_cost=12.0, supplier='乳制品供应商'),
            Ingredient(name='红酒醋', unit='升', current_stock=10, min_stock=3, unit_cost=22.0, supplier='意大利醋进口'),
            Ingredient(name='柠檬', unit='公斤', current_stock=20, min_stock=8, unit_cost=8.0, supplier='水果供应商'),
            
            # 面包甜品原料
            Ingredient(name='法式面包', unit='个', current_stock=50, min_stock=20, unit_cost=8.0, supplier='法式面包房'),
            Ingredient(name='马斯卡彭芝士', unit='公斤', current_stock=8, min_stock=3, unit_cost=35.0, supplier='意大利芝士进口'),
            Ingredient(name='比利时巧克力', unit='公斤', current_stock=10, min_stock=4, unit_cost=45.0, supplier='比利时巧克力进口'),
            Ingredient(name='香草豆', unit='根', current_stock=20, min_stock=8, unit_cost=15.0, supplier='香草进口商'),
            Ingredient(name='鸡蛋', unit='个', current_stock=200, min_stock=50, unit_cost=1.5, supplier='有机农场'),
            Ingredient(name='面粉', unit='公斤', current_stock=50, min_stock=20, unit_cost=6.0, supplier='面粉厂'),
            Ingredient(name='糖', unit='公斤', current_stock=30, min_stock=10, unit_cost=5.0, supplier='糖业公司'),
            
            # 酒水原料
            Ingredient(name='咖啡豆', unit='公斤', current_stock=20, min_stock=8, unit_cost=35.0, supplier='意大利咖啡进口'),
            Ingredient(name='茶叶', unit='公斤', current_stock=10, min_stock=4, unit_cost=25.0, supplier='茶叶进口商'),
            Ingredient(name='新鲜果汁', unit='升', current_stock=50, min_stock=20, unit_cost=8.0, supplier='果汁供应商'),
            Ingredient(name='气泡水', unit='瓶', current_stock=100, min_stock=30, unit_cost=3.0, supplier='饮料供应商'),
        ]
        
        # 添加所有原料
        for ingredient in ingredients:
            db.session.add(ingredient)
        
        db.session.commit()
        print(f"成功添加 {len(ingredients)} 种牛排餐厅专业原料！")
        
        # 统计各类原料
        categories = {}
        for ingredient in ingredients:
            supplier = ingredient.supplier
            if supplier not in categories:
                categories[supplier] = 0
            categories[supplier] += 1
        
        print("\n按供应商分类统计:")
        for supplier, count in categories.items():
            print(f"{supplier}: {count} 种原料")

if __name__ == '__main__':
    sync_inventory_with_menu()
