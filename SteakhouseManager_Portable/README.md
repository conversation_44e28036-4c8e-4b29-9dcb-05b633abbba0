# 🥩 牛排餐厅管理系统 - 便携版

## 📋 系统简介
这是一个完整的牛排餐厅管理系统，包含以下功能：
- POS收银系统 (31个桌位)
- 菜品管理 (88道西餐菜品饮品)
- 员工管理 (25名专业员工)
- 排班管理 (576个专业班次)
- 客户管理 (生日提醒、忠诚度报告)
- 库存管理 (56种西餐原料)
- 财务报表

## 🚀 使用方法

### 1. 启动系统
双击 `SteakhouseManager.exe` 启动系统

### 2. 访问系统
系统启动后会自动打开浏览器访问：http://1********:5000

### 3. 登录账号
**管理员账号：**
- 用户名：admin
- 密码：admin123

**专业员工账号：**
- 总经理：general_manager / gm123456
- 行政总厨：executive_chef / chef123456
- 首席服务员：head_waiter / head123456
- 侍酒师：sommelier / wine123456
- 首席调酒师：head_bartender / bar123456

### 4. 停止系统
在控制台窗口按 Ctrl+C 停止服务器

## 🎯 主要功能

### POS收银系统
- 31个桌位管理（2人桌、4人桌、6人桌、8人桌、VIP包间、吧台座位、露台座位）
- 88道菜品饮品选择
- 实时桌位状态管理
- 订单管理和结账

### 菜品管理
- 精选牛排系列（¥268-358）
- 顶级牛排系列（¥468-1288）
- 海鲜类、开胃菜、汤品、沙拉、配菜、甜品
- 威士忌、香槟、白酒、无酒精饮品
- 精品咖啡茶饮

### 员工管理
- 25名专业员工
- 管理层、厨房团队、前厅服务、吧台收银、后勤支持
- 专业职位划分和权限管理

### 排班管理
- 576个专业班次（30天）
- 周一到周日全覆盖
- 智能排班算法
- 差异化人员配置

## 💡 技术特点
- 无需安装Python环境
- 便携式设计，即开即用
- 完整的数据库支持
- 现代化Web界面
- 响应式设计

## 📞 技术支持
如有问题，请联系技术支持。

---
© 2024 牛排餐厅管理系统 - 便携版
