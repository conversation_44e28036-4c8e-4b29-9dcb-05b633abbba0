<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Find and explore upcoming sports events, tournaments, and championships from around the world.">
    <meta name="keywords" content="sports, events, tournaments, championships, football, basketball, tennis, swimming, athletics, golf, formula 1">
    <title>体育赛事 - 首页</title>
    <!-- Favicon -->
    <link rel="icon" href="/static/favicon.ico?q=1747998751" type="image/x-icon">
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/static/css/main.css?q=1747309699">
    <link rel="stylesheet" href="/static/css/custom.css?q=1747728358">
    <!-- Integrated Featured Events CSS -->
    <link rel="stylesheet" href="/static/css/integrated_featured_events.css?q=1748252361">
    
<style>
/* 单个赛事全屏滚动样式 */
.featured-events-scroll-container {
    position: relative;
    overflow: hidden;
    border-radius: 1rem;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.featured-events-scroll {
    display: flex;
    overflow-x: auto;
    scroll-behavior: smooth;
    scroll-snap-type: x mandatory;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.featured-events-scroll::-webkit-scrollbar {
    display: none;
}

.featured-event-card {
    flex: 0 0 100%;
    scroll-snap-align: start;
    background: white;
    overflow: hidden;
    min-height: 400px;
    display: flex;
    flex-direction: column;
}

.featured-event-image {
    height: 280px;
    overflow: hidden;
    position: relative;
    border-radius: 1rem 1rem 0 0;
}

.featured-event-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
}

.featured-event-card:hover .featured-event-image img {
    transform: scale(1.05);
}

.featured-event-content {
    padding: 2rem;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.event-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1rem;
    line-height: 1.3;
}

.event-description {
    color: #6c757d;
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: 1.5rem;
    flex: 1;
}

.event-meta {
    margin-bottom: 1.5rem;
}

.event-meta-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
    font-size: 1rem;
    color: #495057;
}

.event-meta-item i {
    width: 24px;
    margin-right: 1rem;
    font-size: 1rem;
    color: #007bff;
}

.event-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.event-actions .btn {
    flex: 1;
    min-width: 150px;
    font-size: 1rem;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.event-category-badge {
    position: absolute;
    top: 1.5rem;
    left: 1.5rem;
    z-index: 2;
}

.event-category-badge .badge {
    font-size: 1rem;
    padding: 0.75rem 1.5rem;
    border-radius: 2rem;
    background: rgba(0,123,255,0.9);
    backdrop-filter: blur(10px);
    font-weight: 600;
}

.next-event-badge {
    position: absolute;
    top: 1.5rem;
    right: 1.5rem;
    z-index: 2;
}

.next-event-badge .badge {
    font-size: 0.9rem;
    padding: 0.6rem 1.2rem;
    border-radius: 1.5rem;
    background: rgba(255,193,7,0.95);
    backdrop-filter: blur(10px);
    font-weight: 600;
    color: #000;
}

/* 滚动导航样式 */
.scroll-navigation {
    margin-top: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}

.scroll-navigation .btn {
    padding: 0.75rem 1.5rem;
    border-radius: 2rem;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    border: 2px solid #007bff;
    min-width: 120px;
}

.scroll-navigation .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,123,255,0.3);
}

.scroll-indicator {
    font-size: 1rem;
    color: #6c757d;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    background: rgba(248,249,250,0.9);
    border-radius: 2rem;
    border: 2px solid #dee2e6;
    backdrop-filter: blur(10px);
}

.scroll-indicator .current-slide {
    color: #007bff;
    font-weight: 700;
    font-size: 1.1rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .featured-event-card {
        min-height: 350px;
    }

    .featured-event-image {
        height: 200px;
    }

    .featured-event-content {
        padding: 1.5rem;
    }

    .event-title {
        font-size: 1.25rem;
    }

    .event-description {
        font-size: 0.9rem;
    }

    .event-actions {
        flex-direction: column;
    }

    .event-actions .btn {
        flex: none;
        width: 100%;
    }
}
</style>

    <!-- Open Graph tags for social sharing -->
    <meta property="og:title" content="体育赛事">
    <meta property="og:description" content="Find and explore upcoming sports events, tournaments, and championships from around the world.">
    <meta property="og:image" content="http://localhost/static/images/og-image.jpg">
    <meta property="og:url" content="http://localhost/">
    <meta property="og:type" content="website">
</head>
<body>
    <!-- Skip to content link for accessibility -->
    <a href="#main-content" class="skip-link">Skip to content</a>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-trophy"></i> 体育赛事
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="/" aria-current="page">
                            <i class="fas fa-home me-1"></i> 首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link " href="/events" aria-current="">
                            <i class="fas fa-calendar-alt me-1"></i> 赛事
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="categoriesDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-list me-1"></i> 分类
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="categoriesDropdown">
                            <li><a class="dropdown-item" href="/events?category=1">足球</a></li>
                            <li><a class="dropdown-item" href="/events?category=2">篮球</a></li>
                            <li><a class="dropdown-item" href="/events?category=3">网球</a></li>
                            <li><a class="dropdown-item" href="/events?category=4">游泳</a></li>
                            <li><a class="dropdown-item" href="/events?category=5">田径</a></li>
                            <li><a class="dropdown-item" href="/events?category=6">高尔夫</a></li>
                            <li><a class="dropdown-item" href="/events?category=7">一级方程式</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Coming Soon">
                            <i class="fas fa-newspaper me-1"></i> News
                        </a>
                    </li>
                </ul>
                <div class="d-flex align-items-center">
                    <div class="search-container position-relative me-3">
                        <form class="search-form" action="/events" method="get">
                            <div class="input-group">
                                <input class="form-control search-autocomplete" type="search" name="q" placeholder="Search events" aria-label="Search events" autocomplete="off">
                                <button class="btn btn-primary" type="submit">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </form>
                        <div class="search-results position-absolute w-100 bg-white shadow-lg rounded-bottom p-2" style="display: none; z-index: 1000;"></div>
                    </div>
                    <ul class="navbar-nav">
                        <li class="nav-item">
                            <a class="nav-link" href="/login" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Login">
                                <i class="fas fa-sign-in-alt"></i>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/register" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Register">
                                <i class="fas fa-user-plus"></i>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Language Switcher -->
                <div class="navbar-nav ms-auto">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="languageDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-globe me-1"></i>
                            中文
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="languageDropdown">
                            <li>
                                <a class="dropdown-item " href="/set_language/en">
                                    <i class="fas fa-flag-usa me-2"></i> English
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item active" href="/set_language/zh">
                                    <i class="fas fa-flag me-2" style="color: #de2910;"></i> 中文
                                </a>
                            </li>
                        </ul>
                    </li>
                </div>
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    
        
    

    <!-- Main Content -->
    <main id="main-content" class="py-4">
        
<div class="container">
    <!-- Hero Section -->
    <section class="hero-section rounded-lg shadow-lg mb-5">
        <div class="container py-5">
            <div class="row py-lg-5 align-items-center">
                <div class="col-lg-7 col-md-8 mx-auto text-center">
                    <h1 class="fw-bold mb-4 animate-on-scroll">发现精彩体育赛事</h1>
                    <p class="lead mb-4 animate-on-scroll">寻找和探索来自世界各地最激动人心的体育赛事</p>
                    <div class="d-flex justify-content-center animate-on-scroll">
                        <form class="search-form w-100" action="/events" method="get">
                            <div class="input-group input-group-lg">
                                <input class="form-control" type="search" name="q" placeholder="搜索赛事..." aria-label="Search">
                                <button class="btn btn-primary" type="submit">
                                    <i class="fas fa-search me-2"></i>搜索
                                </button>
                            </div>
                        </form>
                    </div>
                    <div class="mt-4 d-flex justify-content-center gap-2 animate-on-scroll">
                        <span class="badge bg-primary">足球</span>
                        <span class="badge bg-primary">篮球</span>
                        <span class="badge bg-primary">网球</span>
                        <span class="badge bg-primary">一级方程式</span>
                        <span class="badge bg-primary">高尔夫</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 删除原有的轮播图精选赛事区域 -->

    <!-- Unified Featured Events Section -->
    <section class="mb-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="mb-0">精选赛事</h2>
            <a href="/events" class="btn btn-outline-primary btn-sm">
                查看全部 <i class="fas fa-arrow-right ms-1"></i>
            </a>
        </div>

        <!-- Unified Horizontal Scrolling Container -->
        <div class="featured-events-scroll-container">
            <div class="featured-events-scroll">
                
                    
                        <div class="featured-event-card">
                            <div class="featured-event-image">
                                <img src="/static/images/homepage/featured/fifa_world_cup_featured.jpg" alt="FIFA World Cup 2026" class="img-fluid">
                                <div class="event-category-badge">
                                    <span class="badge bg-primary">足球</span>
                                </div>
                                
                                    <div class="next-event-badge">
                                        <span class="badge bg-warning text-dark">下一个重大赛事</span>
                                    </div>
                                
                            </div>
                            <div class="featured-event-content">
                                <h4 class="event-title">FIFA世界杯2026</h4>
                                <p class="event-description">FIFA世界杯2026是世界上最大的足球锦标赛，由美国、加拿大和墨西哥联合主办。这将是首届有48支球队参赛并由三个不同国家联合主办的世界杯。</p>
                                <div class="event-meta">
                                    <div class="event-meta-item">
                                        <i class="fas fa-map-marker-alt text-primary"></i>
                                        <span>美国、加拿大、墨西哥</span>
                                    </div>
                                    <div class="event-meta-item">
                                        <i class="fas fa-calendar-alt text-primary"></i>
                                        <span>2026年六月10日</span>
                                    </div>
                                    
                                        <div class="event-meta-item">
                                            <i class="fas fa-clock text-warning"></i>
                                            <span class="countdown-text" data-event-date="2026-06-10">
                                                即将开始
                                            </span>
                                        </div>
                                    
                                </div>
                                <div class="event-actions">
                                    <a href="/events/1" class="btn btn-primary">
                                        <i class="fas fa-info-circle me-1"></i> 查看详情
                                    </a>
                                    <a href="/events/1" class="btn btn-outline-secondary">
                                        <i class="fas fa-ticket-alt me-1"></i> 获取门票
                                    </a>
                                </div>
                            </div>
                        </div>
                    
                        <div class="featured-event-card">
                            <div class="featured-event-image">
                                <img src="/static/images/homepage/featured/champions_league_featured.jpg" alt="UEFA Champions League Final 2025" class="img-fluid">
                                <div class="event-category-badge">
                                    <span class="badge bg-primary">足球</span>
                                </div>
                                
                            </div>
                            <div class="featured-event-content">
                                <h4 class="event-title">欧洲冠军联赛决赛2025</h4>
                                <p class="event-description">欧洲冠军联赛决赛是欧洲最具声望的俱乐部足球比赛，由锦标赛中的两支最佳球队参加。这场比赛决定欧洲冠军联赛的冠军，这是欧洲足球的顶级俱乐部赛事。</p>
                                <div class="event-meta">
                                    <div class="event-meta-item">
                                        <i class="fas fa-map-marker-alt text-primary"></i>
                                        <span>德国慕尼黑</span>
                                    </div>
                                    <div class="event-meta-item">
                                        <i class="fas fa-calendar-alt text-primary"></i>
                                        <span>2025年五月31日</span>
                                    </div>
                                    
                                </div>
                                <div class="event-actions">
                                    <a href="/events/2" class="btn btn-primary">
                                        <i class="fas fa-info-circle me-1"></i> 查看详情
                                    </a>
                                    <a href="/events/2" class="btn btn-outline-secondary">
                                        <i class="fas fa-ticket-alt me-1"></i> 获取门票
                                    </a>
                                </div>
                            </div>
                        </div>
                    
                        <div class="featured-event-card">
                            <div class="featured-event-image">
                                <img src="/static/images/homepage/featured/nba_finals_featured.jpg" alt="NBA Finals 2025" class="img-fluid">
                                <div class="event-category-badge">
                                    <span class="badge bg-primary">篮球</span>
                                </div>
                                
                            </div>
                            <div class="featured-event-content">
                                <h4 class="event-title">NBA总决赛2025</h4>
                                <p class="event-description">NBA的冠军系列赛，由东部和西部联盟的最佳球队参加。NBA总决赛是美国职业篮球联赛(NBA)的年度冠军系列赛。</p>
                                <div class="event-meta">
                                    <div class="event-meta-item">
                                        <i class="fas fa-map-marker-alt text-primary"></i>
                                        <span>美国</span>
                                    </div>
                                    <div class="event-meta-item">
                                        <i class="fas fa-calendar-alt text-primary"></i>
                                        <span>2025年六月1日</span>
                                    </div>
                                    
                                </div>
                                <div class="event-actions">
                                    <a href="/events/6" class="btn btn-primary">
                                        <i class="fas fa-info-circle me-1"></i> 查看详情
                                    </a>
                                    <a href="/events/6" class="btn btn-outline-secondary">
                                        <i class="fas fa-ticket-alt me-1"></i> 获取门票
                                    </a>
                                </div>
                            </div>
                        </div>
                    
                        <div class="featured-event-card">
                            <div class="featured-event-image">
                                <img src="/static/images/homepage/featured/wimbledon_featured.jpg" alt="Wimbledon 2025" class="img-fluid">
                                <div class="event-category-badge">
                                    <span class="badge bg-primary">网球</span>
                                </div>
                                
                            </div>
                            <div class="featured-event-content">
                                <h4 class="event-title">温布尔登网球锦标赛2025</h4>
                                <p class="event-description">The oldest tennis tournament in the world and widely regarded as the most prestigious. Held at the All England Club in Wimbledon, London since 1877, it is one of the four Grand Slam tennis tournaments.</p>
                                <div class="event-meta">
                                    <div class="event-meta-item">
                                        <i class="fas fa-map-marker-alt text-primary"></i>
                                        <span>London, UK</span>
                                    </div>
                                    <div class="event-meta-item">
                                        <i class="fas fa-calendar-alt text-primary"></i>
                                        <span>2025年七月1日</span>
                                    </div>
                                    
                                </div>
                                <div class="event-actions">
                                    <a href="/events/9" class="btn btn-primary">
                                        <i class="fas fa-info-circle me-1"></i> 查看详情
                                    </a>
                                    <a href="/events/9" class="btn btn-outline-secondary">
                                        <i class="fas fa-ticket-alt me-1"></i> 获取门票
                                    </a>
                                </div>
                            </div>
                        </div>
                    
                        <div class="featured-event-card">
                            <div class="featured-event-image">
                                <img src="/static/images/homepage/featured/olympic_swimming_featured.jpg" alt="Olympic Swimming 2028" class="img-fluid">
                                <div class="event-category-badge">
                                    <span class="badge bg-primary">游泳</span>
                                </div>
                                
                            </div>
                            <div class="featured-event-content">
                                <h4 class="event-title">奥运会游泳比赛2028</h4>
                                <p class="event-description">The swimming competitions at the 2028 Summer Olympics in Los Angeles will feature various events across different strokes and distances. Olympic swimming is one of the most popular and widely watched events at the Summer Games.</p>
                                <div class="event-meta">
                                    <div class="event-meta-item">
                                        <i class="fas fa-map-marker-alt text-primary"></i>
                                        <span>美国洛杉矶</span>
                                    </div>
                                    <div class="event-meta-item">
                                        <i class="fas fa-calendar-alt text-primary"></i>
                                        <span>2028年七月21日</span>
                                    </div>
                                    
                                </div>
                                <div class="event-actions">
                                    <a href="/events/14" class="btn btn-primary">
                                        <i class="fas fa-info-circle me-1"></i> 查看详情
                                    </a>
                                    <a href="/events/14" class="btn btn-outline-secondary">
                                        <i class="fas fa-ticket-alt me-1"></i> 获取门票
                                    </a>
                                </div>
                            </div>
                        </div>
                    
                        <div class="featured-event-card">
                            <div class="featured-event-image">
                                <img src="/static/images/homepage/featured/masters_golf_featured.jpg" alt="The Masters 2026" class="img-fluid">
                                <div class="event-category-badge">
                                    <span class="badge bg-primary">高尔夫</span>
                                </div>
                                
                            </div>
                            <div class="featured-event-content">
                                <h4 class="event-title">美国大师赛2026</h4>
                                <p class="event-description">The Masters Tournament is one of the four major championships in professional golf. Scheduled for the first full week of April, it is the first major of the year. The Masters is held annually at Augusta National Golf Club in Augusta, Georgia.</p>
                                <div class="event-meta">
                                    <div class="event-meta-item">
                                        <i class="fas fa-map-marker-alt text-primary"></i>
                                        <span>Augusta, Georgia, USA</span>
                                    </div>
                                    <div class="event-meta-item">
                                        <i class="fas fa-calendar-alt text-primary"></i>
                                        <span>2026年四月9日</span>
                                    </div>
                                    
                                </div>
                                <div class="event-actions">
                                    <a href="/events/17" class="btn btn-primary">
                                        <i class="fas fa-info-circle me-1"></i> 查看详情
                                    </a>
                                    <a href="/events/17" class="btn btn-outline-secondary">
                                        <i class="fas fa-ticket-alt me-1"></i> 获取门票
                                    </a>
                                </div>
                            </div>
                        </div>
                    
                        <div class="featured-event-card">
                            <div class="featured-event-image">
                                <img src="/static/images/homepage/featured/monaco_gp_featured.jpg" alt="Formula 1 Monaco Grand Prix 2026" class="img-fluid">
                                <div class="event-category-badge">
                                    <span class="badge bg-primary">formula 1</span>
                                </div>
                                
                            </div>
                            <div class="featured-event-content">
                                <h4 class="event-title">一级方程式摩纳哥大奖赛2026</h4>
                                <p class="event-description">The Monaco Grand Prix is a Formula One motor racing event held annually on the Circuit de Monaco in the streets of Monte Carlo. It is widely considered to be one of the most prestigious automobile races in the world, alongside the Indianapolis 500 and the 24 Hours of Le Mans.</p>
                                <div class="event-meta">
                                    <div class="event-meta-item">
                                        <i class="fas fa-map-marker-alt text-primary"></i>
                                        <span>Monte Carlo, Monaco</span>
                                    </div>
                                    <div class="event-meta-item">
                                        <i class="fas fa-calendar-alt text-primary"></i>
                                        <span>2026年五月28日</span>
                                    </div>
                                    
                                </div>
                                <div class="event-actions">
                                    <a href="/events/20" class="btn btn-primary">
                                        <i class="fas fa-info-circle me-1"></i> 查看详情
                                    </a>
                                    <a href="/events/20" class="btn btn-outline-secondary">
                                        <i class="fas fa-ticket-alt me-1"></i> 获取门票
                                    </a>
                                </div>
                            </div>
                        </div>
                    
                
            </div>
        </div>

        <!-- Enhanced Scroll Navigation -->
        <div class="scroll-navigation text-center mt-3">
            <button class="btn btn-outline-primary btn-sm scroll-left">
                <i class="fas fa-chevron-left"></i> 上一个
            </button>
            <span class="scroll-indicator mx-3">
                <span class="current-slide">1</span> / <span class="total-slides">7</span>
            </span>
            <button class="btn btn-outline-primary btn-sm scroll-right">
                <i class="fas fa-chevron-right"></i> 下一个
            </button>
        </div>
    </section>

    <!-- Categories Section -->
    <section class="mb-5" id="categories">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="mb-0">按类别浏览</h2>
        </div>
        <div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 row-cols-lg-4 g-4">
            
                <div class="col animate-on-scroll" style="animation-delay: 0.0s">
                    <div class="card h-100 category-card hover-shadow">
                        <img src="/static/images/homepage/categories/football_category.jpg"
                             class="card-img-top category-image"
                             alt="Football"
                             style="height: 220px; object-fit: cover; display: block !important; opacity: 1 !important; visibility: visible !important; border-radius: 0.5rem 0.5rem 0 0;"
                             onerror="console.log('Category image failed:', this.src); this.style.background='linear-gradient(135deg, #f8f9fa, #e9ecef)'; this.style.display='block';"
                             onload="console.log('Category image loaded:', this.src); this.style.display='block'; this.style.opacity='1';">
                        <div class="card-body text-center">
                            <h5 class="card-title">足球</h5>
                            <p class="card-text text-muted">探索赛事</p>
                            <a href="/events?category=1" class="btn btn-outline-primary w-100">
                                <i class="fas fa-list me-1"></i> 赛事
                            </a>
                        </div>
                    </div>
                </div>
            
                <div class="col animate-on-scroll" style="animation-delay: 0.1s">
                    <div class="card h-100 category-card hover-shadow">
                        <img src="/static/images/homepage/categories/basketball_category.jpg"
                             class="card-img-top category-image"
                             alt="Basketball"
                             style="height: 220px; object-fit: cover; display: block !important; opacity: 1 !important; visibility: visible !important; border-radius: 0.5rem 0.5rem 0 0;"
                             onerror="console.log('Category image failed:', this.src); this.style.background='linear-gradient(135deg, #f8f9fa, #e9ecef)'; this.style.display='block';"
                             onload="console.log('Category image loaded:', this.src); this.style.display='block'; this.style.opacity='1';">
                        <div class="card-body text-center">
                            <h5 class="card-title">篮球</h5>
                            <p class="card-text text-muted">探索赛事</p>
                            <a href="/events?category=2" class="btn btn-outline-primary w-100">
                                <i class="fas fa-list me-1"></i> 赛事
                            </a>
                        </div>
                    </div>
                </div>
            
                <div class="col animate-on-scroll" style="animation-delay: 0.2s">
                    <div class="card h-100 category-card hover-shadow">
                        <img src="/static/images/homepage/categories/tennis_category.jpg"
                             class="card-img-top category-image"
                             alt="Tennis"
                             style="height: 220px; object-fit: cover; display: block !important; opacity: 1 !important; visibility: visible !important; border-radius: 0.5rem 0.5rem 0 0;"
                             onerror="console.log('Category image failed:', this.src); this.style.background='linear-gradient(135deg, #f8f9fa, #e9ecef)'; this.style.display='block';"
                             onload="console.log('Category image loaded:', this.src); this.style.display='block'; this.style.opacity='1';">
                        <div class="card-body text-center">
                            <h5 class="card-title">网球</h5>
                            <p class="card-text text-muted">探索赛事</p>
                            <a href="/events?category=3" class="btn btn-outline-primary w-100">
                                <i class="fas fa-list me-1"></i> 赛事
                            </a>
                        </div>
                    </div>
                </div>
            
                <div class="col animate-on-scroll" style="animation-delay: 0.30000000000000004s">
                    <div class="card h-100 category-card hover-shadow">
                        <img src="/static/images/homepage/categories/swimming_category.jpg"
                             class="card-img-top category-image"
                             alt="Swimming"
                             style="height: 220px; object-fit: cover; display: block !important; opacity: 1 !important; visibility: visible !important; border-radius: 0.5rem 0.5rem 0 0;"
                             onerror="console.log('Category image failed:', this.src); this.style.background='linear-gradient(135deg, #f8f9fa, #e9ecef)'; this.style.display='block';"
                             onload="console.log('Category image loaded:', this.src); this.style.display='block'; this.style.opacity='1';">
                        <div class="card-body text-center">
                            <h5 class="card-title">游泳</h5>
                            <p class="card-text text-muted">探索赛事</p>
                            <a href="/events?category=4" class="btn btn-outline-primary w-100">
                                <i class="fas fa-list me-1"></i> 赛事
                            </a>
                        </div>
                    </div>
                </div>
            
                <div class="col animate-on-scroll" style="animation-delay: 0.4s">
                    <div class="card h-100 category-card hover-shadow">
                        <img src="/static/images/homepage/categories/athletics_category.jpg"
                             class="card-img-top category-image"
                             alt="Athletics"
                             style="height: 220px; object-fit: cover; display: block !important; opacity: 1 !important; visibility: visible !important; border-radius: 0.5rem 0.5rem 0 0;"
                             onerror="console.log('Category image failed:', this.src); this.style.background='linear-gradient(135deg, #f8f9fa, #e9ecef)'; this.style.display='block';"
                             onload="console.log('Category image loaded:', this.src); this.style.display='block'; this.style.opacity='1';">
                        <div class="card-body text-center">
                            <h5 class="card-title">田径</h5>
                            <p class="card-text text-muted">探索赛事</p>
                            <a href="/events?category=5" class="btn btn-outline-primary w-100">
                                <i class="fas fa-list me-1"></i> 赛事
                            </a>
                        </div>
                    </div>
                </div>
            
                <div class="col animate-on-scroll" style="animation-delay: 0.5s">
                    <div class="card h-100 category-card hover-shadow">
                        <img src="/static/images/homepage/categories/golf_category.jpg"
                             class="card-img-top category-image"
                             alt="Golf"
                             style="height: 220px; object-fit: cover; display: block !important; opacity: 1 !important; visibility: visible !important; border-radius: 0.5rem 0.5rem 0 0;"
                             onerror="console.log('Category image failed:', this.src); this.style.background='linear-gradient(135deg, #f8f9fa, #e9ecef)'; this.style.display='block';"
                             onload="console.log('Category image loaded:', this.src); this.style.display='block'; this.style.opacity='1';">
                        <div class="card-body text-center">
                            <h5 class="card-title">高尔夫</h5>
                            <p class="card-text text-muted">探索赛事</p>
                            <a href="/events?category=6" class="btn btn-outline-primary w-100">
                                <i class="fas fa-list me-1"></i> 赛事
                            </a>
                        </div>
                    </div>
                </div>
            
                <div class="col animate-on-scroll" style="animation-delay: 0.6000000000000001s">
                    <div class="card h-100 category-card hover-shadow">
                        <img src="/static/images/homepage/categories/formula_1_category.jpg"
                             class="card-img-top category-image"
                             alt="Formula 1"
                             style="height: 220px; object-fit: cover; display: block !important; opacity: 1 !important; visibility: visible !important; border-radius: 0.5rem 0.5rem 0 0;"
                             onerror="console.log('Category image failed:', this.src); this.style.background='linear-gradient(135deg, #f8f9fa, #e9ecef)'; this.style.display='block';"
                             onload="console.log('Category image loaded:', this.src); this.style.display='block'; this.style.opacity='1';">
                        <div class="card-body text-center">
                            <h5 class="card-title">formula 1</h5>
                            <p class="card-text text-muted">探索赛事</p>
                            <a href="/events?category=7" class="btn btn-outline-primary w-100">
                                <i class="fas fa-list me-1"></i> 赛事
                            </a>
                        </div>
                    </div>
                </div>
            
        </div>

        <!-- Debug Info -->
        <div class="mt-4">
            <details>
                <summary class="btn btn-outline-secondary btn-sm">🔍 调试信息</summary>
                <div class="mt-3 p-3 bg-light rounded">
                    <h6>分类数量: 7</h6>
                    
                        <div class="small mb-2">
                            <strong>Football:</strong> /static/images/homepage/categories/football_category.jpg
                        </div>
                    
                        <div class="small mb-2">
                            <strong>Basketball:</strong> /static/images/homepage/categories/basketball_category.jpg
                        </div>
                    
                        <div class="small mb-2">
                            <strong>Tennis:</strong> /static/images/homepage/categories/tennis_category.jpg
                        </div>
                    
                        <div class="small mb-2">
                            <strong>Swimming:</strong> /static/images/homepage/categories/swimming_category.jpg
                        </div>
                    
                        <div class="small mb-2">
                            <strong>Athletics:</strong> /static/images/homepage/categories/athletics_category.jpg
                        </div>
                    
                        <div class="small mb-2">
                            <strong>Golf:</strong> /static/images/homepage/categories/golf_category.jpg
                        </div>
                    
                        <div class="small mb-2">
                            <strong>Formula 1:</strong> /static/images/homepage/categories/formula_1_category.jpg
                        </div>
                    
                    <div class="mt-3">
                        <a href="/debug/categories" target="_blank" class="btn btn-sm btn-info">查看分类数据</a>
                        <a href="/static/category_debug.html" target="_blank" class="btn btn-sm btn-warning">图片测试页面</a>
                    </div>
                </div>
            </details>
        </div>
    </section>

    <!-- Upcoming Events Section -->
    <section class="mb-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="mb-0">即将举行的赛事</h2>
            <a href="/events" class="btn btn-outline-primary btn-sm">
                查看全部 <i class="fas fa-arrow-right ms-1"></i>
            </a>
        </div>
        <div class="row">
            <div class="col-lg-8">
                <div class="card shadow-sm mb-4">
                    <div class="card-body p-0">
                        
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead>
                                        <tr>
                                            <th>赛事</th>
                                            <th>日期</th>
                                            <th>地点</th>
                                            <th>类别</th>
                                            <th></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <img src="/static/images/events/fifa_world_cup_2026.jpg" class="rounded me-2" width="40" height="40" alt="FIFA世界杯2026" style="object-fit: cover;">
                                                        <span>FIFA世界杯2026</span>
                                                    </div>
                                                </td>
                                                <td>2026年六月10日</td>
                                                <td>美国、加拿大、墨西哥</td>
                                                <td><span class="badge bg-primary">足球</span></td>
                                                <td>
                                                    <a href="/events/1" class="btn btn-sm btn-outline-primary">
                                                        查看详情
                                                    </a>
                                                </td>
                                            </tr>
                                        
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <img src="/static/images/events/uefa_champions_league_final_2025.jpg" class="rounded me-2" width="40" height="40" alt="欧洲冠军联赛决赛2025" style="object-fit: cover;">
                                                        <span>欧洲冠军联赛决赛2025</span>
                                                    </div>
                                                </td>
                                                <td>2025年五月31日</td>
                                                <td>德国慕尼黑</td>
                                                <td><span class="badge bg-primary">足球</span></td>
                                                <td>
                                                    <a href="/events/2" class="btn btn-sm btn-outline-primary">
                                                        查看详情
                                                    </a>
                                                </td>
                                            </tr>
                                        
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <img src="/static/images/events/nba_finals_2025.jpg" class="rounded me-2" width="40" height="40" alt="NBA总决赛2025" style="object-fit: cover;">
                                                        <span>NBA总决赛2025</span>
                                                    </div>
                                                </td>
                                                <td>2025年六月1日</td>
                                                <td>美国</td>
                                                <td><span class="badge bg-primary">篮球</span></td>
                                                <td>
                                                    <a href="/events/6" class="btn btn-sm btn-outline-primary">
                                                        查看详情
                                                    </a>
                                                </td>
                                            </tr>
                                        
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <img src="/static/images/events/wimbledon_2025.jpg" class="rounded me-2" width="40" height="40" alt="温布尔登网球锦标赛2025" style="object-fit: cover;">
                                                        <span>温布尔登网球锦标赛2025</span>
                                                    </div>
                                                </td>
                                                <td>2025年七月1日</td>
                                                <td>London, UK</td>
                                                <td><span class="badge bg-primary">网球</span></td>
                                                <td>
                                                    <a href="/events/9" class="btn btn-sm btn-outline-primary">
                                                        查看详情
                                                    </a>
                                                </td>
                                            </tr>
                                        
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <img src="/static/images/events/olympic_swimming_2028.jpg" class="rounded me-2" width="40" height="40" alt="奥运会游泳比赛2028" style="object-fit: cover;">
                                                        <span>奥运会游泳比赛2028</span>
                                                    </div>
                                                </td>
                                                <td>2028年七月21日</td>
                                                <td>美国洛杉矶</td>
                                                <td><span class="badge bg-primary">游泳</span></td>
                                                <td>
                                                    <a href="/events/14" class="btn btn-sm btn-outline-primary">
                                                        查看详情
                                                    </a>
                                                </td>
                                            </tr>
                                        
                                    </tbody>
                                </table>
                            </div>
                        
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">下一个重大赛事</h5>
                    </div>
                    <div class="card-body">
                        
                            
                            <h5 class="card-title">FIFA世界杯2026</h5>
                            <p class="card-text">FIFA世界杯2026是世界上最大的足球锦标赛，由美国、加拿大和墨西哥联合主办。这将是首届有48支球队参赛并由三个不同国家联合主办的世界杯。</p>
                            <div class="mb-3">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-calendar-alt text-primary me-2"></i>
                                    <span>June 10, 2026</span>
                                </div>
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-map-marker-alt text-primary me-2"></i>
                                    <span>美国、加拿大、墨西哥</span>
                                </div>
                            </div>
                            <a href="/events/1" class="btn btn-primary w-100 mt-3">
                                <i class="fas fa-ticket-alt me-1"></i> 查看赛事
                            </a>
                        
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

    </main>

    <!-- Back to top button -->
    <button class="back-to-top" aria-label="back_to_top">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- Dark mode toggle -->
    <button class="dark-mode-toggle" aria-label="Toggle dark mode">
        <i class="fas fa-moon"></i>
    </button>

    <!-- Cookie consent banner -->
    <div class="cookie-banner" style="display: none;">
        <p>We use cookies to enhance your experience. By continuing to visit this site you agree to our use of cookies.</p>
        <div class="btn-group">
            <button class="btn btn-primary accept-cookies">Accept</button>
            <button class="btn btn-outline-secondary decline-cookies">Decline</button>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-5 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 col-md-6 mb-4 mb-md-0">
                    <h5 class="mb-3"><i class="fas fa-trophy me-2"></i>体育赛事</h5>
                    <p class="mb-3">Your one-stop platform for sports event information and updates from around the world.</p>
                    <div class="social-links mb-4">
                        <a href="#" class="me-2 text-white" aria-label="Facebook"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="me-2 text-white" aria-label="Twitter"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="me-2 text-white" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="me-2 text-white" aria-label="YouTube"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>
                <div class="col-lg-2 col-md-6 mb-4 mb-md-0">
                    <h5 class="mb-3">quick_links</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="/" class="text-white"><i class="fas fa-chevron-right me-1"></i>首页</a></li>
                        <li class="mb-2"><a href="/events" class="text-white"><i class="fas fa-chevron-right me-1"></i>赛事</a></li>
                        <li class="mb-2"><a href="#" class="text-white"><i class="fas fa-chevron-right me-1"></i>关于</a></li>
                        <li class="mb-2"><a href="#" class="text-white"><i class="fas fa-chevron-right me-1"></i>联系我们</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-6 mb-4 mb-md-0">
                    <h5 class="mb-3">分类</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="/events?category=1" class="text-white"><i class="fas fa-chevron-right me-1"></i>足球</a></li>
                        <li class="mb-2"><a href="/events?category=2" class="text-white"><i class="fas fa-chevron-right me-1"></i>篮球</a></li>
                        <li class="mb-2"><a href="/events?category=3" class="text-white"><i class="fas fa-chevron-right me-1"></i>网球</a></li>
                        <li class="mb-2"><a href="/events?category=7" class="text-white"><i class="fas fa-chevron-right me-1"></i>一级方程式</a></li>
                    </ul>
                </div>
                <div class="col-lg-4 col-md-6 mb-4 mb-md-0">
                    <h5 class="mb-3">新闻通讯</h5>
                    <p class="mb-3">订阅我们的新闻通讯，获取最新体育赛事更新</p>
                    <form class="newsletter-form">
                        <div class="input-group">
                            <input type="email" class="form-control" placeholder="输入您的邮箱" aria-label="输入您的邮箱" required>
                            <button class="btn btn-primary" type="submit">订阅</button>
                        </div>
                    </form>
                    <div class="mt-4">
                        <h5 class="mb-3">联系我们</h5>
                        <address class="mb-0">
                            <p class="mb-2"><i class="fas fa-map-marker-alt me-2"></i>company_address</p>
                            <p class="mb-2"><i class="fas fa-phone me-2"></i>company_phone</p>
                            <p class="mb-0"><i class="fas fa-envelope me-2"></i>company_email</p>
                        </address>
                    </div>
                </div>
            </div>
            <hr class="my-4">
            <div class="row align-items-center">
                <div class="col-md-6 text-center text-md-start">
                    <p class="mb-md-0">&copy; 2025 体育赛事. 版权所有.</p>
                </div>
                <div class="col-md-6 text-center text-md-end">
                    <ul class="list-inline mb-0">
                        <li class="list-inline-item"><a href="#" class="text-white">隐私政策</a></li>
                        <li class="list-inline-item">|</li>
                        <li class="list-inline-item"><a href="#" class="text-white">服务条款</a></li>
                        <li class="list-inline-item">|</li>
                        <li class="list-inline-item"><a href="#" class="text-white">Cookie政策</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Ultimate Image Display Fix - Prevent Disappearing -->
    <style>
        /* 强制显示所有图片 - 最高优先级 */
        img, .card-img-top, .event-list-image, .event-detail-image, .category-image, .related-event-image {
            display: block !important;
            opacity: 1 !important;
            visibility: visible !important;
            transition: none !important;
            animation: none !important;
            max-width: 100% !important;
            height: auto !important;
        }

        /* 特定图片类型的强制显示 */
        .category-card img,
        .event-card img,
        .card img,
        img[src*="/static/images/"] {
            display: block !important;
            opacity: 1 !important;
            visibility: visible !important;
            transition: none !important;
            animation: none !important;
        }

        /* 防止任何隐藏机制 */
        .force-show-images img,
        .force-show-images .card-img-top,
        .force-show-images .event-list-image,
        .force-show-images .event-detail-image,
        .force-show-images .category-image,
        .force-show-images .related-event-image {
            display: block !important;
            opacity: 1 !important;
            visibility: visible !important;
            transition: none !important;
            animation: none !important;
        }

        /* 覆盖可能的Bootstrap或其他CSS */
        .d-none img, .invisible img, .opacity-0 img {
            display: block !important;
            opacity: 1 !important;
            visibility: visible !important;
        }

        /* 确保图片容器也显示 */
        .category-card, .event-card, .card {
            display: block !important;
            opacity: 1 !important;
            visibility: visible !important;
        }
    </style>

    <script>
        // 简化的图片显示修复 - 避免性能问题
        (function() {
            'use strict';

            // 立即添加保护类
            document.documentElement.classList.add('force-show-images');

            // 简化的图片保护函数
            function fixImages() {
                try {
                    const images = document.querySelectorAll('img');
                    images.forEach(img => {
                        // 只设置基本的显示样式，不锁定属性
                        if (img.style.display === 'none' || img.style.opacity === '0' || img.style.visibility === 'hidden') {
                            img.style.display = 'block';
                            img.style.opacity = '1';
                            img.style.visibility = 'visible';
                        }

                        // 移除可能的隐藏类
                        img.classList.remove('d-none', 'invisible', 'opacity-0');
                    });
                } catch (error) {
                    console.log('Image fix error:', error);
                }
            }

            // 页面加载时执行
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', fixImages);
            } else {
                fixImages();
            }

            // 页面完全加载后执行
            window.addEventListener('load', fixImages);

            // 延迟执行一次，确保所有动态内容加载完成
            setTimeout(fixImages, 1000);
        })();
    </script>
    <!-- Custom JS -->
    <script src="/static/js/main.js?q=1747309725"></script>
    <script src="/static/js/custom.js?q=1747726551"></script>
    <!-- Page-specific JavaScript -->
    
<script>
    // Initialize featured events scroll functionality
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Featured Events Scroll initialized');

        // Add animation classes to elements when they come into view
        const animateOnScroll = () => {
            const elements = document.querySelectorAll('.animate-on-scroll');

            elements.forEach(element => {
                const elementPosition = element.getBoundingClientRect().top;
                const windowHeight = window.innerHeight;

                if (elementPosition < windowHeight - 50) {
                    element.classList.add('animated', 'fade-in');
                }
            });
        };

        window.addEventListener('scroll', animateOnScroll);
        animateOnScroll(); // Run once on page load

        // Full-width Featured Events Scroll Functionality
        const scrollContainer = document.querySelector('.featured-events-scroll');
        const scrollLeftBtn = document.querySelector('.scroll-left');
        const scrollRightBtn = document.querySelector('.scroll-right');
        const currentSlideSpan = document.querySelector('.current-slide');
        const totalSlidesSpan = document.querySelector('.total-slides');

        if (scrollContainer && scrollLeftBtn && scrollRightBtn) {
            const cards = scrollContainer.querySelectorAll('.featured-event-card');
            const totalCards = cards.length;

            // Update total slides display
            if (totalSlidesSpan) {
                totalSlidesSpan.textContent = totalCards;
            }

            // Calculate scroll amount based on container width (full width per card)
            const getScrollAmount = () => scrollContainer.clientWidth;

            scrollLeftBtn.addEventListener('click', () => {
                scrollContainer.scrollBy({
                    left: -getScrollAmount(),
                    behavior: 'smooth'
                });
            });

            scrollRightBtn.addEventListener('click', () => {
                scrollContainer.scrollBy({
                    left: getScrollAmount(),
                    behavior: 'smooth'
                });
            });

            // Update button states and slide indicator based on scroll position
            const updateScrollButtons = () => {
                const scrollAmount = getScrollAmount();
                const currentScrollLeft = scrollContainer.scrollLeft;
                const maxScrollLeft = scrollContainer.scrollWidth - scrollContainer.clientWidth;

                const isAtStart = currentScrollLeft <= 10; // Small tolerance
                const isAtEnd = currentScrollLeft >= maxScrollLeft - 10; // Small tolerance

                scrollLeftBtn.disabled = isAtStart;
                scrollRightBtn.disabled = isAtEnd;

                scrollLeftBtn.style.opacity = isAtStart ? '0.5' : '1';
                scrollRightBtn.style.opacity = isAtEnd ? '0.5' : '1';

                // Update current slide indicator (more accurate calculation)
                if (currentSlideSpan && totalCards > 0) {
                    const currentSlide = Math.round(currentScrollLeft / scrollAmount) + 1;
                    currentSlideSpan.textContent = Math.min(Math.max(currentSlide, 1), totalCards);
                }
            };

            // Add scroll event listener with debouncing
            let scrollTimeout;
            scrollContainer.addEventListener('scroll', () => {
                clearTimeout(scrollTimeout);
                scrollTimeout = setTimeout(updateScrollButtons, 50);
            });

            // Initial state and window resize handler
            updateScrollButtons();
            window.addEventListener('resize', updateScrollButtons);

            // Touch/swipe support for mobile
            let isDown = false;
            let startX;
            let scrollLeft;

            scrollContainer.addEventListener('mousedown', (e) => {
                isDown = true;
                scrollContainer.style.cursor = 'grabbing';
                startX = e.pageX - scrollContainer.offsetLeft;
                scrollLeft = scrollContainer.scrollLeft;
            });

            scrollContainer.addEventListener('mouseleave', () => {
                isDown = false;
                scrollContainer.style.cursor = 'grab';
            });

            scrollContainer.addEventListener('mouseup', () => {
                isDown = false;
                scrollContainer.style.cursor = 'grab';
            });

            scrollContainer.addEventListener('mousemove', (e) => {
                if (!isDown) return;
                e.preventDefault();
                const x = e.pageX - scrollContainer.offsetLeft;
                const walk = (x - startX) * 2;
                scrollContainer.scrollLeft = scrollLeft - walk;
            });
        }

        // Countdown functionality for next big event
        const countdownElements = document.querySelectorAll('.countdown-text[data-event-date]');

        function updateCountdowns() {
            countdownElements.forEach(element => {
                const eventDate = new Date(element.dataset.eventDate + 'T00:00:00');
                const now = new Date();
                const timeDiff = eventDate.getTime() - now.getTime();

                if (timeDiff > 0) {
                    const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
                    const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                    const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));

                    if (days > 0) {
                        element.textContent = `${days} days_left`;
                    } else if (hours > 0) {
                        element.textContent = `${hours} hours_left`;
                    } else if (minutes > 0) {
                        element.textContent = `${minutes} minutes_left`;
                    } else {
                        element.textContent = 'starting_soon';
                    }
                } else {
                    element.textContent = 'event_started';
                }
            });
        }

        // Update countdowns every minute
        if (countdownElements.length > 0) {
            updateCountdowns();
            setInterval(updateCountdowns, 60000);
        }
    });
</script>


    <!-- Accessibility script -->
    <script>
        // Add 'aria-current' to active navigation items
        document.addEventListener('DOMContentLoaded', function() {
            const currentPath = window.location.pathname;
            document.querySelectorAll('.navbar-nav .nav-link').forEach(link => {
                if (link.getAttribute('href') === currentPath) {
                    link.setAttribute('aria-current', 'page');
                    link.classList.add('active');
                }
            });
        });
    </script>
</body>
</html>