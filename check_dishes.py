#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库中的菜品数量
"""

from app import app, db
from models import Category, MenuItem

def check_dishes():
    with app.app_context():
        # 检查分类数量
        categories = Category.query.all()
        print(f"总分类数: {len(categories)}")
        
        for category in categories:
            dish_count = MenuItem.query.filter_by(category_id=category.id).count()
            print(f"{category.name}: {dish_count} 道菜品")
        
        # 检查总菜品数量
        total_dishes = MenuItem.query.count()
        print(f"\n总菜品数: {total_dishes}")

if __name__ == '__main__':
    check_dishes()
