from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from datetime import datetime, date, time, timedelta
from models import db, User, Attendance, Schedule

staff_bp = Blueprint('staff', __name__)

@staff_bp.route('/')
@login_required
def index():
    """员工管理首页"""
    if current_user.role not in ['admin', 'manager']:
        flash('权限不足！', 'error')
        return redirect(url_for('main.dashboard'))
    
    return render_template('staff/index.html')

@staff_bp.route('/employees')
@login_required
def list_employees():
    """员工列表"""
    if current_user.role not in ['admin', 'manager']:
        flash('权限不足！', 'error')
        return redirect(url_for('main.dashboard'))
    
    employees = User.query.filter(User.role != 'admin').all()
    return render_template('staff/employees.html', employees=employees)

@staff_bp.route('/attendance')
@login_required
def attendance():
    """考勤管理"""
    if current_user.role not in ['admin', 'manager']:
        flash('权限不足！', 'error')
        return redirect(url_for('main.dashboard'))
    
    today = date.today()
    date_filter = request.args.get('date', today.strftime('%Y-%m-%d'))
    filter_date = datetime.strptime(date_filter, '%Y-%m-%d').date()
    
    # 获取当日考勤记录
    attendances = Attendance.query.filter_by(date=filter_date).all()
    
    # 获取当日应该上班的员工
    scheduled_employees = db.session.query(User).join(Schedule).filter(
        Schedule.date == filter_date
    ).all()
    
    # 创建考勤状态字典
    attendance_dict = {att.employee_id: att for att in attendances}
    
    return render_template('staff/attendance.html',
                         attendances=attendances,
                         scheduled_employees=scheduled_employees,
                         attendance_dict=attendance_dict,
                         current_date=date_filter)

@staff_bp.route('/clock-in', methods=['POST'])
@login_required
def clock_in():
    """打卡上班"""
    today = date.today()
    now = datetime.now()
    
    # 检查今天是否已经打卡
    existing_attendance = Attendance.query.filter_by(
        employee_id=current_user.id,
        date=today
    ).first()
    
    if existing_attendance and existing_attendance.clock_in:
        return jsonify({'success': False, 'message': '今天已经打过上班卡了'})
    
    if not existing_attendance:
        attendance = Attendance(
            employee_id=current_user.id,
            date=today,
            clock_in=now
        )
        db.session.add(attendance)
    else:
        existing_attendance.clock_in = now
    
    db.session.commit()
    
    return jsonify({
        'success': True,
        'message': f'上班打卡成功！时间：{now.strftime("%H:%M:%S")}'
    })

@staff_bp.route('/clock-out', methods=['POST'])
@login_required
def clock_out():
    """打卡下班"""
    today = date.today()
    now = datetime.now()
    
    attendance = Attendance.query.filter_by(
        employee_id=current_user.id,
        date=today
    ).first()
    
    if not attendance or not attendance.clock_in:
        return jsonify({'success': False, 'message': '请先打上班卡'})
    
    if attendance.clock_out:
        return jsonify({'success': False, 'message': '今天已经打过下班卡了'})
    
    attendance.clock_out = now
    
    # 计算工作时长
    work_duration = now - attendance.clock_in
    total_hours = work_duration.total_seconds() / 3600
    
    # 减去休息时间
    if attendance.break_start and attendance.break_end:
        break_duration = attendance.break_end - attendance.break_start
        total_hours -= break_duration.total_seconds() / 3600
    
    attendance.total_hours = round(total_hours, 2)
    
    # 计算加班时长（超过8小时算加班）
    if total_hours > 8:
        attendance.overtime_hours = round(total_hours - 8, 2)
    
    db.session.commit()
    
    return jsonify({
        'success': True,
        'message': f'下班打卡成功！工作时长：{attendance.total_hours}小时'
    })

@staff_bp.route('/schedule')
@login_required
def schedule():
    """排班管理"""
    if current_user.role not in ['admin', 'manager']:
        flash('权限不足！', 'error')
        return redirect(url_for('main.dashboard'))
    
    # 获取本周排班
    today = date.today()
    start_of_week = today - timedelta(days=today.weekday())
    end_of_week = start_of_week + timedelta(days=6)
    
    schedules = Schedule.query.filter(
        Schedule.date.between(start_of_week, end_of_week)
    ).all()
    
    employees = User.query.filter(User.role != 'admin').all()
    
    # 组织排班数据
    schedule_dict = {}
    for schedule in schedules:
        date_str = schedule.date.strftime('%Y-%m-%d')
        if date_str not in schedule_dict:
            schedule_dict[date_str] = []
        schedule_dict[date_str].append(schedule)
    
    # 生成本周日期列表
    week_dates = []
    for i in range(7):
        week_dates.append(start_of_week + timedelta(days=i))
    
    return render_template('staff/schedule.html',
                         schedules=schedules,
                         schedule_dict=schedule_dict,
                         employees=employees,
                         week_dates=week_dates)

@staff_bp.route('/add-schedule', methods=['POST'])
@login_required
def add_schedule():
    """添加排班"""
    if current_user.role not in ['admin', 'manager']:
        return jsonify({'success': False, 'message': '权限不足'})
    
    employee_id = int(request.json['employee_id'])
    schedule_date = datetime.strptime(request.json['date'], '%Y-%m-%d').date()
    shift_start = datetime.strptime(request.json['shift_start'], '%H:%M').time()
    shift_end = datetime.strptime(request.json['shift_end'], '%H:%M').time()
    position = request.json.get('position', '')
    
    # 检查是否已有排班
    existing_schedule = Schedule.query.filter_by(
        employee_id=employee_id,
        date=schedule_date
    ).first()
    
    if existing_schedule:
        return jsonify({'success': False, 'message': '该员工当天已有排班'})
    
    schedule = Schedule(
        employee_id=employee_id,
        date=schedule_date,
        shift_start=shift_start,
        shift_end=shift_end,
        position=position
    )
    
    db.session.add(schedule)
    db.session.commit()
    
    employee = User.query.get(employee_id)
    return jsonify({
        'success': True,
        'message': f'已为 {employee.name} 添加排班'
    })

@staff_bp.route('/my-schedule')
@login_required
def my_schedule():
    """我的排班"""
    # 获取本周排班
    today = date.today()
    start_of_week = today - timedelta(days=today.weekday())
    end_of_week = start_of_week + timedelta(days=6)
    
    my_schedules = Schedule.query.filter(
        Schedule.employee_id == current_user.id,
        Schedule.date.between(start_of_week, end_of_week)
    ).order_by(Schedule.date).all()
    
    # 获取本周考勤记录
    my_attendances = Attendance.query.filter(
        Attendance.employee_id == current_user.id,
        Attendance.date.between(start_of_week, end_of_week)
    ).all()
    
    attendance_dict = {att.date: att for att in my_attendances}
    
    return render_template('staff/my_schedule.html',
                         schedules=my_schedules,
                         attendance_dict=attendance_dict)

@staff_bp.route('/api/attendance-summary')
@login_required
def api_attendance_summary():
    """考勤统计API"""
    if current_user.role not in ['admin', 'manager']:
        return jsonify({'error': '权限不足'})
    
    today = date.today()
    month_start = today.replace(day=1)
    
    # 本月考勤统计
    monthly_attendances = Attendance.query.filter(
        Attendance.date >= month_start
    ).all()
    
    total_hours = sum(att.total_hours or 0 for att in monthly_attendances)
    total_overtime = sum(att.overtime_hours or 0 for att in monthly_attendances)
    
    # 今日出勤人数
    today_attendances = Attendance.query.filter_by(date=today).count()
    
    return jsonify({
        'monthly_total_hours': round(total_hours, 2),
        'monthly_overtime_hours': round(total_overtime, 2),
        'today_attendance_count': today_attendances
    })
