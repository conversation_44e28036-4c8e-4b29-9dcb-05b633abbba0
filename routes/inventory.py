from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from datetime import datetime
from models import db, Ingredient, MenuItem, Category
from config import Config

inventory_bp = Blueprint('inventory', __name__)

@inventory_bp.route('/')
@login_required
def index():
    """库存管理首页"""
    if current_user.role not in ['admin', 'manager']:
        flash('权限不足！', 'error')
        return redirect(url_for('main.dashboard'))
    
    # 获取所有原料
    ingredients = Ingredient.query.all()
    
    # 分类库存状态
    critical_stock = [i for i in ingredients if i.current_stock <= Config.CRITICAL_STOCK_THRESHOLD]
    low_stock = [i for i in ingredients if Config.CRITICAL_STOCK_THRESHOLD < i.current_stock <= Config.LOW_STOCK_THRESHOLD]
    normal_stock = [i for i in ingredients if i.current_stock > Config.LOW_STOCK_THRESHOLD]
    
    return render_template('inventory/index.html',
                         ingredients=ingredients,
                         critical_stock=critical_stock,
                         low_stock=low_stock,
                         normal_stock=normal_stock)

@inventory_bp.route('/add', methods=['GET', 'POST'])
@login_required
def add_ingredient():
    """添加新原料"""
    if current_user.role not in ['admin', 'manager']:
        flash('权限不足！', 'error')
        return redirect(url_for('inventory.index'))
    
    if request.method == 'POST':
        name = request.form['name']
        unit = request.form['unit']
        current_stock = float(request.form['current_stock'])
        min_stock = float(request.form['min_stock'])
        unit_cost = float(request.form['unit_cost'])
        supplier = request.form.get('supplier', '')
        
        ingredient = Ingredient(
            name=name,
            unit=unit,
            current_stock=current_stock,
            min_stock=min_stock,
            unit_cost=unit_cost,
            supplier=supplier
        )
        
        db.session.add(ingredient)
        db.session.commit()
        
        flash(f'原料 {name} 添加成功！', 'success')
        return redirect(url_for('inventory.index'))
    
    return render_template('inventory/add_ingredient.html')

@inventory_bp.route('/update/<int:ingredient_id>', methods=['GET', 'POST'])
@login_required
def update_stock(ingredient_id):
    """更新库存"""
    if current_user.role not in ['admin', 'manager', 'employee']:
        flash('权限不足！', 'error')
        return redirect(url_for('inventory.index'))
    
    ingredient = Ingredient.query.get_or_404(ingredient_id)
    
    if request.method == 'POST':
        action = request.form['action']  # 'add' 或 'subtract'
        quantity = float(request.form['quantity'])
        reason = request.form.get('reason', '')
        
        if action == 'add':
            ingredient.current_stock += quantity
        elif action == 'subtract':
            if ingredient.current_stock >= quantity:
                ingredient.current_stock -= quantity
            else:
                flash('库存不足，无法扣除！', 'error')
                return render_template('inventory/update_stock.html', ingredient=ingredient)
        
        ingredient.last_updated = datetime.utcnow()
        db.session.commit()
        
        flash(f'库存更新成功！当前库存：{ingredient.current_stock} {ingredient.unit}', 'success')
        return redirect(url_for('inventory.index'))
    
    return render_template('inventory/update_stock.html', ingredient=ingredient)

@inventory_bp.route('/menu')
@login_required
def menu_management():
    """菜品管理"""
    if current_user.role not in ['admin', 'manager']:
        flash('权限不足！', 'error')
        return redirect(url_for('main.dashboard'))
    
    categories = Category.query.all()
    menu_items = MenuItem.query.all()
    
    return render_template('inventory/menu.html', 
                         categories=categories, 
                         menu_items=menu_items)

@inventory_bp.route('/menu/add', methods=['GET', 'POST'])
@login_required
def add_menu_item():
    """添加菜品"""
    if current_user.role not in ['admin', 'manager']:
        flash('权限不足！', 'error')
        return redirect(url_for('inventory.menu_management'))
    
    if request.method == 'POST':
        name = request.form['name']
        description = request.form['description']
        price = float(request.form['price'])
        category_id = int(request.form['category_id'])
        preparation_time = int(request.form.get('preparation_time', 15))
        
        menu_item = MenuItem(
            name=name,
            description=description,
            price=price,
            category_id=category_id,
            preparation_time=preparation_time
        )
        
        db.session.add(menu_item)
        db.session.commit()
        
        flash(f'菜品 {name} 添加成功！', 'success')
        return redirect(url_for('inventory.menu_management'))
    
    categories = Category.query.all()
    return render_template('inventory/add_menu_item.html', categories=categories)

@inventory_bp.route('/api/low-stock')
@login_required
def api_low_stock():
    """获取低库存商品API"""
    low_stock_items = Ingredient.query.filter(
        Ingredient.current_stock <= Ingredient.min_stock
    ).all()
    
    return jsonify([{
        'id': item.id,
        'name': item.name,
        'current_stock': item.current_stock,
        'min_stock': item.min_stock,
        'unit': item.unit,
        'status': 'critical' if item.current_stock <= Config.CRITICAL_STOCK_THRESHOLD else 'low'
    } for item in low_stock_items])

@inventory_bp.route('/toggle-availability/<int:item_id>', methods=['POST'])
@login_required
def toggle_availability(item_id):
    """切换菜品可用状态"""
    if current_user.role not in ['admin', 'manager']:
        return jsonify({'success': False, 'message': '权限不足'})
    
    menu_item = MenuItem.query.get_or_404(item_id)
    menu_item.is_available = not menu_item.is_available
    db.session.commit()
    
    return jsonify({
        'success': True, 
        'is_available': menu_item.is_available,
        'message': f'菜品 {menu_item.name} 已{"启用" if menu_item.is_available else "禁用"}'
    })
