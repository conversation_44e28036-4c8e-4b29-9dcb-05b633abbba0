from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from datetime import datetime
from models import db, Order, OrderItem, MenuItem, Customer, Table
import uuid

pos_bp = Blueprint('pos', __name__)

@pos_bp.route('/')
@login_required
def index():
    """POS收银系统首页"""
    if current_user.role not in ['admin', 'manager', 'waiter', 'employee']:
        flash('权限不足！', 'error')
        return redirect(url_for('main.dashboard'))

    # 获取可用桌位
    available_tables = Table.query.filter_by(status='available').all()
    occupied_tables = Table.query.filter_by(status='occupied').all()

    # 获取菜品分类和菜品
    from models import Category
    categories = Category.query.all()
    menu_items = MenuItem.query.filter_by(is_available=True).all()

    return render_template('pos/index.html',
                         available_tables=available_tables,
                         occupied_tables=occupied_tables,
                         categories=categories,
                         menu_items=menu_items)

@pos_bp.route('/new-order/<int:table_id>')
@login_required
def new_order(table_id):
    """创建新订单"""
    if current_user.role not in ['admin', 'manager', 'waiter', 'employee']:
        flash('权限不足！', 'error')
        return redirect(url_for('pos.index'))

    table = Table.query.get_or_404(table_id)
    if table.status != 'available':
        flash('桌位不可用！', 'error')
        return redirect(url_for('pos.index'))

    # 创建新订单
    order_number = f"ORD{datetime.now().strftime('%Y%m%d%H%M%S')}"
    order = Order(
        order_number=order_number,
        table_id=table_id,
        employee_id=current_user.id,
        status='pending'
    )

    # 更新桌位状态
    table.status = 'occupied'

    db.session.add(order)
    db.session.commit()

    flash(f'订单 {order_number} 创建成功！', 'success')
    return redirect(url_for('pos.order_detail', order_id=order.id))

@pos_bp.route('/order/<int:order_id>')
@login_required
def order_detail(order_id):
    """订单详情"""
    order = Order.query.get_or_404(order_id)

    # 获取菜品分类和菜品
    from models import Category
    categories = Category.query.all()
    menu_items = MenuItem.query.filter_by(is_available=True).all()

    return render_template('pos/order_detail.html',
                         order=order,
                         categories=categories,
                         menu_items=menu_items)

@pos_bp.route('/add-item/<int:order_id>', methods=['POST'])
@login_required
def add_item(order_id):
    """添加订单项目"""
    order = Order.query.get_or_404(order_id)

    if order.status not in ['pending', 'preparing']:
        return jsonify({'success': False, 'message': '订单状态不允许修改'})

    menu_item_id = int(request.json['menu_item_id'])
    quantity = int(request.json['quantity'])
    special_instructions = request.json.get('special_instructions', '')

    menu_item = MenuItem.query.get_or_404(menu_item_id)

    # 检查是否已有相同菜品
    existing_item = OrderItem.query.filter_by(
        order_id=order_id,
        menu_item_id=menu_item_id
    ).first()

    if existing_item:
        existing_item.quantity += quantity
        if special_instructions:
            existing_item.special_instructions = special_instructions
    else:
        order_item = OrderItem(
            order_id=order_id,
            menu_item_id=menu_item_id,
            quantity=quantity,
            unit_price=menu_item.price,
            special_instructions=special_instructions
        )
        db.session.add(order_item)

    # 更新订单总金额
    order.total_amount = sum(item.quantity * item.unit_price for item in order.order_items)

    db.session.commit()

    return jsonify({
        'success': True,
        'message': f'已添加 {menu_item.name} x{quantity}',
        'total_amount': order.total_amount
    })

@pos_bp.route('/remove-item/<int:item_id>', methods=['POST'])
@login_required
def remove_item(item_id):
    """移除订单项目"""
    order_item = OrderItem.query.get_or_404(item_id)
    order = order_item.order

    if order.status not in ['pending', 'preparing']:
        return jsonify({'success': False, 'message': '订单状态不允许修改'})

    db.session.delete(order_item)

    # 更新订单总金额
    order.total_amount = sum(item.quantity * item.unit_price for item in order.order_items)

    db.session.commit()

    return jsonify({
        'success': True,
        'message': '项目已移除',
        'total_amount': order.total_amount
    })

@pos_bp.route('/checkout/<int:order_id>', methods=['GET', 'POST'])
@login_required
def checkout(order_id):
    """结账"""
    order = Order.query.get_or_404(order_id)

    if request.method == 'POST':
        payment_method = request.form['payment_method']
        customer_phone = request.form.get('customer_phone', '')

        # 如果提供了客户电话，查找或创建客户
        customer = None
        if customer_phone:
            customer = Customer.query.filter_by(phone=customer_phone).first()
            if not customer:
                customer_name = request.form.get('customer_name', '客户')
                customer = Customer(
                    name=customer_name,
                    phone=customer_phone
                )
                db.session.add(customer)
                db.session.flush()  # 获取ID

            order.customer_id = customer.id

            # 更新客户积分和消费总额
            customer.points += int(order.total_amount // 10)  # 每10元1积分
            customer.total_spent += order.total_amount

        # 更新订单状态
        order.status = 'paid'
        order.payment_method = payment_method
        order.completed_at = datetime.utcnow()

        # 释放桌位
        if order.table:
            order.table.status = 'available'

        db.session.commit()

        flash(f'订单 {order.order_number} 结账成功！', 'success')
        return redirect(url_for('pos.index'))

    return render_template('pos/checkout.html', order=order)

@pos_bp.route('/orders')
@login_required
def orders():
    """订单列表"""
    page = request.args.get('page', 1, type=int)
    status = request.args.get('status', '')

    query = Order.query
    if status:
        query = query.filter_by(status=status)

    orders = query.order_by(Order.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False
    )

    return render_template('pos/orders.html', orders=orders, current_status=status)

@pos_bp.route('/api/menu-items/<int:category_id>')
@login_required
def api_menu_items(category_id):
    """获取分类下的菜品"""
    menu_items = MenuItem.query.filter_by(
        category_id=category_id,
        is_available=True
    ).all()

    return jsonify([{
        'id': item.id,
        'name': item.name,
        'price': item.price,
        'description': item.description,
        'preparation_time': item.preparation_time
    } for item in menu_items])

@pos_bp.route('/table-status/<int:table_id>', methods=['POST'])
@login_required
def update_table_status(table_id):
    """更新桌位状态"""
    if current_user.role not in ['admin', 'manager', 'waiter']:
        return jsonify({'success': False, 'message': '权限不足'})

    table = Table.query.get_or_404(table_id)
    new_status = request.json.get('status')

    if new_status not in ['available', 'occupied', 'reserved', 'cleaning']:
        return jsonify({'success': False, 'message': '无效的状态'})

    # 如果要设置为可用状态，需要检查是否有未完成的订单
    if new_status == 'available':
        active_orders = Order.query.filter_by(
            table_id=table_id,
            status__in=['pending', 'preparing', 'ready']
        ).first()

        if active_orders:
            return jsonify({
                'success': False,
                'message': '该桌位还有未完成的订单，无法设置为可用状态'
            })

    old_status = table.status
    table.status = new_status
    db.session.commit()

    status_text = {
        'available': '可用',
        'occupied': '使用中',
        'reserved': '已预订',
        'cleaning': '清洁中'
    }

    return jsonify({
        'success': True,
        'message': f'桌位 {table.number} 状态已从 {status_text.get(old_status, old_status)} 更新为 {status_text.get(new_status, new_status)}',
        'new_status': new_status
    })

@pos_bp.route('/table-orders/<int:table_id>')
@login_required
def table_orders(table_id):
    """查看桌位的当前订单"""
    table = Table.query.get_or_404(table_id)

    # 获取该桌位的活跃订单
    active_orders = Order.query.filter_by(
        table_id=table_id
    ).filter(
        Order.status.in_(['pending', 'preparing', 'ready'])
    ).order_by(Order.created_at.desc()).all()

    return render_template('pos/table_orders.html',
                         table=table,
                         orders=active_orders)

@pos_bp.route('/cancel-order/<int:order_id>', methods=['POST'])
@login_required
def cancel_order(order_id):
    """取消订单"""
    if current_user.role not in ['admin', 'manager']:
        return jsonify({'success': False, 'message': '权限不足'})

    order = Order.query.get_or_404(order_id)

    if order.status not in ['pending', 'preparing']:
        return jsonify({'success': False, 'message': '订单状态不允许取消'})

    # 释放桌位
    if order.table:
        order.table.status = 'available'

    # 更新订单状态
    order.status = 'cancelled'
    order.completed_at = datetime.utcnow()

    db.session.commit()

    return jsonify({
        'success': True,
        'message': f'订单 {order.order_number} 已取消，桌位已释放'
    })
