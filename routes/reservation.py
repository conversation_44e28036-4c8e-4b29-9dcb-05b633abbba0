from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from datetime import datetime, date, time, timedelta
from models import db, Reservation, Customer, Table

reservation_bp = Blueprint('reservation', __name__)

@reservation_bp.route('/')
def index():
    """预订首页 - 客户可访问"""
    tables = Table.query.filter_by(status='available').all()
    return render_template('reservation/index.html', tables=tables)

@reservation_bp.route('/make', methods=['GET', 'POST'])
def make_reservation():
    """创建预订"""
    if request.method == 'POST':
        name = request.form['name']
        phone = request.form['phone']
        email = request.form.get('email', '')
        party_size = int(request.form['party_size'])
        reservation_date = datetime.strptime(request.form['reservation_date'], '%Y-%m-%d').date()
        reservation_time = datetime.strptime(request.form['reservation_time'], '%H:%M').time()
        special_requests = request.form.get('special_requests', '')
        
        # 查找或创建客户
        customer = Customer.query.filter_by(phone=phone).first()
        if not customer:
            customer = Customer(
                name=name,
                phone=phone,
                email=email
            )
            db.session.add(customer)
            db.session.flush()
        
        # 查找合适的桌位
        suitable_table = find_suitable_table(party_size, reservation_date, reservation_time)
        
        if not suitable_table:
            flash('抱歉，该时间段没有合适的桌位。请选择其他时间。', 'error')
            return render_template('reservation/make.html')
        
        # 创建预订
        reservation = Reservation(
            customer_id=customer.id,
            table_id=suitable_table.id,
            reservation_date=reservation_date,
            reservation_time=reservation_time,
            party_size=party_size,
            special_requests=special_requests
        )
        
        db.session.add(reservation)
        db.session.commit()
        
        flash(f'预订成功！预订号：{reservation.id}，桌位：{suitable_table.number}', 'success')
        return redirect(url_for('reservation.confirmation', reservation_id=reservation.id))
    
    return render_template('reservation/make.html')

@reservation_bp.route('/confirmation/<int:reservation_id>')
def confirmation(reservation_id):
    """预订确认页面"""
    reservation = Reservation.query.get_or_404(reservation_id)
    return render_template('reservation/confirmation.html', reservation=reservation)

@reservation_bp.route('/manage')
@login_required
def manage():
    """预订管理 - 员工使用"""
    if current_user.role not in ['admin', 'manager', 'waiter', 'employee']:
        flash('权限不足！', 'error')
        return redirect(url_for('main.dashboard'))
    
    today = date.today()
    page = request.args.get('page', 1, type=int)
    date_filter = request.args.get('date', today.strftime('%Y-%m-%d'))
    
    if date_filter:
        filter_date = datetime.strptime(date_filter, '%Y-%m-%d').date()
        reservations = Reservation.query.filter_by(
            reservation_date=filter_date
        ).order_by(Reservation.reservation_time).paginate(
            page=page, per_page=20, error_out=False
        )
    else:
        reservations = Reservation.query.order_by(
            Reservation.reservation_date.desc(),
            Reservation.reservation_time.desc()
        ).paginate(page=page, per_page=20, error_out=False)
    
    return render_template('reservation/manage.html', 
                         reservations=reservations, 
                         current_date=date_filter)

@reservation_bp.route('/update-status/<int:reservation_id>', methods=['POST'])
@login_required
def update_status(reservation_id):
    """更新预订状态"""
    if current_user.role not in ['admin', 'manager', 'waiter', 'employee']:
        return jsonify({'success': False, 'message': '权限不足'})
    
    reservation = Reservation.query.get_or_404(reservation_id)
    new_status = request.json['status']
    
    if new_status not in ['confirmed', 'cancelled', 'completed', 'no_show']:
        return jsonify({'success': False, 'message': '无效状态'})
    
    reservation.status = new_status
    
    # 如果取消或完成预订，释放桌位
    if new_status in ['cancelled', 'completed', 'no_show']:
        reservation.table.status = 'available'
    elif new_status == 'confirmed':
        # 如果是当天的预订，可能需要标记桌位为预订状态
        if reservation.reservation_date == date.today():
            current_time = datetime.now().time()
            reservation_datetime = datetime.combine(reservation.reservation_date, reservation.reservation_time)
            # 如果预订时间在1小时内，标记桌位为预订状态
            if abs((datetime.now() - reservation_datetime).total_seconds()) <= 3600:
                reservation.table.status = 'reserved'
    
    db.session.commit()
    
    return jsonify({
        'success': True,
        'message': f'预订状态已更新为：{get_status_text(new_status)}'
    })

@reservation_bp.route('/api/available-times')
def api_available_times():
    """获取可用时间段API"""
    date_str = request.args.get('date')
    party_size = int(request.args.get('party_size', 2))
    
    if not date_str:
        return jsonify({'error': '请提供日期'})
    
    reservation_date = datetime.strptime(date_str, '%Y-%m-%d').date()
    
    # 营业时间：11:00-22:00
    start_time = time(11, 0)
    end_time = time(22, 0)
    
    available_times = []
    current_time = datetime.combine(reservation_date, start_time)
    end_datetime = datetime.combine(reservation_date, end_time)
    
    while current_time <= end_datetime:
        if has_available_table(party_size, reservation_date, current_time.time()):
            available_times.append(current_time.strftime('%H:%M'))
        current_time += timedelta(minutes=30)  # 每30分钟一个时间段
    
    return jsonify({'available_times': available_times})

def find_suitable_table(party_size, reservation_date, reservation_time):
    """查找合适的桌位"""
    # 查找容量足够的桌位
    suitable_tables = Table.query.filter(
        Table.capacity >= party_size,
        Table.status.in_(['available', 'reserved'])
    ).all()
    
    for table in suitable_tables:
        if is_table_available(table.id, reservation_date, reservation_time):
            return table
    
    return None

def is_table_available(table_id, reservation_date, reservation_time):
    """检查桌位在指定时间是否可用"""
    # 检查该时间段前后2小时是否有预订
    start_time = (datetime.combine(reservation_date, reservation_time) - timedelta(hours=2)).time()
    end_time = (datetime.combine(reservation_date, reservation_time) + timedelta(hours=2)).time()
    
    conflicting_reservations = Reservation.query.filter(
        Reservation.table_id == table_id,
        Reservation.reservation_date == reservation_date,
        Reservation.reservation_time.between(start_time, end_time),
        Reservation.status.in_(['confirmed', 'completed'])
    ).count()
    
    return conflicting_reservations == 0

def has_available_table(party_size, reservation_date, reservation_time):
    """检查是否有可用桌位"""
    return find_suitable_table(party_size, reservation_date, reservation_time) is not None

def get_status_text(status):
    """获取状态文本"""
    status_map = {
        'confirmed': '已确认',
        'cancelled': '已取消',
        'completed': '已完成',
        'no_show': '未到店'
    }
    return status_map.get(status, status)
