from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from datetime import datetime, date, timedelta
from models import db, Customer, Order, Reservation
from sqlalchemy import func

customer_bp = Blueprint('customer', __name__)

@customer_bp.route('/')
@login_required
def index():
    """客户管理首页"""
    if current_user.role not in ['admin', 'manager', 'waiter']:
        flash('权限不足！', 'error')
        return redirect(url_for('main.dashboard'))

    # 客户统计
    total_customers = Customer.query.count()
    new_customers_this_month = Customer.query.filter(
        func.extract('month', Customer.created_at) == datetime.now().month,
        func.extract('year', Customer.created_at) == datetime.now().year
    ).count()

    # VIP客户（消费超过1000元）
    vip_customers = Customer.query.filter(Customer.total_spent >= 1000).count()

    # 今日生日客户
    today = date.today()
    today_birthdays = Customer.query.filter(
        func.extract('month', Customer.birthday) == today.month,
        func.extract('day', Customer.birthday) == today.day
    ).count()

    # VIP客户列表（前5名）
    vip_customers_list = Customer.query.filter(
        Customer.total_spent >= 1000
    ).order_by(Customer.total_spent.desc()).limit(5).all()

    # 近期生日客户（未来7天）
    upcoming_birthdays = []
    for i in range(7):
        check_date = today + timedelta(days=i)
        birthday_customers = Customer.query.filter(
            func.extract('month', Customer.birthday) == check_date.month,
            func.extract('day', Customer.birthday) == check_date.day
        ).all()
        upcoming_birthdays.extend(birthday_customers)

    return render_template('customer/index.html',
                         total_customers=total_customers,
                         new_customers_this_month=new_customers_this_month,
                         vip_customers=vip_customers,
                         today_birthdays=today_birthdays,
                         vip_customers_list=vip_customers_list,
                         upcoming_birthdays=upcoming_birthdays[:5])

@customer_bp.route('/list')
@login_required
def list_customers():
    """客户列表"""
    if current_user.role not in ['admin', 'manager', 'waiter']:
        flash('权限不足！', 'error')
        return redirect(url_for('main.dashboard'))

    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')

    query = Customer.query
    if search:
        query = query.filter(
            db.or_(
                Customer.name.contains(search),
                Customer.phone.contains(search),
                Customer.email.contains(search)
            )
        )

    customers = query.order_by(Customer.total_spent.desc()).paginate(
        page=page, per_page=20, error_out=False
    )

    return render_template('customer/list.html',
                         customers=customers,
                         search=search)

@customer_bp.route('/detail/<int:customer_id>')
@login_required
def detail(customer_id):
    """客户详情"""
    if current_user.role not in ['admin', 'manager', 'waiter']:
        flash('权限不足！', 'error')
        return redirect(url_for('main.dashboard'))

    customer = Customer.query.get_or_404(customer_id)

    # 获取客户订单历史
    orders = Order.query.filter_by(customer_id=customer_id).order_by(
        Order.created_at.desc()
    ).limit(10).all()

    # 获取客户预订历史
    reservations = Reservation.query.filter_by(customer_id=customer_id).order_by(
        Reservation.created_at.desc()
    ).limit(10).all()

    # 计算客户统计
    total_orders = Order.query.filter_by(customer_id=customer_id).count()
    total_reservations = Reservation.query.filter_by(customer_id=customer_id).count()

    # 最近消费时间
    last_order = Order.query.filter_by(customer_id=customer_id).order_by(
        Order.created_at.desc()
    ).first()

    return render_template('customer/detail.html',
                         customer=customer,
                         orders=orders,
                         reservations=reservations,
                         total_orders=total_orders,
                         total_reservations=total_reservations,
                         last_order=last_order)

@customer_bp.route('/add', methods=['GET', 'POST'])
@login_required
def add_customer():
    """添加客户"""
    if current_user.role not in ['admin', 'manager', 'waiter']:
        flash('权限不足！', 'error')
        return redirect(url_for('customer.index'))

    if request.method == 'POST':
        name = request.form['name']
        phone = request.form['phone']
        email = request.form.get('email', '')
        birthday_str = request.form.get('birthday', '')
        preferences = request.form.get('preferences', '')

        # 检查手机号是否已存在
        if Customer.query.filter_by(phone=phone).first():
            flash('手机号已存在！', 'error')
            return render_template('customer/add.html')

        # 处理生日
        birthday = None
        if birthday_str:
            try:
                birthday = datetime.strptime(birthday_str, '%Y-%m-%d').date()
            except ValueError:
                flash('生日格式错误！', 'error')
                return render_template('customer/add.html')

        customer = Customer(
            name=name,
            phone=phone,
            email=email,
            birthday=birthday,
            preferences=preferences
        )

        db.session.add(customer)
        db.session.commit()

        flash(f'客户 {name} 添加成功！', 'success')
        return redirect(url_for('customer.list_customers'))

    return render_template('customer/add.html')

@customer_bp.route('/edit/<int:customer_id>', methods=['GET', 'POST'])
@login_required
def edit_customer(customer_id):
    """编辑客户信息"""
    if current_user.role not in ['admin', 'manager']:
        flash('权限不足！', 'error')
        return redirect(url_for('customer.detail', customer_id=customer_id))

    customer = Customer.query.get_or_404(customer_id)

    if request.method == 'POST':
        customer.name = request.form['name']
        customer.email = request.form.get('email', '')
        customer.preferences = request.form.get('preferences', '')

        birthday_str = request.form.get('birthday', '')
        if birthday_str:
            try:
                customer.birthday = datetime.strptime(birthday_str, '%Y-%m-%d').date()
            except ValueError:
                flash('生日格式错误！', 'error')
                return render_template('customer/edit.html', customer=customer)
        else:
            customer.birthday = None

        db.session.commit()
        flash('客户信息更新成功！', 'success')
        return redirect(url_for('customer.detail', customer_id=customer_id))

    return render_template('customer/edit.html', customer=customer)

@customer_bp.route('/points/<int:customer_id>', methods=['POST'])
@login_required
def adjust_points(customer_id):
    """调整客户积分"""
    if current_user.role not in ['admin', 'manager']:
        return jsonify({'success': False, 'message': '权限不足'})

    customer = Customer.query.get_or_404(customer_id)
    action = request.json['action']  # 'add' 或 'subtract'
    points = int(request.json['points'])
    reason = request.json.get('reason', '')

    if action == 'add':
        customer.points += points
        message = f'为客户 {customer.name} 增加 {points} 积分'
    elif action == 'subtract':
        if customer.points >= points:
            customer.points -= points
            message = f'为客户 {customer.name} 扣除 {points} 积分'
        else:
            return jsonify({'success': False, 'message': '积分不足'})
    else:
        return jsonify({'success': False, 'message': '无效操作'})

    db.session.commit()

    return jsonify({
        'success': True,
        'message': message,
        'current_points': customer.points
    })

@customer_bp.route('/birthday-customers')
@login_required
def birthday_customers():
    """生日客户"""
    if current_user.role not in ['admin', 'manager', 'waiter']:
        flash('权限不足！', 'error')
        return redirect(url_for('customer.index'))

    today = date.today()

    # 今天生日的客户
    today_birthdays = Customer.query.filter(
        func.extract('month', Customer.birthday) == today.month,
        func.extract('day', Customer.birthday) == today.day
    ).all()

    # 本周生日的客户
    week_start = today - timedelta(days=today.weekday())
    week_end = week_start + timedelta(days=6)

    week_birthdays = []
    for i in range(7):
        day = week_start + timedelta(days=i)
        day_birthdays = Customer.query.filter(
            func.extract('month', Customer.birthday) == day.month,
            func.extract('day', Customer.birthday) == day.day
        ).all()
        if day_birthdays:
            week_birthdays.append({
                'date': day,
                'customers': day_birthdays
            })

    return render_template('customer/birthdays.html',
                         today_birthdays=today_birthdays,
                         week_birthdays=week_birthdays)

@customer_bp.route('/api/search')
@login_required
def api_search():
    """客户搜索API"""
    query = request.args.get('q', '')
    if len(query) < 2:
        return jsonify([])

    customers = Customer.query.filter(
        db.or_(
            Customer.name.contains(query),
            Customer.phone.contains(query)
        )
    ).limit(10).all()

    return jsonify([{
        'id': customer.id,
        'name': customer.name,
        'phone': customer.phone,
        'points': customer.points,
        'total_spent': customer.total_spent
    } for customer in customers])

@customer_bp.route('/loyalty-report')
@login_required
def loyalty_report():
    """客户忠诚度报告"""
    if current_user.role not in ['admin', 'manager']:
        flash('权限不足！', 'error')
        return redirect(url_for('customer.index'))

    # 按消费金额分类客户
    vip_customers = Customer.query.filter(Customer.total_spent >= 1000).order_by(
        Customer.total_spent.desc()
    ).limit(20).all()

    regular_customers = Customer.query.filter(
        Customer.total_spent.between(100, 999)
    ).count()

    new_customers = Customer.query.filter(Customer.total_spent < 100).count()

    # 客户消费趋势
    monthly_stats = db.session.query(
        func.extract('year', Customer.created_at).label('year'),
        func.extract('month', Customer.created_at).label('month'),
        func.count(Customer.id).label('count')
    ).group_by('year', 'month').order_by('year', 'month').all()

    return render_template('customer/loyalty_report.html',
                         vip_customers=vip_customers,
                         regular_customers=regular_customers,
                         new_customers=new_customers,
                         monthly_stats=monthly_stats)
