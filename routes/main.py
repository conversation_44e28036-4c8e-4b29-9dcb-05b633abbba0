from flask import Blueprint, render_template, jsonify
from flask_login import login_required, current_user
from datetime import datetime, date, timedelta
from sqlalchemy import func
from models import db, Order, OrderItem, MenuItem, Customer, Reservation, Table, Ingredient

main_bp = Blueprint('main', __name__)

@main_bp.route('/')
def index():
    """首页 - 重定向到管理系统登录"""
    return redirect(url_for('auth.login'))

@main_bp.route('/dashboard')
@login_required
def dashboard():
    """管理仪表板"""
    # 获取今日统计数据
    today = date.today()

    # 今日订单统计
    today_orders = Order.query.filter(
        func.date(Order.created_at) == today
    ).all()

    today_revenue = sum(order.total_amount for order in today_orders if order.total_amount)
    today_order_count = len(today_orders)

    # 今日预订统计
    today_reservations = Reservation.query.filter(
        Reservation.reservation_date == today
    ).count()

    # 库存警告
    low_stock_items = Ingredient.query.filter(
        Ingredient.current_stock <= Ingredient.min_stock
    ).all()

    # 桌位状态
    tables = Table.query.all()
    available_tables = len([t for t in tables if t.status == 'available'])

    # 热门菜品（本周）
    week_ago = datetime.now() - timedelta(days=7)
    popular_items = db.session.query(
        MenuItem.name,
        func.sum(OrderItem.quantity).label('total_quantity')
    ).join(OrderItem).join(Order).filter(
        Order.created_at >= week_ago
    ).group_by(MenuItem.id, MenuItem.name).order_by(
        func.sum(OrderItem.quantity).desc()
    ).limit(5).all()

    # 本月收入趋势（最近7天）
    revenue_trend = []
    for i in range(7):
        day = today - timedelta(days=i)
        day_orders = Order.query.filter(
            func.date(Order.created_at) == day
        ).all()
        day_revenue = sum(order.total_amount for order in day_orders if order.total_amount)
        revenue_trend.append({
            'date': day.strftime('%m-%d'),
            'revenue': day_revenue
        })
    revenue_trend.reverse()

    return render_template('dashboard.html',
                         today_revenue=today_revenue,
                         today_order_count=today_order_count,
                         today_reservations=today_reservations,
                         low_stock_items=low_stock_items,
                         available_tables=available_tables,
                         total_tables=len(tables),
                         popular_items=popular_items,
                         revenue_trend=revenue_trend)

@main_bp.route('/api/dashboard/stats')
@login_required
def dashboard_stats():
    """仪表板统计数据API"""
    today = date.today()

    # 今日订单
    today_orders = Order.query.filter(
        func.date(Order.created_at) == today
    ).all()

    # 桌位状态
    table_status = db.session.query(
        Table.status,
        func.count(Table.id).label('count')
    ).group_by(Table.status).all()

    return jsonify({
        'today_orders': len(today_orders),
        'today_revenue': sum(order.total_amount for order in today_orders if order.total_amount),
        'table_status': {status: count for status, count in table_status}
    })
