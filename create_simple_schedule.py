#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建简化的排班表
"""

from app import app, db
from models import User, Schedule
from datetime import datetime, date, timedelta, time

def create_simple_schedule():
    with app.app_context():
        # 删除现有排班
        Schedule.query.delete()
        
        # 获取所有员工
        staff_members = User.query.filter(User.role != 'admin').all()
        
        if not staff_members:
            print("没有找到员工，请先添加员工数据")
            return
        
        # 按角色分组员工
        managers = [s for s in staff_members if s.role == 'manager']
        waiters = [s for s in staff_members if s.role == 'waiter']
        chefs = [s for s in staff_members if s.role == 'chef']
        cashiers = [s for s in staff_members if s.role == 'cashier']
        
        print(f"找到员工: 经理{len(managers)}人, 服务员{len(waiters)}人, 厨师{len(chefs)}人, 收银员{len(cashiers)}人")
        
        # 生成未来30天的排班
        start_date = date.today()
        schedules = []
        
        # 定义班次时间
        morning_start = time(9, 0)   # 9:00
        morning_end = time(17, 0)    # 17:00
        evening_start = time(17, 0)  # 17:00
        evening_end = time(23, 0)    # 23:00
        full_start = time(9, 0)      # 9:00
        full_end = time(23, 0)       # 23:00
        
        for day_offset in range(30):
            current_date = start_date + timedelta(days=day_offset)
            weekday = current_date.weekday()  # 0=周一, 6=周日
            is_weekend = weekday >= 5
            
            # 经理排班 - 每天至少有一个经理
            if managers:
                manager_index = day_offset % len(managers)
                schedules.append(Schedule(
                    employee_id=managers[manager_index].id,
                    date=current_date,
                    shift_start=full_start,
                    shift_end=full_end,
                    position='经理-全天'
                ))
            
            # 服务员排班
            if waiters:
                waiter_count = 4 if is_weekend else 3
                
                # 早班服务员 (2人)
                for i in range(min(2, len(waiters))):
                    waiter_index = (day_offset * 2 + i) % len(waiters)
                    schedules.append(Schedule(
                        employee_id=waiters[waiter_index].id,
                        date=current_date,
                        shift_start=morning_start,
                        shift_end=morning_end,
                        position='服务员-早班'
                    ))
                
                # 晚班服务员
                evening_count = waiter_count - 2
                for i in range(evening_count):
                    waiter_index = (day_offset * 2 + 2 + i) % len(waiters)
                    schedules.append(Schedule(
                        employee_id=waiters[waiter_index].id,
                        date=current_date,
                        shift_start=evening_start,
                        shift_end=evening_end,
                        position='服务员-晚班'
                    ))
            
            # 厨师排班
            if chefs:
                # 主厨全天
                if len(chefs) > 0:
                    schedules.append(Schedule(
                        employee_id=chefs[0].id,
                        date=current_date,
                        shift_start=full_start,
                        shift_end=full_end,
                        position='主厨-全天'
                    ))
                
                # 其他厨师分班
                if len(chefs) > 1:
                    chef_count = 3 if is_weekend else 2
                    for i in range(1, min(chef_count, len(chefs))):
                        chef_index = i % len(chefs)
                        if i % 2 == 1:
                            # 早班
                            schedules.append(Schedule(
                                employee_id=chefs[chef_index].id,
                                date=current_date,
                                shift_start=morning_start,
                                shift_end=morning_end,
                                position='厨师-早班'
                            ))
                        else:
                            # 晚班
                            schedules.append(Schedule(
                                employee_id=chefs[chef_index].id,
                                date=current_date,
                                shift_start=evening_start,
                                shift_end=evening_end,
                                position='厨师-晚班'
                            ))
            
            # 收银员排班
            if cashiers:
                if is_weekend and len(cashiers) >= 2:
                    # 周末两班
                    schedules.append(Schedule(
                        employee_id=cashiers[day_offset % len(cashiers)].id,
                        date=current_date,
                        shift_start=morning_start,
                        shift_end=morning_end,
                        position='收银员-早班'
                    ))
                    schedules.append(Schedule(
                        employee_id=cashiers[(day_offset + 1) % len(cashiers)].id,
                        date=current_date,
                        shift_start=evening_start,
                        shift_end=evening_end,
                        position='收银员-晚班'
                    ))
                else:
                    # 工作日全天
                    schedules.append(Schedule(
                        employee_id=cashiers[day_offset % len(cashiers)].id,
                        date=current_date,
                        shift_start=full_start,
                        shift_end=full_end,
                        position='收银员-全天'
                    ))
        
        # 添加所有排班到数据库
        for schedule in schedules:
            db.session.add(schedule)
        
        db.session.commit()
        print(f"成功创建 {len(schedules)} 条排班记录！")
        
        # 统计信息
        role_stats = {}
        for schedule in schedules:
            user = User.query.get(schedule.employee_id)
            if user.role not in role_stats:
                role_stats[user.role] = 0
            role_stats[user.role] += 1
        
        print("按角色统计班次:")
        for role, count in role_stats.items():
            print(f"- {role}: {count} 个班次")

if __name__ == '__main__':
    create_simple_schedule()
