#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
添加特色菜品到数据库（小食、早餐、夜宵等）
"""

from app import app, db
from models import Category, MenuItem

def add_special_dishes():
    with app.app_context():
        # 获取分类
        snack_category = Category.query.filter_by(name='小食').first()
        breakfast_category = Category.query.filter_by(name='早餐').first()
        latenight_category = Category.query.filter_by(name='夜宵').first()
        french_category = Category.query.filter_by(name='法国菜').first()
        western_category = Category.query.filter_by(name='西餐').first()
        jiangsu_category = Category.query.filter_by(name='苏菜').first()
        zhejiang_category = Category.query.filter_by(name='浙菜').first()
        fujian_category = Category.query.filter_by(name='闽菜').first()
        anhui_category = Category.query.filter_by(name='徽菜').first()
        
        new_dishes = []
        
        # 小食 (20种)
        if snack_category:
            new_dishes.extend([
                MenuItem(name='薯条', description='金黄薯条，香脆可口', price=18.0, category_id=snack_category.id, preparation_time=8),
                MenuItem(name='鸡米花', description='香脆鸡米花，外酥内嫩', price=28.0, category_id=snack_category.id, preparation_time=10),
                MenuItem(name='爆米花', description='香甜爆米花，休闲首选', price=15.0, category_id=snack_category.id, preparation_time=5),
                MenuItem(name='花生米', description='酒鬼花生米，下酒佳品', price=12.0, category_id=snack_category.id, preparation_time=3),
                MenuItem(name='瓜子', description='香瓜子，休闲零食', price=10.0, category_id=snack_category.id, preparation_time=2),
                MenuItem(name='开心果', description='进口开心果，营养美味', price=35.0, category_id=snack_category.id, preparation_time=2),
                MenuItem(name='腰果', description='香脆腰果，营养丰富', price=32.0, category_id=snack_category.id, preparation_time=2),
                MenuItem(name='杏仁', description='美国大杏仁，香脆可口', price=28.0, category_id=snack_category.id, preparation_time=2),
                MenuItem(name='牛肉干', description='内蒙古牛肉干，嚼劲十足', price=45.0, category_id=snack_category.id, preparation_time=3),
                MenuItem(name='猪肉脯', description='蜜汁猪肉脯，甜香可口', price=38.0, category_id=snack_category.id, preparation_time=3),
                MenuItem(name='鱿鱼丝', description='香辣鱿鱼丝，Q弹有嚼劲', price=25.0, category_id=snack_category.id, preparation_time=3),
                MenuItem(name='海苔', description='韩式海苔，清香可口', price=15.0, category_id=snack_category.id, preparation_time=2),
                MenuItem(name='薯片', description='香脆薯片，多种口味', price=12.0, category_id=snack_category.id, preparation_time=2),
                MenuItem(name='饼干', description='进口饼干，香甜酥脆', price=18.0, category_id=snack_category.id, preparation_time=2),
                MenuItem(name='巧克力', description='进口巧克力，丝滑香甜', price=25.0, category_id=snack_category.id, preparation_time=2),
                MenuItem(name='果冻', description='水果果冻，Q弹爽口', price=8.0, category_id=snack_category.id, preparation_time=2),
                MenuItem(name='酸奶', description='希腊酸奶，浓郁香甜', price=15.0, category_id=snack_category.id, preparation_time=2),
                MenuItem(name='水果拼盘', description='时令水果拼盘，新鲜营养', price=38.0, category_id=snack_category.id, preparation_time=8),
                MenuItem(name='坚果拼盘', description='混合坚果拼盘，营养丰富', price=48.0, category_id=snack_category.id, preparation_time=5),
                MenuItem(name='蛋糕', description='小份蛋糕，精致甜美', price=28.0, category_id=snack_category.id, preparation_time=3),
            ])
        
        # 早餐 (15种)
        if breakfast_category:
            new_dishes.extend([
                MenuItem(name='小笼包', description='上海小笼包，皮薄汁多', price=28.0, category_id=breakfast_category.id, preparation_time=15),
                MenuItem(name='生煎包', description='上海生煎包，底脆汁鲜', price=25.0, category_id=breakfast_category.id, preparation_time=12),
                MenuItem(name='豆浆油条', description='经典豆浆油条，营养美味', price=15.0, category_id=breakfast_category.id, preparation_time=8),
                MenuItem(name='煎饼果子', description='天津煎饼果子，香脆可口', price=18.0, category_id=breakfast_category.id, preparation_time=10),
                MenuItem(name='包子', description='各种馅料包子，松软香甜', price=12.0, category_id=breakfast_category.id, preparation_time=10),
                MenuItem(name='馒头', description='白面馒头，松软香甜', price=8.0, category_id=breakfast_category.id, preparation_time=5),
                MenuItem(name='稀饭', description='白米稀饭，清淡养胃', price=8.0, category_id=breakfast_category.id, preparation_time=10),
                MenuItem(name='咸菜', description='爽口咸菜，开胃下饭', price=6.0, category_id=breakfast_category.id, preparation_time=3),
                MenuItem(name='鸡蛋', description='水煮鸡蛋，营养丰富', price=5.0, category_id=breakfast_category.id, preparation_time=8),
                MenuItem(name='牛奶', description='新鲜牛奶，营养健康', price=12.0, category_id=breakfast_category.id, preparation_time=2),
                MenuItem(name='面条', description='清汤面条，简单美味', price=15.0, category_id=breakfast_category.id, preparation_time=8),
                MenuItem(name='蒸蛋', description='水蒸蛋，嫩滑营养', price=12.0, category_id=breakfast_category.id, preparation_time=15),
                MenuItem(name='粥', description='各种口味粥品，营养暖胃', price=18.0, category_id=breakfast_category.id, preparation_time=20),
                MenuItem(name='三明治', description='西式三明治，营养均衡', price=25.0, category_id=breakfast_category.id, preparation_time=8),
                MenuItem(name='吐司', description='烤吐司配果酱，香甜可口', price=15.0, category_id=breakfast_category.id, preparation_time=5),
            ])
        
        # 夜宵 (18种)
        if latenight_category:
            new_dishes.extend([
                MenuItem(name='烧烤拼盘', description='各种烧烤拼盘，香辣过瘾', price=88.0, category_id=latenight_category.id, preparation_time=20),
                MenuItem(name='麻辣烫', description='四川麻辣烫，麻辣鲜香', price=35.0, category_id=latenight_category.id, preparation_time=15),
                MenuItem(name='关东煮', description='日式关东煮，清淡暖胃', price=28.0, category_id=latenight_category.id, preparation_time=10),
                MenuItem(name='泡面', description='各种口味泡面，简单快手', price=12.0, category_id=latenight_category.id, preparation_time=5),
                MenuItem(name='炒面', description='夜宵炒面，香滑可口', price=25.0, category_id=latenight_category.id, preparation_time=10),
                MenuItem(name='炒饭', description='夜宵炒饭，简单美味', price=22.0, category_id=latenight_category.id, preparation_time=8),
                MenuItem(name='馄饨', description='鲜肉馄饨，皮薄馅大', price=18.0, category_id=latenight_category.id, preparation_time=12),
                MenuItem(name='饺子', description='各种馅料饺子，营养美味', price=25.0, category_id=latenight_category.id, preparation_time=15),
                MenuItem(name='汤圆', description='甜汤圆，软糯香甜', price=15.0, category_id=latenight_category.id, preparation_time=10),
                MenuItem(name='粉丝汤', description='粉丝汤，清淡爽口', price=12.0, category_id=latenight_category.id, preparation_time=8),
                MenuItem(name='蛋花汤', description='蛋花汤，营养暖胃', price=10.0, category_id=latenight_category.id, preparation_time=5),
                MenuItem(name='小馄饨', description='小馄饨，精致可爱', price=15.0, category_id=latenight_category.id, preparation_time=10),
                MenuItem(name='煎蛋', description='煎蛋，简单营养', price=8.0, category_id=latenight_category.id, preparation_time=5),
                MenuItem(name='火腿肠', description='烤火腿肠，香嫩可口', price=12.0, category_id=latenight_category.id, preparation_time=5),
                MenuItem(name='烤肠', description='德式烤肠，香辣可口', price=18.0, category_id=latenight_category.id, preparation_time=8),
                MenuItem(name='热狗', description='美式热狗，丰富配菜', price=22.0, category_id=latenight_category.id, preparation_time=8),
                MenuItem(name='汉堡', description='迷你汉堡，精致美味', price=28.0, category_id=latenight_category.id, preparation_time=10),
                MenuItem(name='粥品', description='各种夜宵粥品，暖胃养生', price=20.0, category_id=latenight_category.id, preparation_time=25),
            ])
        
        # 法国菜 (10种)
        if french_category:
            new_dishes.extend([
                MenuItem(name='法式鹅肝', description='法式鹅肝，奢华享受', price=188.0, category_id=french_category.id, preparation_time=15),
                MenuItem(name='法式蜗牛', description='法式蜗牛，独特风味', price=128.0, category_id=french_category.id, preparation_time=20),
                MenuItem(name='法式洋葱汤', description='法式洋葱汤，浓郁香甜', price=48.0, category_id=french_category.id, preparation_time=25),
                MenuItem(name='法式牛排', description='法式牛排，嫩滑多汁', price=168.0, category_id=french_category.id, preparation_time=20),
                MenuItem(name='法式鸡腿', description='法式香草鸡腿，香嫩可口', price=88.0, category_id=french_category.id, preparation_time=30),
                MenuItem(name='法式面包', description='法式长棍面包，外酥内软', price=25.0, category_id=french_category.id, preparation_time=5),
                MenuItem(name='法式奶酪', description='进口法式奶酪，醇香浓郁', price=68.0, category_id=french_category.id, preparation_time=3),
                MenuItem(name='法式红酒', description='法国波尔多红酒，醇厚浓郁', price=288.0, category_id=french_category.id, preparation_time=3),
                MenuItem(name='法式甜品', description='法式精致甜品，优雅精美', price=58.0, category_id=french_category.id, preparation_time=10),
                MenuItem(name='法式咖啡', description='法式咖啡，香浓醇厚', price=35.0, category_id=french_category.id, preparation_time=8),
            ])
        
        # 西餐 (12种)
        if western_category:
            new_dishes.extend([
                MenuItem(name='牛排套餐', description='西式牛排套餐，配菜丰富', price=128.0, category_id=western_category.id, preparation_time=25),
                MenuItem(name='羊排', description='法式羊排，香嫩可口', price=148.0, category_id=western_category.id, preparation_time=30),
                MenuItem(name='鸡排', description='香煎鸡排，外焦内嫩', price=68.0, category_id=western_category.id, preparation_time=18),
                MenuItem(name='鱼排', description='香煎鱼排，鲜嫩可口', price=78.0, category_id=western_category.id, preparation_time=15),
                MenuItem(name='汉堡包', description='经典汉堡包，丰富配菜', price=48.0, category_id=western_category.id, preparation_time=12),
                MenuItem(name='热狗', description='美式热狗，香肠配菜', price=35.0, category_id=western_category.id, preparation_time=8),
                MenuItem(name='沙拉', description='西式沙拉，新鲜健康', price=38.0, category_id=western_category.id, preparation_time=8),
                MenuItem(name='汤', description='西式汤品，营养丰富', price=32.0, category_id=western_category.id, preparation_time=15),
                MenuItem(name='面包', description='西式面包，松软香甜', price=18.0, category_id=western_category.id, preparation_time=5),
                MenuItem(name='甜品', description='西式甜品，精致美味', price=45.0, category_id=western_category.id, preparation_time=8),
                MenuItem(name='咖啡', description='西式咖啡，香浓醇厚', price=28.0, category_id=western_category.id, preparation_time=5),
                MenuItem(name='红酒', description='进口红酒，醇厚浓郁', price=158.0, category_id=western_category.id, preparation_time=3),
            ])
        
        # 添加到数据库
        for dish in new_dishes:
            db.session.add(dish)
        
        try:
            db.session.commit()
            print(f"成功添加 {len(new_dishes)} 道特色菜品！")
        except Exception as e:
            db.session.rollback()
            print(f"添加菜品失败: {e}")

if __name__ == '__main__':
    add_special_dishes()
