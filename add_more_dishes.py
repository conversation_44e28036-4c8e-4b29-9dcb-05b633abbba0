#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
添加更多菜品到数据库
"""

from app import app, db
from models import Category, MenuItem

def add_more_dishes():
    with app.app_context():
        # 获取分类
        sichuan_category = Category.query.filter_by(name='川菜').first()
        cantonese_category = Category.query.filter_by(name='粤菜').first()
        hunan_category = Category.query.filter_by(name='湘菜').first()
        shandong_category = Category.query.filter_by(name='鲁菜').first()
        jiangsu_category = Category.query.filter_by(name='苏菜').first()
        zhejiang_category = Category.query.filter_by(name='浙菜').first()
        fujian_category = Category.query.filter_by(name='闽菜').first()
        anhui_category = Category.query.filter_by(name='徽菜').first()
        western_category = Category.query.filter_by(name='西餐').first()
        japanese_category = Category.query.filter_by(name='日料').first()
        korean_category = Category.query.filter_by(name='韩料').first()
        thai_category = Category.query.filter_by(name='泰菜').first()
        indian_category = Category.query.filter_by(name='印度菜').first()
        italian_category = Category.query.filter_by(name='意大利菜').first()
        french_category = Category.query.filter_by(name='法国菜').first()
        snack_category = Category.query.filter_by(name='小食').first()
        breakfast_category = Category.query.filter_by(name='早餐').first()
        latenight_category = Category.query.filter_by(name='夜宵').first()
        
        new_dishes = []
        
        # 川菜 (15种)
        if sichuan_category:
            new_dishes.extend([
                MenuItem(name='麻婆豆腐', description='经典川菜麻婆豆腐，麻辣鲜香', price=32.0, category_id=sichuan_category.id, preparation_time=12),
                MenuItem(name='宫保鸡丁', description='宫保鸡丁，酸甜微辣', price=48.0, category_id=sichuan_category.id, preparation_time=15),
                MenuItem(name='回锅肉', description='四川回锅肉，香辣下饭', price=58.0, category_id=sichuan_category.id, preparation_time=18),
                MenuItem(name='鱼香肉丝', description='鱼香肉丝，酸甜开胃', price=42.0, category_id=sichuan_category.id, preparation_time=12),
                MenuItem(name='水煮牛肉', description='水煮牛肉，麻辣鲜嫩', price=78.0, category_id=sichuan_category.id, preparation_time=20),
                MenuItem(name='毛血旺', description='重庆毛血旺，麻辣过瘾', price=68.0, category_id=sichuan_category.id, preparation_time=25),
                MenuItem(name='口水鸡', description='四川口水鸡，麻辣鲜香', price=58.0, category_id=sichuan_category.id, preparation_time=15),
                MenuItem(name='夫妻肺片', description='夫妻肺片，麻辣爽口', price=48.0, category_id=sichuan_category.id, preparation_time=10),
                MenuItem(name='蒜泥白肉', description='蒜泥白肉，清香爽口', price=52.0, category_id=sichuan_category.id, preparation_time=20),
                MenuItem(name='辣子鸡', description='辣子鸡，香辣酥脆', price=68.0, category_id=sichuan_category.id, preparation_time=18),
                MenuItem(name='担担面', description='四川担担面，麻辣鲜香', price=28.0, category_id=sichuan_category.id, preparation_time=12),
                MenuItem(name='酸辣粉', description='重庆酸辣粉，酸辣开胃', price=22.0, category_id=sichuan_category.id, preparation_time=10),
                MenuItem(name='麻辣香锅', description='麻辣香锅，香辣过瘾', price=88.0, category_id=sichuan_category.id, preparation_time=25),
                MenuItem(name='水煮鱼', description='四川水煮鱼，麻辣鲜嫩', price=98.0, category_id=sichuan_category.id, preparation_time=22),
                MenuItem(name='干煸豆角', description='干煸豆角，香辣下饭', price=28.0, category_id=sichuan_category.id, preparation_time=15),
            ])
        
        # 粤菜 (15种)
        if cantonese_category:
            new_dishes.extend([
                MenuItem(name='白切鸡', description='广式白切鸡，清淡鲜美', price=78.0, category_id=cantonese_category.id, preparation_time=30),
                MenuItem(name='蜜汁叉烧', description='广式蜜汁叉烧，甜香可口', price=88.0, category_id=cantonese_category.id, preparation_time=25),
                MenuItem(name='烧鹅', description='广式烧鹅，皮脆肉嫩', price=128.0, category_id=cantonese_category.id, preparation_time=45),
                MenuItem(name='白灼虾', description='白灼虾，鲜甜原味', price=98.0, category_id=cantonese_category.id, preparation_time=8),
                MenuItem(name='蒸排骨', description='豉汁蒸排骨，鲜香可口', price=58.0, category_id=cantonese_category.id, preparation_time=25),
                MenuItem(name='干炒牛河', description='干炒牛河，香滑可口', price=38.0, category_id=cantonese_category.id, preparation_time=12),
                MenuItem(name='艇仔粥', description='广式艇仔粥，鲜美营养', price=28.0, category_id=cantonese_category.id, preparation_time=20),
                MenuItem(name='虾饺', description='广式虾饺，皮薄馅鲜', price=48.0, category_id=cantonese_category.id, preparation_time=15),
                MenuItem(name='烧卖', description='广式烧卖，鲜美可口', price=42.0, category_id=cantonese_category.id, preparation_time=15),
                MenuItem(name='肠粉', description='广式肠粉，嫩滑爽口', price=25.0, category_id=cantonese_category.id, preparation_time=10),
                MenuItem(name='咕噜肉', description='广式咕噜肉，酸甜开胃', price=68.0, category_id=cantonese_category.id, preparation_time=18),
                MenuItem(name='白云猪手', description='白云猪手，软糯香甜', price=58.0, category_id=cantonese_category.id, preparation_time=60),
                MenuItem(name='盐焗鸡', description='客家盐焗鸡，香嫩可口', price=88.0, category_id=cantonese_category.id, preparation_time=40),
                MenuItem(name='煲仔饭', description='广式煲仔饭，香气扑鼻', price=48.0, category_id=cantonese_category.id, preparation_time=30),
                MenuItem(name='双皮奶', description='顺德双皮奶，嫩滑香甜', price=18.0, category_id=cantonese_category.id, preparation_time=5),
            ])
        
        # 湘菜 (12种)
        if hunan_category:
            new_dishes.extend([
                MenuItem(name='剁椒鱼头', description='湖南剁椒鱼头，香辣下饭', price=108.0, category_id=hunan_category.id, preparation_time=30),
                MenuItem(name='口味虾', description='长沙口味虾，麻辣鲜香', price=88.0, category_id=hunan_category.id, preparation_time=20),
                MenuItem(name='臭豆腐', description='长沙臭豆腐，外焦内嫩', price=28.0, category_id=hunan_category.id, preparation_time=8),
                MenuItem(name='糖油粑粑', description='长沙糖油粑粑，香甜可口', price=18.0, category_id=hunan_category.id, preparation_time=10),
                MenuItem(name='毛氏红烧肉', description='毛氏红烧肉，肥而不腻', price=78.0, category_id=hunan_category.id, preparation_time=45),
                MenuItem(name='湘西外婆菜', description='湘西外婆菜，酸辣开胃', price=32.0, category_id=hunan_category.id, preparation_time=12),
                MenuItem(name='辣椒炒肉', description='湖南辣椒炒肉，香辣下饭', price=48.0, category_id=hunan_category.id, preparation_time=15),
                MenuItem(name='血鸭', description='永州血鸭，鲜香可口', price=88.0, category_id=hunan_category.id, preparation_time=35),
                MenuItem(name='腊味合蒸', description='湖南腊味合蒸，香味浓郁', price=68.0, category_id=hunan_category.id, preparation_time=40),
                MenuItem(name='酸豆角炒肉末', description='酸豆角炒肉末，酸辣开胃', price=38.0, category_id=hunan_category.id, preparation_time=12),
                MenuItem(name='湘西土匪鸭', description='湘西土匪鸭，香辣过瘾', price=98.0, category_id=hunan_category.id, preparation_time=50),
                MenuItem(name='长沙米粉', description='长沙米粉，爽滑可口', price=22.0, category_id=hunan_category.id, preparation_time=8),
            ])
        
        # 鲁菜 (10种)
        if shandong_category:
            new_dishes.extend([
                MenuItem(name='糖醋鲤鱼', description='山东糖醋鲤鱼，酸甜可口', price=88.0, category_id=shandong_category.id, preparation_time=25),
                MenuItem(name='九转大肠', description='济南九转大肠，肥而不腻', price=68.0, category_id=shandong_category.id, preparation_time=45),
                MenuItem(name='德州扒鸡', description='德州扒鸡，香嫩可口', price=78.0, category_id=shandong_category.id, preparation_time=60),
                MenuItem(name='油爆双脆', description='油爆双脆，脆嫩爽口', price=58.0, category_id=shandong_category.id, preparation_time=15),
                MenuItem(name='葱烧海参', description='葱烧海参，鲜美滋补', price=188.0, category_id=shandong_category.id, preparation_time=30),
                MenuItem(name='爆炒腰花', description='爆炒腰花，嫩滑鲜美', price=48.0, category_id=shandong_category.id, preparation_time=12),
                MenuItem(name='锅塌豆腐', description='锅塌豆腐，外焦内嫩', price=32.0, category_id=shandong_category.id, preparation_time=15),
                MenuItem(name='四喜丸子', description='四喜丸子，寓意吉祥', price=58.0, category_id=shandong_category.id, preparation_time=30),
                MenuItem(name='白扒四宝', description='白扒四宝，营养丰富', price=88.0, category_id=shandong_category.id, preparation_time=25),
                MenuItem(name='蒲菜炒肉丝', description='蒲菜炒肉丝，清香爽口', price=38.0, category_id=shandong_category.id, preparation_time=12),
            ])
        
        # 添加到数据库
        for dish in new_dishes:
            db.session.add(dish)
        
        try:
            db.session.commit()
            print(f"成功添加 {len(new_dishes)} 道菜品！")
        except Exception as e:
            db.session.rollback()
            print(f"添加菜品失败: {e}")

if __name__ == '__main__':
    add_more_dishes()
