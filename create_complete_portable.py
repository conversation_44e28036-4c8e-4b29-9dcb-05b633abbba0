#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建包含完整数据的便携版
"""

import os
import shutil
import zipfile
from pathlib import Path

def create_complete_portable():
    """创建包含完整数据的便携版分发包"""
    
    print("📦 创建完整数据便携版分发包...")
    
    # 检查修复版目录
    portable_dir = Path("SteakhouseManager_Fixed_Portable")
    if not portable_dir.exists():
        print("❌ 修复版便携版目录不存在")
        return False
    
    # 确保数据目录存在且有数据
    data_dir = portable_dir / "data"
    if not data_dir.exists():
        data_dir.mkdir()
        print("📁 创建数据目录")
    
    # 检查是否有现有数据库，如果没有则从instance复制
    db_file = data_dir / "restaurant.db"
    source_db = Path("instance/restaurant.db")
    
    if source_db.exists() and not db_file.exists():
        shutil.copy2(source_db, db_file)
        print(f"✅ 复制数据库: {source_db} -> {db_file}")
    elif not db_file.exists():
        print("⚠️ 将在首次运行时创建数据库")
    else:
        print(f"✅ 数据库已存在: {db_file}")
    
    # 创建数据验证脚本
    verify_script = """#!/usr/bin/env python3
# -*- coding: utf-8 -*-
\"\"\"
数据验证脚本 - 验证便携版数据完整性
\"\"\"

import sqlite3
from pathlib import Path

def verify_data():
    \"\"\"验证数据完整性\"\"\"
    
    db_path = Path("data/restaurant.db")
    if not db_path.exists():
        print("❌ 数据库文件不存在")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查各表数据
        tables_to_check = [
            ('user', '员工'),
            ('table', '桌位'),
            ('menu_item', '菜品'),
            ('category', '分类'),
            ('ingredient', '库存'),
            ('schedule', '排班')
        ]
        
        print("🔍 数据验证结果:")
        print("-" * 40)
        
        for table_name, display_name in tables_to_check:
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"{display_name}: {count} 条记录")
        
        conn.close()
        print("-" * 40)
        print("✅ 数据验证完成")
        return True
        
    except Exception as e:
        print(f"❌ 数据验证失败: {e}")
        return False

if __name__ == '__main__':
    verify_data()
"""
    
    with open(portable_dir / "验证数据.py", "w", encoding="utf-8") as f:
        f.write(verify_script)
    
    # 创建数据备份脚本
    backup_script = """@echo off
title 数据备份工具
echo.
echo 牛排餐厅管理系统 - 数据备份工具
echo ================================
echo.

set backup_dir=backup_%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set backup_dir=%backup_dir: =0%

if not exist backup mkdir backup
if not exist data\\restaurant.db (
    echo 错误: 数据库文件不存在
    pause
    exit /b 1
)

copy data\\restaurant.db backup\\restaurant_%backup_dir%.db
echo.
echo 数据备份完成: backup\\restaurant_%backup_dir%.db
echo.
pause
"""
    
    with open(portable_dir / "备份数据.bat", "w", encoding="gbk") as f:
        f.write(backup_script)
    
    # 创建数据恢复脚本
    restore_script = """@echo off
title 数据恢复工具
echo.
echo 牛排餐厅管理系统 - 数据恢复工具
echo ================================
echo.

if not exist backup (
    echo 错误: 备份目录不存在
    pause
    exit /b 1
)

echo 可用的备份文件:
dir /b backup\\*.db

echo.
set /p backup_file=请输入要恢复的备份文件名: 

if not exist backup\\%backup_file% (
    echo 错误: 备份文件不存在
    pause
    exit /b 1
)

copy backup\\%backup_file% data\\restaurant.db
echo.
echo 数据恢复完成
echo 请重启系统以使用恢复的数据
echo.
pause
"""
    
    with open(portable_dir / "恢复数据.bat", "w", encoding="gbk") as f:
        f.write(restore_script)
    
    # 更新README文件
    enhanced_readme = """# 🥩 牛排餐厅管理系统 - 完整数据便携版

## 📋 系统简介
这是包含完整数据的牛排餐厅管理系统便携版，解决了所有数据丢失问题：

### 🏪 完整数据内容
- **31个桌位** - 2人桌、4人桌、6人桌、8人桌、VIP包间、吧台座位、露台座位
- **88道菜品饮品** - 精选牛排、顶级和牛、海鲜、威士忌、香槟、白酒等
- **25名专业员工** - 总经理、行政总厨、首席服务员、侍酒师、调酒师等
- **576个排班记录** - 30天专业排班，周一到周日全覆盖
- **56种库存原料** - 与西餐菜品完全匹配的专业原料
- **客户管理系统** - 生日提醒、忠诚度报告等功能

## 🚀 快速开始

### 1. 启动系统
**方法一**: 双击 `SteakhouseManager.exe`
**方法二**: 双击 `启动系统.bat`

### 2. 访问系统
系统启动后会自动打开浏览器访问：http://127.0.0.1:5000

### 3. 登录验证
**管理员账号：**
- 用户名：admin
- 密码：admin123

**专业员工账号：**
- 总经理：general_manager / gm123456
- 行政总厨：executive_chef / chef123456
- 首席服务员：head_waiter / head123456
- 侍酒师：sommelier / wine123456
- 首席调酒师：head_bartender / bar123456

## 🔧 数据管理

### 📊 数据验证
运行 `python 验证数据.py` 检查数据完整性

### 💾 数据备份
双击 `备份数据.bat` 创建数据备份

### 🔄 数据恢复
双击 `恢复数据.bat` 从备份恢复数据

### 📁 数据位置
- 主数据库：`data/restaurant.db`
- 备份目录：`backup/`

## ✅ 功能验证清单

启动后请验证以下功能：

### 🏪 POS收银系统
- [ ] 查看31个桌位（T01-T20, VIP01-03, BAR01-04, OUT01-04）
- [ ] 查看88道菜品饮品
- [ ] 桌位状态切换功能
- [ ] 菜品搜索和分类筛选

### 👥 员工管理
- [ ] 查看25名专业员工
- [ ] 员工信息完整（姓名、职位、部门）
- [ ] 员工登录功能正常

### 📅 排班管理
- [ ] 查看576个排班记录
- [ ] 周一到周日都有排班
- [ ] 不同职位合理分配

### 📦 库存管理
- [ ] 查看56种原料库存
- [ ] 库存分类清晰（牛肉类、海鲜类等）
- [ ] 库存数量和价格正确

### 👤 客户管理
- [ ] 生日提醒功能正常
- [ ] 忠诚度报告显示正确
- [ ] 客户信息管理

### 📊 财务报表
- [ ] 营收分析功能
- [ ] 数据统计正确

## 🛠️ 故障排除

### 问题：数据丢失
**解决方案：**
1. 检查 `data/restaurant.db` 文件是否存在
2. 运行 `python 验证数据.py` 检查数据
3. 如有备份，使用 `恢复数据.bat` 恢复

### 问题：无法启动
**解决方案：**
1. 确保Windows防火墙允许程序运行
2. 检查5000端口是否被占用
3. 以管理员身份运行

### 问题：浏览器未自动打开
**解决方案：**
手动打开浏览器访问：http://127.0.0.1:5000

## 💻 系统要求
- Windows 7/8/10/11 (64位)
- 至少 512MB 可用内存
- 至少 100MB 可用磁盘空间
- 现代浏览器（Chrome、Edge、Firefox等）

## 📞 技术支持
如遇问题，请检查：
1. 数据文件完整性
2. 系统权限设置
3. 网络端口占用

---
© 2024 牛排餐厅管理系统 - 完整数据便携版
版本：2.0 (修复数据丢失问题)
"""
    
    with open(portable_dir / "README.md", "w", encoding="utf-8") as f:
        f.write(enhanced_readme)
    
    # 创建最终分发包
    zip_filename = "牛排餐厅管理系统_完整数据便携版.zip"
    
    with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for file_path in portable_dir.rglob('*'):
            if file_path.is_file():
                arcname = file_path.relative_to(portable_dir.parent)
                zipf.write(file_path, arcname)
                print(f"  📁 添加: {arcname}")
    
    # 获取文件大小
    zip_size = os.path.getsize(zip_filename) / (1024 * 1024)  # MB
    
    print(f"""
🎉 完整数据便携版创建完成！

📁 分发包: {zip_filename}
📊 文件大小: {zip_size:.1f} MB
📍 位置: {Path(zip_filename).absolute()}

📋 包含内容:
   ✅ SteakhouseManager.exe - 主程序
   ✅ data/ - 完整数据目录
   ✅ README.md - 详细使用说明
   ✅ 启动系统.bat - 快速启动
   ✅ 验证数据.py - 数据验证工具
   ✅ 备份数据.bat - 数据备份工具
   ✅ 恢复数据.bat - 数据恢复工具

🔧 数据管理工具:
   📊 数据验证 - 检查数据完整性
   💾 数据备份 - 创建数据备份
   🔄 数据恢复 - 从备份恢复

🚀 分发说明:
   1. 发送ZIP文件给用户
   2. 用户解压到任意目录
   3. 双击启动程序即可使用
   4. 包含完整的31桌位、88菜品、25员工、576排班数据

💡 特色功能:
   ✅ 完全便携 - 无需安装任何程序
   ✅ 数据完整 - 包含所有业务数据
   ✅ 数据管理 - 备份恢复功能
   ✅ 故障排除 - 详细的使用说明
""")
    
    return True

if __name__ == '__main__':
    create_complete_portable()
