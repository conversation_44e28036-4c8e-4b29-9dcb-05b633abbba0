#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
AllSportsNow 便携版主程序
专为便携版优化的启动脚本
"""

import os
import sys
import time
import webbrowser
import threading
import socket
from pathlib import Path

def check_port_available(port):
    """检查端口是否可用"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('127.0.0.1', port))
            return True
    except OSError:
        return False

def find_available_port(start_port=5000, max_attempts=10):
    """查找可用端口"""
    for i in range(max_attempts):
        port = start_port + i
        if check_port_available(port):
            return port
    return None

def open_browser(url):
    """延迟打开浏览器"""
    time.sleep(3)  # 等待服务器启动
    try:
        webbrowser.open(url)
        print(f"🌐 浏览器已打开: {url}")
    except Exception as e:
        print(f"⚠️ 无法自动打开浏览器: {e}")
        print(f"请手动访问: {url}")

def setup_environment():
    """设置运行环境"""
    # 获取当前目录
    if getattr(sys, 'frozen', False):
        # 如果是打包后的可执行文件
        current_dir = Path(sys.executable).parent
    else:
        # 如果是Python脚本
        current_dir = Path(__file__).parent
    
    # 设置工作目录
    os.chdir(current_dir)
    
    # 添加到Python路径
    sys.path.insert(0, str(current_dir))
    
    return current_dir

def print_banner():
    """打印启动横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║     🏆 AllSportsNow - 全球体育赛事信息平台                    ║
║                                                              ║
║     🌟 功能特色:                                              ║
║        • 全球体育赛事信息                                     ║
║        • 中英文双语支持                                       ║
║        • 响应式设计                                           ║
║        • 智能搜索功能                                         ║
║        • 详细赛程和场馆信息                                   ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
"""
    print(banner)

def main():
    """主函数"""
    try:
        # 设置环境
        current_dir = setup_environment()
        
        # 打印横幅
        print_banner()
        
        # 查找可用端口
        port = find_available_port()
        if not port:
            print("❌ 错误: 无法找到可用端口 (5000-5009)")
            input("按回车键退出...")
            return
        
        url = f"http://127.0.0.1:{port}"
        
        print(f"📍 服务器地址: {url}")
        print(f"📁 工作目录: {current_dir}")
        print("🌐 浏览器将自动打开...")
        print("❌ 按 Ctrl+C 停止服务器")
        print("=" * 60)
        
        # 在后台线程中打开浏览器
        browser_thread = threading.Thread(target=open_browser, args=(url,))
        browser_thread.daemon = True
        browser_thread.start()
        
        # 导入并启动Flask应用
        try:
            # 设置Flask环境变量
            os.environ['FLASK_ENV'] = 'production'
            os.environ['FLASK_DEBUG'] = 'False'
            
            from app import app
            
            # 配置应用
            app.config['DEBUG'] = False
            app.config['TESTING'] = False
            app.config['PROPAGATE_EXCEPTIONS'] = True
            
            print("✅ 应用加载成功")
            print(f"🚀 启动服务器在端口 {port}...")
            
            # 启动服务器
            app.run(
                host='127.0.0.1', 
                port=port, 
                debug=False,
                use_reloader=False,
                threaded=True
            )
            
        except ImportError as e:
            print(f"❌ 导入错误: {e}")
            print("请确保所有必要文件都在当前目录中")
            input("按回车键退出...")
            
        except Exception as e:
            print(f"❌ 启动错误: {e}")
            input("按回车键退出...")
            
    except KeyboardInterrupt:
        print("\n👋 AllSportsNow 已停止")
        print("感谢使用！")
        
    except Exception as e:
        print(f"❌ 意外错误: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
