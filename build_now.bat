@echo off
chcp 65001 >nul
title AllSportsNow 便携版构建器

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                                                              ║
echo ║     🚀 AllSportsNow 便携版构建器                              ║
echo ║                                                              ║
echo ║     这将创建一个无需安装Python的便携版应用程序                 ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 📋 构建步骤:
echo    1. 安装构建依赖
echo    2. 创建应用程序图标
echo    3. 构建可执行文件
echo    4. 创建附加文件
echo    5. 打包压缩文件
echo.

set /p confirm="确认开始构建? (y/N): "
if /i not "%confirm%"=="y" (
    echo 构建已取消
    pause
    exit /b
)

echo.
echo 🚀 开始构建...
echo.

python build_portable.py

echo.
echo 构建完成！
pause
