{% extends "base.html" %}

{% block title %}仪表板 - 牛排餐厅管理系统{% endblock %}
{% block page_title %}仪表板{% endblock %}

{% block content %}
<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">今日营业额</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">¥{{ "%.2f"|format(today_revenue) }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">今日订单</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ today_order_count }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clipboard-list fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">今日预订</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ today_reservations }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-calendar fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">可用桌位</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ available_tables }}/{{ total_tables }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-chair fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 收入趋势图 -->
    <div class="col-xl-8 col-lg-7">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">收入趋势（最近7天）</h6>
            </div>
            <div class="card-body">
                <div class="chart-area">
                    <canvas id="revenueChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 热门菜品 -->
    <div class="col-xl-4 col-lg-5">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">热门菜品（本周）</h6>
            </div>
            <div class="card-body">
                {% if popular_items %}
                    {% for item in popular_items %}
                    <div class="d-flex align-items-center mb-3">
                        <div class="flex-grow-1">
                            <h6 class="mb-0">{{ item.name }}</h6>
                            <small class="text-muted">销量：{{ item.total_quantity }}</small>
                        </div>
                        <div class="badge bg-primary rounded-pill">{{ loop.index }}</div>
                    </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted">暂无数据</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 库存警告 -->
    <div class="col-xl-6 col-lg-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-warning">库存警告</h6>
                <a href="{{ url_for('inventory.index') }}" class="btn btn-sm btn-outline-warning">查看详情</a>
            </div>
            <div class="card-body">
                {% if low_stock_items %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>原料名称</th>
                                    <th>当前库存</th>
                                    <th>最低库存</th>
                                    <th>状态</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in low_stock_items %}
                                <tr>
                                    <td>{{ item.name }}</td>
                                    <td>{{ item.current_stock }} {{ item.unit }}</td>
                                    <td>{{ item.min_stock }} {{ item.unit }}</td>
                                    <td>
                                        {% if item.current_stock <= 5 %}
                                            <span class="badge bg-danger">紧急</span>
                                        {% else %}
                                            <span class="badge bg-warning">偏低</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-success mb-0">
                        <i class="fas fa-check-circle"></i>
                        所有库存充足
                    </p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- 快速操作 -->
    <div class="col-xl-6 col-lg-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">快速操作</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6 mb-3">
                        <a href="{{ url_for('pos.index') }}" class="btn btn-primary btn-block">
                            <i class="fas fa-cash-register"></i><br>
                            POS收银
                        </a>
                    </div>
                    <div class="col-6 mb-3">
                        <a href="{{ url_for('reservation.manage') }}" class="btn btn-info btn-block">
                            <i class="fas fa-calendar-alt"></i><br>
                            预订管理
                        </a>
                    </div>
                    {% if current_user.role in ['admin', 'manager'] %}
                    <div class="col-6 mb-3">
                        <a href="{{ url_for('inventory.index') }}" class="btn btn-warning btn-block">
                            <i class="fas fa-boxes"></i><br>
                            库存管理
                        </a>
                    </div>
                    <div class="col-6 mb-3">
                        <a href="{{ url_for('staff.index') }}" class="btn btn-success btn-block">
                            <i class="fas fa-users"></i><br>
                            员工管理
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}
.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}
.btn-block {
    display: block;
    width: 100%;
    text-align: center;
    padding: 1rem;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// 收入趋势图
var ctx = document.getElementById('revenueChart').getContext('2d');
var revenueChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: [
            {% for day in revenue_trend %}
                '{{ day.date }}'{% if not loop.last %},{% endif %}
            {% endfor %}
        ],
        datasets: [{
            label: '营业额',
            data: [
                {% for day in revenue_trend %}
                    {{ day.revenue }}{% if not loop.last %},{% endif %}
                {% endfor %}
            ],
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.1)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        plugins: {
            title: {
                display: true,
                text: '最近7天营业额趋势'
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value, index, values) {
                        return '¥' + value;
                    }
                }
            }
        }
    }
});

// 定时刷新数据
setInterval(function() {
    fetch('/api/dashboard/stats')
        .then(response => response.json())
        .then(data => {
            // 更新统计数据
            console.log('Dashboard stats updated:', data);
        })
        .catch(error => console.error('Error:', error));
}, 30000); // 每30秒刷新一次
</script>
{% endblock %}
