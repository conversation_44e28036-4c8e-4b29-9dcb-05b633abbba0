{% extends "base.html" %}

{% block title %}仪表板 - 牛排餐厅管理系统{% endblock %}
{% block page_title %}仪表板{% endblock %}

{% block content %}
<!-- 欢迎横幅 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="welcome-banner card border-0 overflow-hidden" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 1.5rem;">
            <div class="card-body p-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h2 class="mb-2 fw-bold">
                            <i class="fas fa-sun me-2"></i>
                            早上好，{{ current_user.name }}！
                        </h2>
                        <p class="mb-0 opacity-75">欢迎回到牛排餐厅管理系统，今天也要加油哦！</p>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="current-time fs-4 fw-bold" id="currentTime"></div>
                        <small class="opacity-75">当前时间</small>
                    </div>
                </div>
            </div>
            <!-- 装饰性元素 -->
            <div class="position-absolute top-0 end-0 p-4 opacity-25">
                <i class="fas fa-chart-line fa-4x"></i>
            </div>
        </div>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stat-card card border-0 h-100 overflow-hidden" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
            <div class="card-body text-white p-4">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stat-label small opacity-75 mb-1">今日营业额</div>
                        <div class="stat-value h3 mb-0 fw-bold" data-target="{{ today_revenue }}">¥0</div>
                        <div class="stat-change small mt-1">
                            <i class="fas fa-arrow-up me-1"></i>
                            <span>+12.5% 较昨日</span>
                        </div>
                    </div>
                    <div class="stat-icon">
                        <div class="icon-circle bg-white bg-opacity-20 rounded-circle p-3">
                            <i class="fas fa-dollar-sign fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-footer bg-white bg-opacity-10 border-0 p-3">
                <small class="text-white opacity-75">
                    <i class="fas fa-clock me-1"></i>
                    实时更新
                </small>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stat-card card border-0 h-100 overflow-hidden" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);">
            <div class="card-body text-white p-4">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stat-label small opacity-75 mb-1">今日订单</div>
                        <div class="stat-value h3 mb-0 fw-bold" data-target="{{ today_order_count }}">0</div>
                        <div class="stat-change small mt-1">
                            <i class="fas fa-arrow-up me-1"></i>
                            <span>+8.2% 较昨日</span>
                        </div>
                    </div>
                    <div class="stat-icon">
                        <div class="icon-circle bg-white bg-opacity-20 rounded-circle p-3">
                            <i class="fas fa-clipboard-list fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-footer bg-white bg-opacity-10 border-0 p-3">
                <small class="text-white opacity-75">
                    <i class="fas fa-chart-bar me-1"></i>
                    趋势良好
                </small>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stat-card card border-0 h-100 overflow-hidden" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);">
            <div class="card-body text-white p-4">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stat-label small opacity-75 mb-1">今日预订</div>
                        <div class="stat-value h3 mb-0 fw-bold" data-target="{{ today_reservations }}">0</div>
                        <div class="stat-change small mt-1">
                            <i class="fas fa-arrow-up me-1"></i>
                            <span>+15.3% 较昨日</span>
                        </div>
                    </div>
                    <div class="stat-icon">
                        <div class="icon-circle bg-white bg-opacity-20 rounded-circle p-3">
                            <i class="fas fa-calendar fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-footer bg-white bg-opacity-10 border-0 p-3">
                <small class="text-white opacity-75">
                    <i class="fas fa-users me-1"></i>
                    客流稳定
                </small>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stat-card card border-0 h-100 overflow-hidden" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
            <div class="card-body text-white p-4">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stat-label small opacity-75 mb-1">可用桌位</div>
                        <div class="stat-value h3 mb-0 fw-bold">{{ available_tables }}/{{ total_tables }}</div>
                        <div class="stat-change small mt-1">
                            <i class="fas fa-info-circle me-1"></i>
                            <span>{{ ((available_tables/total_tables)*100)|round(1) }}% 可用</span>
                        </div>
                    </div>
                    <div class="stat-icon">
                        <div class="icon-circle bg-white bg-opacity-20 rounded-circle p-3">
                            <i class="fas fa-chair fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-footer bg-white bg-opacity-10 border-0 p-3">
                <small class="text-white opacity-75">
                    <i class="fas fa-sync me-1"></i>
                    实时状态
                </small>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 收入趋势图 -->
    <div class="col-xl-8 col-lg-7">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">收入趋势（最近7天）</h6>
            </div>
            <div class="card-body">
                <div class="chart-area">
                    <canvas id="revenueChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- 热门菜品 -->
    <div class="col-xl-4 col-lg-5">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">热门菜品（本周）</h6>
            </div>
            <div class="card-body">
                {% if popular_items %}
                    {% for item in popular_items %}
                    <div class="d-flex align-items-center mb-3">
                        <div class="flex-grow-1">
                            <h6 class="mb-0">{{ item.name }}</h6>
                            <small class="text-muted">销量：{{ item.total_quantity }}</small>
                        </div>
                        <div class="badge bg-primary rounded-pill">{{ loop.index }}</div>
                    </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted">暂无数据</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 库存警告 -->
    <div class="col-xl-6 col-lg-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-warning">库存警告</h6>
                <a href="{{ url_for('inventory.index') }}" class="btn btn-sm btn-outline-warning">查看详情</a>
            </div>
            <div class="card-body">
                {% if low_stock_items %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>原料名称</th>
                                    <th>当前库存</th>
                                    <th>最低库存</th>
                                    <th>状态</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in low_stock_items %}
                                <tr>
                                    <td>{{ item.name }}</td>
                                    <td>{{ item.current_stock }} {{ item.unit }}</td>
                                    <td>{{ item.min_stock }} {{ item.unit }}</td>
                                    <td>
                                        {% if item.current_stock <= 5 %}
                                            <span class="badge bg-danger">紧急</span>
                                        {% else %}
                                            <span class="badge bg-warning">偏低</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-success mb-0">
                        <i class="fas fa-check-circle"></i>
                        所有库存充足
                    </p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- 快速操作 -->
    <div class="col-xl-6 col-lg-6">
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-transparent border-0 py-4">
                <h5 class="mb-0 fw-bold">
                    <i class="fas fa-bolt text-warning me-2"></i>
                    快速操作
                </h5>
                <small class="text-muted">常用功能快速入口</small>
            </div>
            <div class="card-body p-4">
                <div class="row g-3">
                    <div class="col-6">
                        <a href="{{ url_for('pos.index') }}" class="quick-action-btn btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center p-4 border-2">
                            <div class="quick-action-icon mb-3">
                                <i class="fas fa-cash-register fa-2x"></i>
                            </div>
                            <div class="quick-action-text">
                                <div class="fw-bold">POS收银</div>
                                <small class="text-muted">订单管理</small>
                            </div>
                        </a>
                    </div>
                    <div class="col-6">
                        <a href="{{ url_for('reservation.manage') }}" class="quick-action-btn btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center p-4 border-2">
                            <div class="quick-action-icon mb-3">
                                <i class="fas fa-calendar-alt fa-2x"></i>
                            </div>
                            <div class="quick-action-text">
                                <div class="fw-bold">预订管理</div>
                                <small class="text-muted">桌位预订</small>
                            </div>
                        </a>
                    </div>
                    {% if current_user.role in ['admin', 'manager'] %}
                    <div class="col-6">
                        <a href="{{ url_for('inventory.index') }}" class="quick-action-btn btn btn-outline-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center p-4 border-2">
                            <div class="quick-action-icon mb-3">
                                <i class="fas fa-boxes fa-2x"></i>
                            </div>
                            <div class="quick-action-text">
                                <div class="fw-bold">库存管理</div>
                                <small class="text-muted">原料监控</small>
                            </div>
                        </a>
                    </div>
                    <div class="col-6">
                        <a href="{{ url_for('staff.index') }}" class="quick-action-btn btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center p-4 border-2">
                            <div class="quick-action-icon mb-3">
                                <i class="fas fa-users fa-2x"></i>
                            </div>
                            <div class="quick-action-text">
                                <div class="fw-bold">员工管理</div>
                                <small class="text-muted">人事管理</small>
                            </div>
                        </a>
                    </div>
                    {% endif %}
                    <div class="col-6">
                        <a href="{{ url_for('customer.index') }}" class="quick-action-btn btn btn-outline-secondary w-100 h-100 d-flex flex-column align-items-center justify-content-center p-4 border-2">
                            <div class="quick-action-icon mb-3">
                                <i class="fas fa-user-friends fa-2x"></i>
                            </div>
                            <div class="quick-action-text">
                                <div class="fw-bold">客户管理</div>
                                <small class="text-muted">客户关系</small>
                            </div>
                        </a>
                    </div>
                    <div class="col-6">
                        <a href="{{ url_for('staff.my_schedule') }}" class="quick-action-btn btn btn-outline-dark w-100 h-100 d-flex flex-column align-items-center justify-content-center p-4 border-2">
                            <div class="quick-action-icon mb-3">
                                <i class="fas fa-clock fa-2x"></i>
                            </div>
                            <div class="quick-action-text">
                                <div class="fw-bold">我的排班</div>
                                <small class="text-muted">个人考勤</small>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    /* 欢迎横幅动画 */
    .welcome-banner {
        animation: slideInDown 0.8s ease-out;
        position: relative;
        overflow: hidden;
    }

    .welcome-banner::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: rotate 20s linear infinite;
    }

    @keyframes rotate {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* 统计卡片动画 */
    .stat-card {
        animation: fadeInUp 0.6s ease-out;
        animation-fill-mode: both;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .stat-card:nth-child(1) { animation-delay: 0.1s; }
    .stat-card:nth-child(2) { animation-delay: 0.2s; }
    .stat-card:nth-child(3) { animation-delay: 0.3s; }
    .stat-card:nth-child(4) { animation-delay: 0.4s; }

    .stat-card:hover {
        transform: translateY(-10px) scale(1.02);
        box-shadow: 0 1rem 3rem rgba(0,0,0,0.2) !important;
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .stat-card:hover::before {
        left: 100%;
    }

    /* 图标动画 */
    .icon-circle {
        transition: all 0.3s ease;
    }

    .stat-card:hover .icon-circle {
        transform: scale(1.1) rotate(5deg);
    }

    /* 数值计数动画 */
    .stat-value {
        transition: all 0.3s ease;
    }

    /* 快速操作按钮 */
    .quick-action-btn {
        transition: all 0.3s ease;
        border-radius: 1rem !important;
        min-height: 120px;
        position: relative;
        overflow: hidden;
    }

    .quick-action-btn:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.15);
    }

    .quick-action-btn:hover .quick-action-icon {
        transform: scale(1.1);
    }

    .quick-action-icon {
        transition: all 0.3s ease;
    }

    /* 图表容器增强 */
    .chart-area {
        position: relative;
        height: 400px;
        padding: 1rem;
    }

    /* 库存警告表格 */
    .table-responsive {
        border-radius: 0.75rem;
        overflow: hidden;
    }

    /* 热门菜品列表 */
    .popular-item {
        transition: all 0.3s ease;
        border-radius: 0.5rem;
        padding: 0.75rem;
        margin-bottom: 0.5rem;
    }

    .popular-item:hover {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
        transform: translateX(5px);
    }

    /* 响应式优化 */
    @media (max-width: 768px) {
        .stat-card {
            margin-bottom: 1rem;
        }

        .quick-action-btn {
            min-height: 100px;
        }

        .welcome-banner h2 {
            font-size: 1.5rem;
        }
    }

    /* 加载状态 */
    .loading-shimmer {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: shimmer 2s infinite;
    }

    @keyframes shimmer {
        0% { background-position: -200% 0; }
        100% { background-position: 200% 0; }
    }

    /* 实时更新指示器 */
    .live-indicator {
        display: inline-block;
        width: 8px;
        height: 8px;
        background: #28a745;
        border-radius: 50%;
        animation: pulse 2s infinite;
        margin-right: 0.5rem;
    }

    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
    }

    /* 时间显示 */
    #currentTime {
        font-family: 'Courier New', monospace;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 初始化时间显示
    updateTime();
    setInterval(updateTime, 1000);

    // 数字计数动画
    animateCounters();

    // 初始化图表
    initializeChart();

    // 定时刷新数据
    setInterval(refreshDashboardData, 30000);

    // 快速操作按钮点击效果
    $('.quick-action-btn').on('click', function(e) {
        $(this).addClass('clicked');
        setTimeout(() => {
            $(this).removeClass('clicked');
        }, 200);
    });

    // 统计卡片点击效果
    $('.stat-card').on('click', function() {
        $(this).addClass('pulse');
        setTimeout(() => {
            $(this).removeClass('pulse');
        }, 600);
    });
});

// 更新时间显示
function updateTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('zh-CN', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
    $('#currentTime').text(timeString);

    // 根据时间更新问候语
    const hour = now.getHours();
    let greeting = '早上好';
    if (hour >= 12 && hour < 18) {
        greeting = '下午好';
    } else if (hour >= 18) {
        greeting = '晚上好';
    }

    $('.welcome-banner h2').html(`
        <i class="fas fa-sun me-2"></i>
        ${greeting}，{{ current_user.name }}！
    `);
}

// 数字计数动画
function animateCounters() {
    $('.stat-value[data-target]').each(function() {
        const $this = $(this);
        const target = parseFloat($this.data('target'));
        const prefix = $this.text().includes('¥') ? '¥' : '';

        $({ counter: 0 }).animate({ counter: target }, {
            duration: 2000,
            easing: 'swing',
            step: function() {
                if (prefix === '¥') {
                    $this.text(prefix + Math.ceil(this.counter).toFixed(2));
                } else {
                    $this.text(prefix + Math.ceil(this.counter));
                }
            },
            complete: function() {
                if (prefix === '¥') {
                    $this.text(prefix + target.toFixed(2));
                } else {
                    $this.text(prefix + target);
                }
            }
        });
    });
}

// 初始化图表
function initializeChart() {
    const ctx = document.getElementById('revenueChart');
    if (!ctx) return;

    const gradient = ctx.getContext('2d').createLinearGradient(0, 0, 0, 400);
    gradient.addColorStop(0, 'rgba(102, 126, 234, 0.3)');
    gradient.addColorStop(1, 'rgba(102, 126, 234, 0.05)');

    new Chart(ctx, {
        type: 'line',
        data: {
            labels: [
                {% for day in revenue_trend %}
                    '{{ day.date }}'{% if not loop.last %},{% endif %}
                {% endfor %}
            ],
            datasets: [{
                label: '营业额',
                data: [
                    {% for day in revenue_trend %}
                        {{ day.revenue }}{% if not loop.last %},{% endif %}
                    {% endfor %}
                ],
                borderColor: '#667eea',
                backgroundColor: gradient,
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: '#667eea',
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
                pointRadius: 6,
                pointHoverRadius: 8
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(0,0,0,0.8)',
                    titleColor: '#ffffff',
                    bodyColor: '#ffffff',
                    borderColor: '#667eea',
                    borderWidth: 1,
                    cornerRadius: 8,
                    callbacks: {
                        label: function(context) {
                            return '营业额: ¥' + context.parsed.y.toFixed(2);
                        }
                    }
                }
            },
            scales: {
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        color: '#6c757d'
                    }
                },
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    },
                    ticks: {
                        color: '#6c757d',
                        callback: function(value) {
                            return '¥' + value;
                        }
                    }
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            }
        }
    });
}

// 刷新仪表板数据
function refreshDashboardData() {
    fetch('/api/dashboard/stats')
        .then(response => response.json())
        .then(data => {
            // 更新统计数据
            updateStatCard('today_revenue', data.today_revenue, '¥');
            updateStatCard('today_orders', data.today_orders);
            updateStatCard('today_reservations', data.today_reservations);

            // 显示更新指示器
            showUpdateIndicator();
        })
        .catch(error => {
            console.error('刷新数据失败:', error);
            showErrorIndicator();
        });
}

// 更新统计卡片
function updateStatCard(type, value, prefix = '') {
    const $card = $(`.stat-value[data-type="${type}"]`);
    if ($card.length) {
        $card.addClass('updating');
        setTimeout(() => {
            $card.text(prefix + value);
            $card.removeClass('updating');
        }, 300);
    }
}

// 显示更新指示器
function showUpdateIndicator() {
    const indicator = '<span class="live-indicator"></span>';
    $('.card-footer:contains("实时更新")').html(indicator + '刚刚更新');

    setTimeout(() => {
        $('.card-footer:contains("刚刚更新")').html(indicator + '实时更新');
    }, 3000);
}

// 显示错误指示器
function showErrorIndicator() {
    $('.card-footer:contains("实时更新")').html('<i class="fas fa-exclamation-triangle text-warning me-1"></i>更新失败');

    setTimeout(() => {
        $('.card-footer:contains("更新失败")').html('<i class="fas fa-clock me-1"></i>实时更新');
    }, 5000);
}

// 添加点击动画CSS
$('<style>')
    .prop('type', 'text/css')
    .html(`
        .clicked {
            transform: scale(0.95) !important;
        }
        .updating {
            opacity: 0.6;
            transform: scale(1.05);
        }
        .pulse {
            animation: cardPulse 0.6s ease-in-out;
        }
        @keyframes cardPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.02); }
        }
        @keyframes slideInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    `)
    .appendTo('head');

// 页面可见性API - 当页面重新获得焦点时刷新数据
document.addEventListener('visibilitychange', function() {
    if (!document.hidden) {
        refreshDashboardData();
    }
});
</script>
{% endblock %}
