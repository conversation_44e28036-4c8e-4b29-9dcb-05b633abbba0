<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}牛排餐厅管理系统{% endblock %}</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            --dark-gradient: linear-gradient(135deg, #434343 0%, #000000 100%);
            --shadow-soft: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            --shadow-hover: 0 1rem 2rem rgba(0, 0, 0, 0.2);
            --border-radius: 1rem;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        .sidebar {
            min-height: 100vh;
            background: var(--primary-gradient);
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow-soft);
            position: relative;
            overflow: hidden;
        }

        .sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
        }

        .sidebar .nav-link {
            color: rgba(255,255,255,0.9);
            padding: 1rem 1.5rem;
            margin: 0.5rem 1rem;
            border-radius: var(--border-radius);
            transition: var(--transition);
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.1);
        }

        .sidebar .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: var(--transition);
        }

        .sidebar .nav-link:hover::before {
            left: 100%;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background: rgba(255,255,255,0.2);
            transform: translateX(5px);
            box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.2);
        }

        .sidebar .nav-link i {
            width: 20px;
            text-align: center;
            margin-right: 0.5rem;
        }

        .main-content {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            position: relative;
        }

        .card {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-soft);
            transition: var(--transition);
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            overflow: hidden;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-hover);
        }

        .card-header {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            border-bottom: 1px solid rgba(0,0,0,0.05);
            padding: 1.5rem;
        }

        .btn {
            border-radius: 0.75rem;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255,255,255,0.3);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: width 0.6s, height 0.6s;
        }

        .btn:active::before {
            width: 300px;
            height: 300px;
        }

        .btn-primary {
            background: var(--primary-gradient);
            border: none;
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 0.5rem 1rem rgba(102, 126, 234, 0.3);
        }

        .btn-success {
            background: var(--success-gradient);
            border: none;
            color: white;
        }

        .btn-warning {
            background: var(--warning-gradient);
            border: none;
            color: white;
        }

        .btn-danger {
            background: var(--danger-gradient);
            border: none;
            color: white;
        }

        .btn-info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
        }

        .alert {
            border-radius: var(--border-radius);
            border: none;
            box-shadow: var(--shadow-soft);
            backdrop-filter: blur(10px);
        }

        .alert-success {
            background: linear-gradient(135deg, rgba(67, 233, 123, 0.1) 0%, rgba(56, 249, 215, 0.1) 100%);
            color: #155724;
            border-left: 4px solid #43e97b;
        }

        .alert-danger {
            background: linear-gradient(135deg, rgba(250, 112, 154, 0.1) 0%, rgba(254, 225, 64, 0.1) 100%);
            color: #721c24;
            border-left: 4px solid #fa709a;
        }

        .alert-warning {
            background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 235, 59, 0.1) 100%);
            color: #856404;
            border-left: 4px solid #ffc107;
        }

        .alert-info {
            background: linear-gradient(135deg, rgba(74, 172, 254, 0.1) 0%, rgba(0, 242, 254, 0.1) 100%);
            color: #0c5460;
            border-left: 4px solid #4facfe;
        }

        .table {
            background: rgba(255,255,255,0.9);
            backdrop-filter: blur(10px);
            border-radius: var(--border-radius);
            overflow: hidden;
        }

        .table th {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            border: none;
            font-weight: 600;
            color: #495057;
            padding: 1rem;
        }

        .table td {
            padding: 1rem;
            border-color: rgba(0,0,0,0.05);
            vertical-align: middle;
        }

        .table-hover tbody tr:hover {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
        }

        .badge {
            padding: 0.5rem 0.75rem;
            border-radius: 0.5rem;
            font-weight: 600;
            font-size: 0.75rem;
        }

        .form-control, .form-select {
            border-radius: 0.75rem;
            border: 2px solid rgba(0,0,0,0.1);
            padding: 0.75rem 1rem;
            transition: var(--transition);
            background: rgba(255,255,255,0.9);
            backdrop-filter: blur(10px);
        }

        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            background: white;
        }

        .modal-content {
            border-radius: var(--border-radius);
            border: none;
            box-shadow: var(--shadow-hover);
            backdrop-filter: blur(20px);
            background: rgba(255,255,255,0.95);
        }

        .modal-header {
            border-bottom: 1px solid rgba(0,0,0,0.05);
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
        }

        .navbar-brand {
            font-weight: 700;
            color: #667eea !important;
            font-size: 1.5rem;
        }

        .status-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            border-radius: 0.5rem;
        }

        /* 动画效果 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .card {
            animation: fadeInUp 0.6s ease-out;
        }

        .sidebar .nav-link {
            animation: slideInLeft 0.6s ease-out;
        }

        /* 响应式设计增强 */
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                top: 0;
                left: -100%;
                width: 280px;
                z-index: 1050;
                transition: var(--transition);
            }

            .sidebar.show {
                left: 0;
            }

            .main-content {
                margin-left: 0;
            }
        }

        /* 加载动画 */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(0,0,0,0.1);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: var(--primary-gradient);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--secondary-gradient);
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            {% if current_user.is_authenticated %}
            <!-- 移动端菜单按钮 -->
            <button class="btn btn-primary d-md-none position-fixed" style="top: 1rem; left: 1rem; z-index: 1060;" onclick="toggleSidebar()">
                <i class="fas fa-bars"></i>
            </button>

            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse" id="sidebar">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <div class="d-flex align-items-center justify-content-center mb-3">
                            <div class="bg-white rounded-circle p-3 me-3">
                                <i class="fas fa-utensils text-primary fa-2x"></i>
                            </div>
                            <div class="text-start">
                                <h4 class="text-white mb-0">牛排餐厅</h4>
                                <small class="text-white-50">管理系统</small>
                            </div>
                        </div>
                        <div class="user-info bg-white bg-opacity-10 rounded-3 p-3">
                            <div class="d-flex align-items-center">
                                <div class="bg-white rounded-circle p-2 me-3">
                                    <i class="fas fa-user text-primary"></i>
                                </div>
                                <div class="text-start">
                                    <div class="text-white fw-bold">{{ current_user.name }}</div>
                                    <small class="text-white-50">{{ current_user.role }}</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('main.dashboard') }}">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                仪表板
                            </a>
                        </li>

                        {% if current_user.role in ['admin', 'manager', 'waiter', 'employee'] %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('pos.index') }}">
                                <i class="fas fa-cash-register me-2"></i>
                                POS收银
                            </a>
                        </li>
                        {% endif %}

                        {% if current_user.role in ['admin', 'manager'] %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('inventory.index') }}">
                                <i class="fas fa-boxes me-2"></i>
                                库存管理
                            </a>
                        </li>
                        {% endif %}

                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('reservation.manage') }}">
                                <i class="fas fa-calendar-alt me-2"></i>
                                预订管理
                            </a>
                        </li>

                        {% if current_user.role in ['admin', 'manager'] %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('staff.index') }}">
                                <i class="fas fa-users me-2"></i>
                                员工管理
                            </a>
                        </li>
                        {% endif %}

                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('customer.index') }}">
                                <i class="fas fa-user-friends me-2"></i>
                                客户管理
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('staff.my_schedule') }}">
                                <i class="fas fa-clock me-2"></i>
                                我的排班
                            </a>
                        </li>

                        <li class="nav-item mt-3">
                            <a class="nav-link text-warning" href="{{ url_for('auth.logout') }}">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                退出登录
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
            {% else %}
            <!-- 未登录时的全屏布局 -->
            <main class="col-12">
            {% endif %}

                {% if current_user.is_authenticated %}
                <!-- 顶部导航栏 -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">{% block page_title %}仪表板{% endblock %}</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="location.reload()">
                                <i class="fas fa-sync-alt"></i> 刷新
                            </button>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user"></i> {{ current_user.name }}
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{{ url_for('staff.my_schedule') }}">我的排班</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}">退出登录</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                {% endif %}

                <!-- 消息提示 -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <!-- 页面内容 -->
                {% block content %}{% endblock %}

            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    {% block extra_js %}{% endblock %}

    <script>
        // 页面加载完成后的初始化
        $(document).ready(function() {
            // 设置当前页面的导航链接为活动状态
            var currentPath = window.location.pathname;
            $('.sidebar .nav-link').each(function() {
                if ($(this).attr('href') === currentPath) {
                    $(this).addClass('active');
                }
            });

            // 添加页面加载动画
            $('body').addClass('loaded');

            // 初始化工具提示
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // 表格行点击效果
            $('.table-hover tbody tr').on('click', function() {
                $(this).addClass('table-active').siblings().removeClass('table-active');
            });

            // 卡片悬停效果增强
            $('.card').hover(
                function() {
                    $(this).addClass('shadow-lg');
                },
                function() {
                    $(this).removeClass('shadow-lg');
                }
            );
        });

        // 移动端侧边栏切换
        function toggleSidebar() {
            $('#sidebar').toggleClass('show');
            if ($('#sidebar').hasClass('show')) {
                $('body').append('<div class="sidebar-overlay" onclick="toggleSidebar()"></div>');
                $('.sidebar-overlay').css({
                    'position': 'fixed',
                    'top': 0,
                    'left': 0,
                    'width': '100%',
                    'height': '100%',
                    'background': 'rgba(0,0,0,0.5)',
                    'z-index': 1040
                });
            } else {
                $('.sidebar-overlay').remove();
            }
        }

        // 自动隐藏提示消息（带动画）
        setTimeout(function() {
            $('.alert').each(function() {
                $(this).fadeOut(500, function() {
                    $(this).remove();
                });
            });
        }, 5000);

        // 表单验证增强
        $('form').on('submit', function() {
            var submitBtn = $(this).find('button[type="submit"]');
            var originalText = submitBtn.html();
            submitBtn.html('<span class="loading"></span> 处理中...').prop('disabled', true);

            // 如果表单验证失败，恢复按钮状态
            setTimeout(function() {
                if (!submitBtn.closest('form')[0].checkValidity()) {
                    submitBtn.html(originalText).prop('disabled', false);
                }
            }, 100);
        });

        // 数字动画效果
        function animateNumber(element, start, end, duration) {
            var range = end - start;
            var current = start;
            var increment = end > start ? 1 : -1;
            var stepTime = Math.abs(Math.floor(duration / range));
            var timer = setInterval(function() {
                current += increment;
                $(element).text(current);
                if (current == end) {
                    clearInterval(timer);
                }
            }, stepTime);
        }

        // 页面切换动画
        function navigateWithAnimation(url) {
            $('body').fadeOut(300, function() {
                window.location.href = url;
            });
        }

        // 搜索框增强
        $('input[type="search"], input[placeholder*="搜索"]').on('input', function() {
            var searchTerm = $(this).val().toLowerCase();
            var targetTable = $(this).closest('.card').find('table tbody tr');

            if (targetTable.length > 0) {
                targetTable.each(function() {
                    var rowText = $(this).text().toLowerCase();
                    if (rowText.indexOf(searchTerm) === -1) {
                        $(this).fadeOut(200);
                    } else {
                        $(this).fadeIn(200);
                    }
                });
            }
        });

        // 状态徽章点击效果
        $('.badge').on('click', function() {
            $(this).addClass('pulse');
            setTimeout(() => {
                $(this).removeClass('pulse');
            }, 600);
        });

        // 添加脉冲动画CSS
        $('<style>')
            .prop('type', 'text/css')
            .html(`
                .pulse {
                    animation: pulse 0.6s ease-in-out;
                }
                @keyframes pulse {
                    0% { transform: scale(1); }
                    50% { transform: scale(1.1); }
                    100% { transform: scale(1); }
                }
                .loaded {
                    opacity: 1;
                    transition: opacity 0.3s ease-in-out;
                }
                body {
                    opacity: 0;
                }
                .sidebar-overlay {
                    animation: fadeIn 0.3s ease-in-out;
                }
                @keyframes fadeIn {
                    from { opacity: 0; }
                    to { opacity: 1; }
                }
            `)
            .appendTo('head');

        // 实时时间显示
        function updateTime() {
            var now = new Date();
            var timeString = now.toLocaleTimeString('zh-CN');
            $('.current-time').text(timeString);
        }

        // 每秒更新时间
        setInterval(updateTime, 1000);
        updateTime();

        // 键盘快捷键
        $(document).keydown(function(e) {
            // Ctrl + / 打开搜索
            if (e.ctrlKey && e.which === 191) {
                e.preventDefault();
                $('input[type="search"], input[placeholder*="搜索"]').first().focus();
            }

            // ESC 关闭模态框
            if (e.which === 27) {
                $('.modal.show').modal('hide');
                if ($('#sidebar').hasClass('show')) {
                    toggleSidebar();
                }
            }
        });

        // 页面性能监控
        window.addEventListener('load', function() {
            var loadTime = window.performance.timing.domContentLoadedEventEnd - window.performance.timing.navigationStart;
            console.log('页面加载时间: ' + loadTime + 'ms');
        });
    </script>
</body>
</html>
