<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}牛排餐厅管理系统{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 0.75rem 1rem;
            margin: 0.25rem 0;
            border-radius: 0.5rem;
            transition: all 0.3s;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.1);
        }
        .main-content {
            background-color: #f8f9fa;
            min-height: 100vh;
        }
        .card {
            border: none;
            border-radius: 1rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        .navbar-brand {
            font-weight: bold;
            color: #667eea !important;
        }
        .alert {
            border-radius: 0.75rem;
        }
        .table th {
            background-color: #f8f9fa;
            border-top: none;
        }
        .status-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            {% if current_user.is_authenticated %}
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h4 class="text-white">
                            <i class="fas fa-utensils"></i>
                            牛排餐厅
                        </h4>
                        <small class="text-white-50">{{ current_user.name }} ({{ current_user.role }})</small>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('main.dashboard') }}">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                仪表板
                            </a>
                        </li>
                        
                        {% if current_user.role in ['admin', 'manager', 'waiter', 'employee'] %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('pos.index') }}">
                                <i class="fas fa-cash-register me-2"></i>
                                POS收银
                            </a>
                        </li>
                        {% endif %}
                        
                        {% if current_user.role in ['admin', 'manager'] %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('inventory.index') }}">
                                <i class="fas fa-boxes me-2"></i>
                                库存管理
                            </a>
                        </li>
                        {% endif %}
                        
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('reservation.manage') }}">
                                <i class="fas fa-calendar-alt me-2"></i>
                                预订管理
                            </a>
                        </li>
                        
                        {% if current_user.role in ['admin', 'manager'] %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('staff.index') }}">
                                <i class="fas fa-users me-2"></i>
                                员工管理
                            </a>
                        </li>
                        {% endif %}
                        
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('customer.index') }}">
                                <i class="fas fa-user-friends me-2"></i>
                                客户管理
                            </a>
                        </li>
                        
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('staff.my_schedule') }}">
                                <i class="fas fa-clock me-2"></i>
                                我的排班
                            </a>
                        </li>
                        
                        <li class="nav-item mt-3">
                            <a class="nav-link text-warning" href="{{ url_for('auth.logout') }}">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                退出登录
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
            
            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
            {% else %}
            <!-- 未登录时的全屏布局 -->
            <main class="col-12">
            {% endif %}
                
                {% if current_user.is_authenticated %}
                <!-- 顶部导航栏 -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">{% block page_title %}仪表板{% endblock %}</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="location.reload()">
                                <i class="fas fa-sync-alt"></i> 刷新
                            </button>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user"></i> {{ current_user.name }}
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{{ url_for('staff.my_schedule') }}">我的排班</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}">退出登录</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                {% endif %}
                
                <!-- 消息提示 -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
                
                <!-- 页面内容 -->
                {% block content %}{% endblock %}
                
            </main>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    {% block extra_js %}{% endblock %}
    
    <script>
        // 自动隐藏提示消息
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 5000);
        
        // 设置当前页面的导航链接为活动状态
        $(document).ready(function() {
            var currentPath = window.location.pathname;
            $('.sidebar .nav-link').each(function() {
                if ($(this).attr('href') === currentPath) {
                    $(this).addClass('active');
                }
            });
        });
    </script>
</body>
</html>
