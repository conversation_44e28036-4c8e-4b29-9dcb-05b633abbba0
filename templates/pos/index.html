{% extends "base.html" %}

{% block title %}POS收银系统 - 牛排餐厅管理系统{% endblock %}
{% block page_title %}POS收银系统{% endblock %}

{% block content %}
<div class="row">
    <!-- 桌位状态 -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-chair"></i> 桌位状态</h5>
            </div>
            <div class="card-body" style="max-height: 600px; overflow-y: auto;">
                <h6 class="text-success">可用桌位</h6>
                <div class="row mb-3">
                    {% for table in available_tables %}
                    <div class="col-6 mb-2">
                        <button class="btn btn-outline-success btn-sm w-100" 
                                onclick="newOrder({{ table.id }})">
                            {{ table.number }}<br>
                            <small>{{ table.capacity }}人座</small>
                        </button>
                    </div>
                    {% endfor %}
                </div>
                
                <h6 class="text-warning">使用中桌位</h6>
                <div class="row">
                    {% for table in occupied_tables %}
                    <div class="col-6 mb-2">
                        <button class="btn btn-warning btn-sm w-100" 
                                onclick="viewTableOrders({{ table.id }})">
                            {{ table.number }}<br>
                            <small>使用中</small>
                        </button>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- 菜品选择 -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-utensils"></i> 菜品选择</h5>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-primary active" onclick="showCategory('all')">
                        全部
                    </button>
                    {% for category in categories %}
                    <button type="button" class="btn btn-outline-primary" onclick="showCategory({{ category.id }})">
                        {{ category.name }}
                    </button>
                    {% endfor %}
                </div>
            </div>
            <div class="card-body" style="max-height: 600px; overflow-y: auto;">
                <div class="row" id="menuItems">
                    {% for item in menu_items %}
                    <div class="col-lg-4 col-md-6 mb-3 menu-item" data-category="{{ item.category_id }}">
                        <div class="card h-100">
                            <div class="card-body p-3">
                                <h6 class="card-title">{{ item.name }}</h6>
                                <p class="card-text small text-muted">{{ item.description }}</p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="h6 text-primary mb-0">¥{{ item.price }}</span>
                                    <button class="btn btn-primary btn-sm" 
                                            onclick="selectMenuItem({{ item.id }}, '{{ item.name }}', {{ item.price }})">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 新订单模态框 -->
<div class="modal fade" id="newOrderModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">创建新订单</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>确认为 <span id="selectedTable"></span> 创建新订单？</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="confirmNewOrder()">确认</button>
            </div>
        </div>
    </div>
</div>

<!-- 菜品选择模态框 -->
<div class="modal fade" id="menuItemModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">添加菜品</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="menuItemForm">
                    <div class="mb-3">
                        <label class="form-label">菜品名称</label>
                        <input type="text" class="form-control" id="itemName" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">单价</label>
                        <input type="text" class="form-control" id="itemPrice" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">数量</label>
                        <input type="number" class="form-control" id="itemQuantity" value="1" min="1">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">特殊要求</label>
                        <textarea class="form-control" id="itemInstructions" rows="2"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="addMenuItem()">添加</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let selectedTableId = null;
let selectedMenuItemId = null;
let currentOrderId = null;

function newOrder(tableId) {
    selectedTableId = tableId;
    $('#selectedTable').text('桌位 ' + getTableNumber(tableId));
    $('#newOrderModal').modal('show');
}

function confirmNewOrder() {
    if (selectedTableId) {
        window.location.href = '/pos/new-order/' + selectedTableId;
    }
}

function selectMenuItem(itemId, itemName, itemPrice) {
    if (!currentOrderId) {
        alert('请先选择桌位创建订单');
        return;
    }
    
    selectedMenuItemId = itemId;
    $('#itemName').val(itemName);
    $('#itemPrice').val('¥' + itemPrice);
    $('#itemQuantity').val(1);
    $('#itemInstructions').val('');
    $('#menuItemModal').modal('show');
}

function addMenuItem() {
    if (!currentOrderId || !selectedMenuItemId) {
        alert('请先选择订单和菜品');
        return;
    }
    
    const quantity = $('#itemQuantity').val();
    const instructions = $('#itemInstructions').val();
    
    $.ajax({
        url: '/pos/add-item/' + currentOrderId,
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            menu_item_id: selectedMenuItemId,
            quantity: parseInt(quantity),
            special_instructions: instructions
        }),
        success: function(response) {
            if (response.success) {
                alert(response.message);
                $('#menuItemModal').modal('hide');
                // 刷新订单详情页面
                if (window.location.pathname.includes('/order/')) {
                    location.reload();
                }
            } else {
                alert(response.message);
            }
        },
        error: function() {
            alert('添加失败，请重试');
        }
    });
}

function showCategory(categoryId) {
    $('.btn-outline-primary').removeClass('active');
    event.target.classList.add('active');
    
    if (categoryId === 'all') {
        $('.menu-item').show();
    } else {
        $('.menu-item').hide();
        $('.menu-item[data-category="' + categoryId + '"]').show();
    }
}

function getTableNumber(tableId) {
    // 这里应该从页面数据中获取桌位号
    // 简化处理，直接返回桌位ID
    return 'T' + String(tableId).padStart(2, '0');
}

function viewTableOrders(tableId) {
    // 查看桌位的当前订单
    window.location.href = '/pos/table-orders/' + tableId;
}

// 从URL获取当前订单ID（如果在订单详情页面）
$(document).ready(function() {
    const path = window.location.pathname;
    const orderMatch = path.match(/\/pos\/order\/(\d+)/);
    if (orderMatch) {
        currentOrderId = orderMatch[1];
    }
});
</script>
{% endblock %}
