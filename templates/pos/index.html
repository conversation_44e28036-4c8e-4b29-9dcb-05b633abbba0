{% extends "base.html" %}

{% block title %}POS收银系统 - 牛排餐厅管理系统{% endblock %}
{% block page_title %}POS收银系统{% endblock %}

{% block extra_css %}
<style>
    .pos-container {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: calc(100vh - 120px);
        border-radius: 15px;
        padding: 20px;
    }

    .table-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        margin-bottom: 15px;
        overflow: hidden;
    }

    .table-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }

    .table-btn {
        border: none;
        border-radius: 12px;
        padding: 15px;
        margin: 5px;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        font-weight: 600;
        min-height: 80px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }

    .table-btn:before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        transition: left 0.5s;
    }

    .table-btn:hover:before {
        left: 100%;
    }

    .table-available {
        background: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%);
        color: white;
        border: 2px solid rgba(255,255,255,0.3);
    }

    .table-available:hover {
        background: linear-gradient(135deg, #6dd5a0 0%, #7bc3e4 100%);
        transform: translateY(-3px);
        box-shadow: 0 6px 20px rgba(132, 250, 176, 0.4);
    }

    .table-occupied {
        background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        color: #8b4513;
        border: 2px solid rgba(255,255,255,0.3);
    }

    .table-occupied:hover {
        background: linear-gradient(135deg, #ffd2a2 0%, #fb969f 100%);
        transform: translateY(-3px);
        box-shadow: 0 6px 20px rgba(252, 182, 159, 0.4);
    }

    .table-reserved {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        color: #6b46c1;
        border: 2px solid rgba(255,255,255,0.3);
    }

    .table-cleaning {
        background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%);
        color: #7c3aed;
        border: 2px solid rgba(255,255,255,0.3);
    }

    .table-number {
        font-size: 1.2rem;
        font-weight: bold;
        margin-bottom: 5px;
    }

    .table-capacity {
        font-size: 0.85rem;
        opacity: 0.9;
    }

    .table-status-badge {
        position: absolute;
        top: 5px;
        right: 5px;
        font-size: 0.7rem;
        padding: 2px 6px;
        border-radius: 10px;
        background: rgba(255,255,255,0.9);
        color: #333;
    }

    .menu-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        background: white;
    }

    .category-btn {
        border: none;
        border-radius: 25px;
        padding: 10px 20px;
        margin: 5px;
        transition: all 0.3s ease;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        font-weight: 500;
    }

    .category-btn:hover, .category-btn.active {
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    }

    .menu-item-card {
        border: 1px solid rgba(0,0,0,0.1);
        border-radius: 12px;
        padding: 15px;
        margin: 10px 0;
        transition: all 0.3s ease;
        cursor: pointer;
        background: white;
    }

    .menu-item-card:hover {
        border-color: #667eea;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
        transform: translateY(-2px);
    }

    .menu-item-name {
        font-weight: 600;
        color: #333;
        margin-bottom: 5px;
    }

    .menu-item-price {
        color: #e74c3c;
        font-weight: bold;
        font-size: 1.1rem;
    }

    .menu-item-description {
        color: #666;
        font-size: 0.9rem;
        margin-bottom: 10px;
    }

    .status-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 8px;
        animation: pulse 2s infinite;
    }

    .status-available { background: #27ae60; }
    .status-occupied { background: #f39c12; }
    .status-reserved { background: #9b59b6; }
    .status-cleaning { background: #e67e22; }

    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
    }

    .table-actions {
        position: absolute;
        bottom: 5px;
        right: 5px;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .table-btn:hover .table-actions {
        opacity: 1;
    }

    .action-btn {
        width: 25px;
        height: 25px;
        border-radius: 50%;
        border: none;
        margin: 0 2px;
        font-size: 0.7rem;
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }
</style>
{% endblock %}

{% block content %}
<div class="pos-container">
    <div class="row">
        <!-- 桌位状态管理 -->
        <div class="col-md-4">
            <div class="table-card card">
                <div class="card-header bg-gradient text-white" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                    <h5 class="mb-0">
                        <i class="fas fa-chair me-2"></i>桌位管理
                        <span class="badge bg-light text-dark ms-2">{{ available_tables|length + occupied_tables|length }}桌</span>
                    </h5>
                </div>
                <div class="card-body p-3" style="max-height: 600px; overflow-y: auto;">
                    <!-- 状态图例 -->
                    <div class="mb-3 p-2 bg-light rounded">
                        <small class="text-muted d-block mb-2">状态图例：</small>
                        <div class="d-flex flex-wrap">
                            <span class="me-3 mb-1"><span class="status-indicator status-available"></span>可用</span>
                            <span class="me-3 mb-1"><span class="status-indicator status-occupied"></span>使用中</span>
                            <span class="me-3 mb-1"><span class="status-indicator status-reserved"></span>已预订</span>
                            <span class="me-3 mb-1"><span class="status-indicator status-cleaning"></span>清洁中</span>
                        </div>
                    </div>

                    <!-- 可用桌位 -->
                    {% if available_tables %}
                    <h6 class="text-success mb-3">
                        <span class="status-indicator status-available"></span>
                        可用桌位 ({{ available_tables|length }})
                    </h6>
                    <div class="row mb-4">
                        {% for table in available_tables %}
                        <div class="col-6 mb-2">
                            <button class="table-btn table-available w-100 position-relative"
                                    onclick="newOrder({{ table.id }})">
                                <div class="table-number">{{ table.number }}</div>
                                <div class="table-capacity">{{ table.capacity }}人座</div>
                                <div class="table-actions">
                                    <button class="action-btn btn btn-sm btn-light"
                                            onclick="event.stopPropagation(); changeTableStatus({{ table.id }}, 'cleaning')"
                                            title="设为清洁中">
                                        <i class="fas fa-broom"></i>
                                    </button>
                                </div>
                            </button>
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}

                    <!-- 使用中桌位 -->
                    {% if occupied_tables %}
                    <h6 class="text-warning mb-3">
                        <span class="status-indicator status-occupied"></span>
                        使用中桌位 ({{ occupied_tables|length }})
                    </h6>
                    <div class="row mb-4">
                        {% for table in occupied_tables %}
                        <div class="col-6 mb-2">
                            <button class="table-btn table-occupied w-100 position-relative"
                                    onclick="viewTableOrders({{ table.id }})">
                                <div class="table-number">{{ table.number }}</div>
                                <div class="table-capacity">使用中</div>
                                <div class="table-actions">
                                    <button class="action-btn btn btn-sm btn-success"
                                            onclick="event.stopPropagation(); changeTableStatus({{ table.id }}, 'available')"
                                            title="设为可用">
                                        <i class="fas fa-check"></i>
                                    </button>
                                </div>
                            </button>
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}

                    <!-- 其他状态桌位 -->
                    <div id="otherTables"></div>
                </div>
            </div>
        </div>

        <!-- 菜品选择 -->
        <div class="col-md-8">
            <div class="menu-card card">
                <div class="card-header bg-gradient text-white" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                    <h5 class="mb-0">
                        <i class="fas fa-utensils me-2"></i>菜品选择
                        <span class="badge bg-light text-dark ms-2">{{ menu_items|length }}道菜品</span>
                    </h5>
                </div>
                <div class="card-body p-3">
                    <!-- 分类按钮 -->
                    <div class="mb-4 text-center">
                        <button class="category-btn active" onclick="showCategory('all')" data-category="all">
                            <i class="fas fa-th-large me-1"></i>全部菜品
                        </button>
                        {% for category in categories %}
                        <button class="category-btn" onclick="showCategory({{ category.id }})" data-category="{{ category.id }}">
                            {% if category.name == '精选牛排' %}
                                <i class="fas fa-cut me-1"></i>
                            {% elif category.name == '顶级牛排' %}
                                <i class="fas fa-crown me-1"></i>
                            {% elif category.name == '海鲜类' %}
                                <i class="fas fa-fish me-1"></i>
                            {% elif category.name == '开胃菜' %}
                                <i class="fas fa-seedling me-1"></i>
                            {% elif category.name == '汤品' %}
                                <i class="fas fa-bowl-hot me-1"></i>
                            {% elif category.name == '沙拉' %}
                                <i class="fas fa-leaf me-1"></i>
                            {% elif category.name == '配菜' %}
                                <i class="fas fa-carrot me-1"></i>
                            {% elif category.name == '甜品' %}
                                <i class="fas fa-ice-cream me-1"></i>
                            {% elif category.name == '红酒' %}
                                <i class="fas fa-wine-glass me-1"></i>
                            {% elif category.name == '咖啡茶饮' %}
                                <i class="fas fa-coffee me-1"></i>
                            {% else %}
                                <i class="fas fa-utensils me-1"></i>
                            {% endif %}
                            {{ category.name }}
                        </button>
                        {% endfor %}
                    </div>

                    <!-- 搜索框 -->
                    <div class="mb-3">
                        <div class="input-group">
                            <span class="input-group-text bg-light border-0">
                                <i class="fas fa-search text-muted"></i>
                            </span>
                            <input type="text" class="form-control border-0 bg-light"
                                   placeholder="搜索菜品..." id="menuSearch"
                                   onkeyup="searchMenuItems()">
                        </div>
                    </div>

                    <!-- 菜品列表 -->
                    <div style="max-height: 500px; overflow-y: auto;" id="menuContainer">
                        <div class="row" id="menuItemsContainer">
                            {% for item in menu_items %}
                            <div class="col-md-6 col-lg-4 mb-3 menu-item"
                                 data-category="{{ item.category_id }}"
                                 data-name="{{ item.name|lower }}"
                                 data-description="{{ item.description|lower }}">
                                <div class="menu-item-card" onclick="selectMenuItem({{ item.id }}, '{{ item.name }}', {{ item.price }})">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <div class="menu-item-name">{{ item.name }}</div>
                                        <div class="menu-item-price">¥{{ "%.0f"|format(item.price) }}</div>
                                    </div>
                                    <div class="menu-item-description">
                                        {{ item.description[:60] }}{% if item.description|length > 60 %}...{% endif %}
                                    </div>
                                    <div class="d-flex justify-content-between align-items-center mt-2">
                                        <small class="text-muted">
                                            <i class="fas fa-clock me-1"></i>{{ item.preparation_time }}分钟
                                        </small>
                                        <button class="btn btn-sm btn-outline-primary" onclick="event.stopPropagation(); quickAdd({{ item.id }})">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>

                        <!-- 无结果提示 -->
                        <div id="noResults" class="text-center py-5" style="display: none;">
                            <i class="fas fa-search fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">未找到匹配的菜品</h5>
                            <p class="text-muted">请尝试其他关键词或选择不同的分类</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 新订单模态框 -->
<div class="modal fade" id="newOrderModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">创建新订单</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>确认为 <span id="selectedTable"></span> 创建新订单？</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="confirmNewOrder()">确认</button>
            </div>
        </div>
    </div>
</div>

<!-- 菜品选择模态框 -->
<div class="modal fade" id="menuItemModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">添加菜品</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="menuItemForm">
                    <div class="mb-3">
                        <label class="form-label">菜品名称</label>
                        <input type="text" class="form-control" id="itemName" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">单价</label>
                        <input type="text" class="form-control" id="itemPrice" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">数量</label>
                        <input type="number" class="form-control" id="itemQuantity" value="1" min="1">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">特殊要求</label>
                        <textarea class="form-control" id="itemInstructions" rows="2"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="addMenuItem()">添加</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let selectedTableId = null;
let selectedMenuItemId = null;
let currentOrderId = null;

function newOrder(tableId) {
    selectedTableId = tableId;
    $('#selectedTable').text('桌位 ' + getTableNumber(tableId));
    $('#newOrderModal').modal('show');
}

function confirmNewOrder() {
    if (selectedTableId) {
        window.location.href = '/pos/new-order/' + selectedTableId;
    }
}

function selectMenuItem(itemId, itemName, itemPrice) {
    if (!currentOrderId) {
        alert('请先选择桌位创建订单');
        return;
    }

    selectedMenuItemId = itemId;
    $('#itemName').val(itemName);
    $('#itemPrice').val('¥' + itemPrice);
    $('#itemQuantity').val(1);
    $('#itemInstructions').val('');
    $('#menuItemModal').modal('show');
}

function addMenuItem() {
    if (!currentOrderId || !selectedMenuItemId) {
        alert('请先选择订单和菜品');
        return;
    }

    const quantity = $('#itemQuantity').val();
    const instructions = $('#itemInstructions').val();

    $.ajax({
        url: '/pos/add-item/' + currentOrderId,
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            menu_item_id: selectedMenuItemId,
            quantity: parseInt(quantity),
            special_instructions: instructions
        }),
        success: function(response) {
            if (response.success) {
                alert(response.message);
                $('#menuItemModal').modal('hide');
                // 刷新订单详情页面
                if (window.location.pathname.includes('/order/')) {
                    location.reload();
                }
            } else {
                alert(response.message);
            }
        },
        error: function() {
            alert('添加失败，请重试');
        }
    });
}

function showCategory(categoryId) {
    $('.btn-outline-primary').removeClass('active');
    event.target.classList.add('active');

    if (categoryId === 'all') {
        $('.menu-item').show();
    } else {
        $('.menu-item').hide();
        $('.menu-item[data-category="' + categoryId + '"]').show();
    }
}

function getTableNumber(tableId) {
    // 这里应该从页面数据中获取桌位号
    // 简化处理，直接返回桌位ID
    return 'T' + String(tableId).padStart(2, '0');
}

function viewTableOrders(tableId) {
    // 查看桌位的当前订单
    window.location.href = '/pos/table-orders/' + tableId;
}

// 更改桌位状态
function changeTableStatus(tableId, newStatus) {
    const statusText = {
        'available': '可用',
        'occupied': '使用中',
        'reserved': '已预订',
        'cleaning': '清洁中'
    };

    if (!confirm(`确认将桌位状态更改为"${statusText[newStatus]}"吗？`)) {
        return;
    }

    $.ajax({
        url: '/pos/table-status/' + tableId,
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            status: newStatus
        }),
        success: function(response) {
            if (response.success) {
                showNotification(response.message, 'success');
                // 刷新页面以更新桌位状态
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                showNotification(response.message, 'error');
            }
        },
        error: function() {
            showNotification('更新失败，请重试', 'error');
        }
    });
}

// 搜索菜品
function searchMenuItems() {
    const searchTerm = $('#menuSearch').val().toLowerCase();
    const menuItems = $('.menu-item');
    let visibleCount = 0;

    menuItems.each(function() {
        const name = $(this).data('name') || '';
        const description = $(this).data('description') || '';

        if (name.includes(searchTerm) || description.includes(searchTerm)) {
            $(this).show();
            visibleCount++;
        } else {
            $(this).hide();
        }
    });

    // 显示/隐藏无结果提示
    if (visibleCount === 0 && searchTerm !== '') {
        $('#noResults').show();
    } else {
        $('#noResults').hide();
    }
}

// 快速添加菜品
function quickAdd(itemId) {
    if (!currentOrderId) {
        showNotification('请先选择桌位创建订单', 'warning');
        return;
    }

    $.ajax({
        url: '/pos/add-item/' + currentOrderId,
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            menu_item_id: itemId,
            quantity: 1,
            special_instructions: ''
        }),
        success: function(response) {
            if (response.success) {
                showNotification(response.message, 'success');
                // 如果在订单详情页面，刷新页面
                if (window.location.pathname.includes('/order/')) {
                    setTimeout(() => {
                        location.reload();
                    }, 1000);
                }
            } else {
                showNotification(response.message, 'error');
            }
        },
        error: function() {
            showNotification('添加失败，请重试', 'error');
        }
    });
}

// 显示通知
function showNotification(message, type = 'info') {
    const alertClass = {
        'success': 'alert-success',
        'error': 'alert-danger',
        'warning': 'alert-warning',
        'info': 'alert-info'
    };

    const notification = $(`
        <div class="alert ${alertClass[type]} alert-dismissible fade show position-fixed"
             style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `);

    $('body').append(notification);

    // 自动消失
    setTimeout(() => {
        notification.alert('close');
    }, 3000);
}

// 增强分类按钮功能
function showCategory(categoryId) {
    // 更新按钮状态
    $('.category-btn').removeClass('active');
    $(`[data-category="${categoryId}"]`).addClass('active');

    // 清空搜索框
    $('#menuSearch').val('');

    // 显示/隐藏菜品
    if (categoryId === 'all') {
        $('.menu-item').show();
    } else {
        $('.menu-item').hide();
        $('.menu-item[data-category="' + categoryId + '"]').show();
    }

    // 隐藏无结果提示
    $('#noResults').hide();
}

// 从URL获取当前订单ID（如果在订单详情页面）
$(document).ready(function() {
    const path = window.location.pathname;
    const orderMatch = path.match(/\/pos\/order\/(\d+)/);
    if (orderMatch) {
        currentOrderId = orderMatch[1];
    }

    // 初始化工具提示
    $('[title]').tooltip();

    // 添加键盘快捷键
    $(document).keydown(function(e) {
        // Ctrl + F 聚焦搜索框
        if (e.ctrlKey && e.key === 'f') {
            e.preventDefault();
            $('#menuSearch').focus();
        }

        // ESC 清空搜索
        if (e.key === 'Escape') {
            $('#menuSearch').val('');
            searchMenuItems();
        }
    });
});
</script>
{% endblock %}
