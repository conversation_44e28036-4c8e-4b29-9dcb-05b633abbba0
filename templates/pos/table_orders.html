{% extends "base.html" %}

{% block title %}桌位订单 - 牛排餐厅管理系统{% endblock %}
{% block page_title %}桌位 {{ table.number }} - 订单管理{% endblock %}

{% block extra_css %}
<style>
    .order-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        margin-bottom: 20px;
        overflow: hidden;
        transition: all 0.3s ease;
    }
    
    .order-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }
    
    .order-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
    }
    
    .order-status {
        display: inline-block;
        padding: 5px 15px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 600;
        text-transform: uppercase;
    }
    
    .status-pending {
        background: #ffeaa7;
        color: #d63031;
    }
    
    .status-preparing {
        background: #74b9ff;
        color: white;
    }
    
    .status-ready {
        background: #00b894;
        color: white;
    }
    
    .status-completed {
        background: #636e72;
        color: white;
    }
    
    .status-cancelled {
        background: #d63031;
        color: white;
    }
    
    .order-item {
        border-bottom: 1px solid #eee;
        padding: 15px 0;
        transition: background 0.3s ease;
    }
    
    .order-item:hover {
        background: #f8f9fa;
    }
    
    .order-item:last-child {
        border-bottom: none;
    }
    
    .item-name {
        font-weight: 600;
        color: #333;
        margin-bottom: 5px;
    }
    
    .item-price {
        color: #e74c3c;
        font-weight: bold;
    }
    
    .item-quantity {
        background: #667eea;
        color: white;
        border-radius: 50%;
        width: 25px;
        height: 25px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-size: 0.8rem;
        font-weight: bold;
    }
    
    .table-info {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .action-btn {
        border-radius: 25px;
        padding: 10px 25px;
        font-weight: 600;
        transition: all 0.3s ease;
        border: none;
        margin: 5px;
    }
    
    .action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    }
    
    .btn-complete {
        background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
        color: white;
    }
    
    .btn-cancel {
        background: linear-gradient(135deg, #d63031 0%, #e17055 100%);
        color: white;
    }
    
    .btn-available {
        background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
        color: white;
    }
    
    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #666;
    }
    
    .empty-state i {
        font-size: 4rem;
        margin-bottom: 20px;
        opacity: 0.5;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 桌位信息 -->
    <div class="table-info">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h3 class="mb-2">
                    <i class="fas fa-chair me-2"></i>
                    桌位 {{ table.number }}
                </h3>
                <p class="mb-0">
                    <i class="fas fa-users me-2"></i>
                    容量: {{ table.capacity }}人
                    <span class="ms-3">
                        <i class="fas fa-circle me-1 status-indicator status-{{ table.status }}"></i>
                        状态: 
                        {% if table.status == 'available' %}可用
                        {% elif table.status == 'occupied' %}使用中
                        {% elif table.status == 'reserved' %}已预订
                        {% elif table.status == 'cleaning' %}清洁中
                        {% endif %}
                    </span>
                </p>
            </div>
            <div class="col-md-6 text-end">
                {% if current_user.role in ['admin', 'manager'] %}
                <button class="action-btn btn-available" onclick="changeTableStatus('available')">
                    <i class="fas fa-check me-2"></i>设为可用
                </button>
                <button class="action-btn btn-cancel" onclick="changeTableStatus('cleaning')">
                    <i class="fas fa-broom me-2"></i>清洁中
                </button>
                {% endif %}
                <a href="{{ url_for('pos.index') }}" class="action-btn" style="background: #636e72; color: white;">
                    <i class="fas fa-arrow-left me-2"></i>返回POS
                </a>
            </div>
        </div>
    </div>
    
    <!-- 活跃订单 -->
    {% if orders %}
        <h4 class="mb-4">
            <i class="fas fa-receipt me-2"></i>
            当前订单 ({{ orders|length }})
        </h4>
        
        {% for order in orders %}
        <div class="order-card">
            <div class="order-header">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h5 class="mb-1">订单 #{{ order.order_number }}</h5>
                        <small>创建时间: {{ order.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
                    </div>
                    <div class="col-md-6 text-end">
                        <span class="order-status status-{{ order.status }}">
                            {% if order.status == 'pending' %}待处理
                            {% elif order.status == 'preparing' %}制作中
                            {% elif order.status == 'ready' %}已完成
                            {% elif order.status == 'completed' %}已结账
                            {% elif order.status == 'cancelled' %}已取消
                            {% endif %}
                        </span>
                        <div class="mt-2">
                            <strong class="h5">总计: ¥{{ "%.2f"|format(order.total_amount) }}</strong>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card-body p-4">
                <!-- 订单项目 -->
                {% for item in order.items %}
                <div class="order-item">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <div class="item-name">{{ item.menu_item.name }}</div>
                            {% if item.special_instructions %}
                            <small class="text-muted">
                                <i class="fas fa-comment me-1"></i>
                                {{ item.special_instructions }}
                            </small>
                            {% endif %}
                        </div>
                        <div class="col-md-2 text-center">
                            <span class="item-quantity">{{ item.quantity }}</span>
                        </div>
                        <div class="col-md-2 text-center">
                            <div class="item-price">¥{{ "%.2f"|format(item.price) }}</div>
                            <small class="text-muted">单价</small>
                        </div>
                        <div class="col-md-2 text-end">
                            <div class="item-price">¥{{ "%.2f"|format(item.price * item.quantity) }}</div>
                            <small class="text-muted">小计</small>
                        </div>
                    </div>
                </div>
                {% endfor %}
                
                <!-- 订单操作 -->
                <div class="mt-4 pt-3 border-top">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            {% if order.customer %}
                            <small class="text-muted">
                                <i class="fas fa-user me-1"></i>
                                客户: {{ order.customer.name }}
                            </small>
                            {% endif %}
                        </div>
                        <div class="col-md-6 text-end">
                            {% if current_user.role in ['admin', 'manager'] %}
                                {% if order.status in ['pending', 'preparing'] %}
                                <button class="action-btn btn-cancel btn-sm" onclick="cancelOrder({{ order.id }})">
                                    <i class="fas fa-times me-1"></i>取消订单
                                </button>
                                {% endif %}
                                {% if order.status == 'ready' %}
                                <button class="action-btn btn-complete btn-sm" onclick="completeOrder({{ order.id }})">
                                    <i class="fas fa-check me-1"></i>完成结账
                                </button>
                                {% endif %}
                            {% endif %}
                            <a href="{{ url_for('pos.order_detail', order_id=order.id) }}" class="action-btn btn-sm" style="background: #667eea; color: white;">
                                <i class="fas fa-eye me-1"></i>查看详情
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    {% else %}
        <!-- 空状态 -->
        <div class="empty-state">
            <i class="fas fa-receipt"></i>
            <h4>暂无活跃订单</h4>
            <p>该桌位当前没有进行中的订单</p>
            {% if table.status == 'available' %}
            <a href="{{ url_for('pos.new_order', table_id=table.id) }}" class="action-btn btn-complete">
                <i class="fas fa-plus me-2"></i>创建新订单
            </a>
            {% endif %}
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
function changeTableStatus(newStatus) {
    const statusText = {
        'available': '可用',
        'occupied': '使用中',
        'reserved': '已预订',
        'cleaning': '清洁中'
    };
    
    if (!confirm(`确认将桌位状态更改为"${statusText[newStatus]}"吗？`)) {
        return;
    }
    
    $.ajax({
        url: '/pos/table-status/{{ table.id }}',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            status: newStatus
        }),
        success: function(response) {
            if (response.success) {
                showNotification(response.message, 'success');
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                showNotification(response.message, 'error');
            }
        },
        error: function() {
            showNotification('更新失败，请重试', 'error');
        }
    });
}

function cancelOrder(orderId) {
    if (!confirm('确认取消此订单吗？此操作不可撤销。')) {
        return;
    }
    
    $.ajax({
        url: '/pos/cancel-order/' + orderId,
        method: 'POST',
        success: function(response) {
            if (response.success) {
                showNotification(response.message, 'success');
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                showNotification(response.message, 'error');
            }
        },
        error: function() {
            showNotification('取消失败，请重试', 'error');
        }
    });
}

function completeOrder(orderId) {
    if (!confirm('确认完成此订单的结账吗？')) {
        return;
    }
    
    // 跳转到结账页面
    window.location.href = '/pos/checkout/' + orderId;
}

function showNotification(message, type = 'info') {
    const alertClass = {
        'success': 'alert-success',
        'error': 'alert-danger',
        'warning': 'alert-warning',
        'info': 'alert-info'
    };
    
    const notification = $(`
        <div class="alert ${alertClass[type]} alert-dismissible fade show position-fixed" 
             style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `);
    
    $('body').append(notification);
    
    setTimeout(() => {
        notification.alert('close');
    }, 3000);
}
</script>
{% endblock %}
