{% extends "base.html" %}

{% block title %}订单详情 - 牛排餐厅管理系统{% endblock %}
{% block page_title %}订单 #{{ order.order_number }}{% endblock %}

{% block content %}
<div class="row">
    <!-- 订单信息 -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-receipt"></i> 订单详情</h5>
                <div>
                    {% if order.status == 'pending' %}
                        <span class="badge bg-warning">待处理</span>
                    {% elif order.status == 'preparing' %}
                        <span class="badge bg-info">制作中</span>
                    {% elif order.status == 'ready' %}
                        <span class="badge bg-success">已完成</span>
                    {% elif order.status == 'served' %}
                        <span class="badge bg-primary">已上菜</span>
                    {% elif order.status == 'paid' %}
                        <span class="badge bg-success">已结账</span>
                    {% else %}
                        <span class="badge bg-secondary">{{ order.status }}</span>
                    {% endif %}
                </div>
            </div>
            <div class="card-body">
                <!-- 基本信息 -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <p><strong>订单号：</strong>{{ order.order_number }}</p>
                        <p><strong>桌位：</strong>{{ order.table.number }} ({{ order.table.capacity }}人座)</p>
                        <p><strong>服务员：</strong>{{ order.employee.name }}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>下单时间：</strong>{{ order.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</p>
                        {% if order.completed_at %}
                        <p><strong>完成时间：</strong>{{ order.completed_at.strftime('%Y-%m-%d %H:%M:%S') }}</p>
                        {% endif %}
                        {% if order.customer %}
                        <p><strong>客户：</strong>{{ order.customer.name }} ({{ order.customer.phone }})</p>
                        {% endif %}
                    </div>
                </div>
                
                {% if order.special_instructions %}
                <div class="alert alert-info">
                    <strong>特殊要求：</strong>{{ order.special_instructions }}
                </div>
                {% endif %}
                
                <!-- 订单项目 -->
                <h6><i class="fas fa-list"></i> 订单项目</h6>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>菜品</th>
                                <th>单价</th>
                                <th>数量</th>
                                <th>小计</th>
                                <th>特殊要求</th>
                                {% if order.status in ['pending', 'preparing'] %}
                                <th>操作</th>
                                {% endif %}
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in order.order_items %}
                            <tr>
                                <td>
                                    <div>{{ item.menu_item.name }}</div>
                                    <small class="text-muted">{{ item.menu_item.description[:50] }}...</small>
                                </td>
                                <td>¥{{ "%.2f"|format(item.unit_price) }}</td>
                                <td>{{ item.quantity }}</td>
                                <td><strong>¥{{ "%.2f"|format(item.quantity * item.unit_price) }}</strong></td>
                                <td>
                                    {% if item.special_instructions %}
                                        <small>{{ item.special_instructions }}</small>
                                    {% else %}
                                        <span class="text-muted">无</span>
                                    {% endif %}
                                </td>
                                {% if order.status in ['pending', 'preparing'] %}
                                <td>
                                    <button class="btn btn-sm btn-outline-danger" onclick="removeItem({{ item.id }})">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                                {% endif %}
                            </tr>
                            {% endfor %}
                        </tbody>
                        <tfoot>
                            <tr class="table-light">
                                <th colspan="3">总计</th>
                                <th><strong>¥{{ "%.2f"|format(order.total_amount) }}</strong></th>
                                <th colspan="2"></th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
                
                <!-- 操作按钮 -->
                <div class="text-center mt-3">
                    {% if order.status == 'pending' %}
                        <button class="btn btn-info" onclick="updateOrderStatus('preparing')">
                            <i class="fas fa-play"></i> 开始制作
                        </button>
                    {% elif order.status == 'preparing' %}
                        <button class="btn btn-success" onclick="updateOrderStatus('ready')">
                            <i class="fas fa-check"></i> 制作完成
                        </button>
                    {% elif order.status == 'ready' %}
                        <button class="btn btn-primary" onclick="updateOrderStatus('served')">
                            <i class="fas fa-utensils"></i> 已上菜
                        </button>
                    {% endif %}
                    
                    {% if order.status in ['served', 'ready'] %}
                        <a href="{{ url_for('pos.checkout', order_id=order.id) }}" class="btn btn-warning">
                            <i class="fas fa-credit-card"></i> 结账
                        </a>
                    {% endif %}
                    
                    <button class="btn btn-outline-secondary" onclick="printOrder()">
                        <i class="fas fa-print"></i> 打印
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 菜品选择 -->
    <div class="col-md-4">
        {% if order.status in ['pending', 'preparing'] %}
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-plus"></i> 添加菜品</h6>
            </div>
            <div class="card-body">
                <!-- 分类按钮 -->
                <div class="btn-group-vertical w-100 mb-3" role="group">
                    <button type="button" class="btn btn-outline-primary active" onclick="showCategory('all')">
                        全部菜品
                    </button>
                    {% for category in categories %}
                    <button type="button" class="btn btn-outline-primary" onclick="showCategory({{ category.id }})">
                        {{ category.name }}
                    </button>
                    {% endfor %}
                </div>
                
                <!-- 菜品列表 -->
                <div id="menuItems" style="max-height: 400px; overflow-y: auto;">
                    {% for item in menu_items %}
                    <div class="menu-item mb-2" data-category="{{ item.category_id }}">
                        <div class="card card-body p-2">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">{{ item.name }}</h6>
                                    <small class="text-muted">¥{{ item.price }}</small>
                                </div>
                                <button class="btn btn-sm btn-primary" 
                                        onclick="selectMenuItem({{ item.id }}, '{{ item.name }}', {{ item.price }})">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- 菜品选择模态框 -->
<div class="modal fade" id="menuItemModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">添加菜品</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="menuItemForm">
                    <div class="mb-3">
                        <label class="form-label">菜品名称</label>
                        <input type="text" class="form-control" id="itemName" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">单价</label>
                        <input type="text" class="form-control" id="itemPrice" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">数量</label>
                        <input type="number" class="form-control" id="itemQuantity" value="1" min="1">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">特殊要求</label>
                        <textarea class="form-control" id="itemInstructions" rows="2"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="addMenuItem()">添加</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let selectedMenuItemId = null;
const currentOrderId = {{ order.id }};

function selectMenuItem(itemId, itemName, itemPrice) {
    selectedMenuItemId = itemId;
    $('#itemName').val(itemName);
    $('#itemPrice').val('¥' + itemPrice);
    $('#itemQuantity').val(1);
    $('#itemInstructions').val('');
    $('#menuItemModal').modal('show');
}

function addMenuItem() {
    const quantity = $('#itemQuantity').val();
    const instructions = $('#itemInstructions').val();
    
    $.ajax({
        url: '/pos/add-item/' + currentOrderId,
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            menu_item_id: selectedMenuItemId,
            quantity: parseInt(quantity),
            special_instructions: instructions
        }),
        success: function(response) {
            if (response.success) {
                alert(response.message);
                $('#menuItemModal').modal('hide');
                location.reload();
            } else {
                alert(response.message);
            }
        },
        error: function() {
            alert('添加失败，请重试');
        }
    });
}

function removeItem(itemId) {
    if (confirm('确定要移除这个菜品吗？')) {
        $.ajax({
            url: '/pos/remove-item/' + itemId,
            method: 'POST',
            success: function(response) {
                if (response.success) {
                    alert(response.message);
                    location.reload();
                } else {
                    alert(response.message);
                }
            },
            error: function() {
                alert('移除失败，请重试');
            }
        });
    }
}

function updateOrderStatus(newStatus) {
    const statusText = {
        'preparing': '开始制作',
        'ready': '制作完成',
        'served': '已上菜'
    };
    
    if (confirm(`确定要${statusText[newStatus]}吗？`)) {
        // 这里应该发送状态更新请求
        alert(`订单状态已更新为：${statusText[newStatus]}`);
        location.reload();
    }
}

function showCategory(categoryId) {
    $('.btn-outline-primary').removeClass('active');
    event.target.classList.add('active');
    
    if (categoryId === 'all') {
        $('.menu-item').show();
    } else {
        $('.menu-item').hide();
        $('.menu-item[data-category="' + categoryId + '"]').show();
    }
}

function printOrder() {
    window.print();
}
</script>
{% endblock %}
