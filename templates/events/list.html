{% extends 'base.html' %}

{% block title %}{{ get_text('home_title') }} - {{ get_text('nav_events') }}{% endblock %}
{% block og_description %}Browse and discover upcoming sports events from around the world. Filter by category or search for specific events.{% endblock %}

{% block extra_css %}
<style>
    .animate-on-scroll {
        opacity: 0;
        transform: translateY(30px);
        transition: all 0.6s ease;
    }

    .animate-on-scroll.visible,
    .animate-on-scroll.animated {
        opacity: 1;
        transform: translateY(0);
    }

    .hover-shadow {
        transition: all 0.3s ease;
    }

    .hover-shadow:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }

    .hover-shadow:hover .event-list-image {
        transform: scale(1.05);
    }

    .event-image-container {
        overflow: hidden;
        border-radius: 0.5rem 0.5rem 0 0;
    }

    .event-list-image {
        transition: transform 0.3s ease;
    }

    .text-truncate-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .text-truncate-3 {
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    /* 强制显示图片 */
    .event-list-image, .card-img-top {
        display: block !important;
        opacity: 1 !important;
        visibility: visible !important;
    }

    /* 美化卡片样式 */
    .event-card {
        border: none;
        border-radius: 1rem;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .event-card .card-body {
        padding: 1.5rem;
    }

    .event-card .card-footer {
        padding: 1rem 1.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="bg-primary-light p-4 rounded-lg shadow-sm">
                <h1 class="mb-2">{{ get_text('events_title') }}</h1>
                <p class="lead mb-0">{{ get_text('events_subtitle') }}</p>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Sidebar with filters -->
        <div class="col-lg-3 col-md-4 mb-4">
            <div class="filter-card sticky-top" style="top: 100px;">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">{{ get_text('filter_by_category') }}</h5>
                    {% if current_category or search_query %}
                        <a href="{{ url_for('events_list') }}" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-times me-1"></i> {{ get_text('Clear All') }}
                        </a>
                    {% endif %}
                </div>
                <div class="card-body">
                    <!-- Search Form -->
                    <div class="mb-4">
                        <h6 class="mb-3">{{ get_text('search_events') }}</h6>
                        <form action="{{ url_for('events_list') }}" method="get" class="mb-3">
                            {% if current_category %}
                                <input type="hidden" name="category" value="{{ current_category }}">
                            {% endif %}
                            <div class="input-group">
                                <input type="text" class="form-control" name="q" placeholder="{{ get_text('search_placeholder') }}" value="{{ search_query }}">
                                <button class="btn btn-primary" type="submit">
                                    <i class="fas fa-search"></i> {{ get_text('Search') }}
                                </button>
                            </div>
                        </form>
                        {% if search_query %}
                            <div class="d-flex align-items-center">
                                <span class="badge bg-primary me-2">{{ search_query }}</span>
                                <a href="{{ url_for('events_list', category=current_category) }}" class="text-decoration-none small">
                                    <i class="fas fa-times"></i>
                                </a>
                            </div>
                        {% endif %}
                    </div>

                    <!-- Categories Filter -->
                    <h6 class="mb-3">{{ get_text('categories') }}</h6>
                    <div class="list-group">
                        <a href="{{ url_for('events_list', q=search_query) }}"
                           class="list-group-item list-group-item-action d-flex justify-content-between align-items-center {{ 'active' if not current_category else '' }}">
                            <span><i class="fas fa-globe me-2"></i> {{ get_text('all_categories') }}</span>
                            <span class="badge bg-primary rounded-pill">{{ events|length }}</span>
                        </a>
                        {% for category in categories %}
                            <a href="{{ url_for('events_list', category=category.id, q=search_query) }}"
                               class="list-group-item list-group-item-action d-flex justify-content-between align-items-center {{ 'active' if current_category == category.id else '' }}">
                                <span>{{ get_text(category.name.lower()) }}</span>
                                <span class="badge bg-primary rounded-pill">{{ events|selectattr('category_id', 'equalto', category.id)|list|length }}</span>
                            </a>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Main content with events list -->
        <div class="col-lg-9 col-md-8">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    {% if current_category %}
                        {% for category in categories %}
                            {% if category.id == current_category %}
                                {{ get_text(category.name.lower()) }} {{ get_text('nav_events') }}
                            {% endif %}
                        {% endfor %}
                    {% else %}
                        {{ get_text('all_events') }}
                    {% endif %}
                    {% if search_query %}
                        {{ get_text('matching') }} "{{ search_query }}"
                    {% endif %}
                </h2>
                <div class="d-flex align-items-center">
                    <span class="me-2 text-muted">{{ events|length }} {{ get_text('events found') }}</span>
                </div>
            </div>

            {% if events %}
                <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
                    {% for event in events %}
                        <div class="col animate-on-scroll" style="animation-delay: {{ loop.index0 * 0.1 }}s">
                            <div class="card h-100 event-card hover-shadow">
                                <div class="position-relative event-image-container">
                                    <img src="{{ event.get_featured_image_url() if event.featured else event.get_image_url() }}"
                                         class="card-img-top event-list-image"
                                         alt="{{ get_text(event.name) }}"
                                         style="height: 280px; object-fit: cover; display: block !important; opacity: 1 !important; visibility: visible !important; border-radius: 0.5rem 0.5rem 0 0; transition: transform 0.3s ease;"
                                         onerror="console.log('Event list image failed:', this.src); this.src='{{ event.get_image_url() }}'; this.style.background='linear-gradient(135deg, #f8f9fa, #e9ecef)'; this.style.display='block';"
                                         onload="console.log('Event list image loaded:', this.src); this.style.display='block'; this.style.opacity='1';"
                                         loading="lazy">
                                    <span class="badge bg-primary position-absolute" style="top: 15px; left: 15px; font-size: 0.9rem; padding: 0.5rem 0.75rem; border-radius: 1rem; backdrop-filter: blur(10px); background: rgba(13, 110, 253, 0.9);">
                                        {{ get_text(event.category.name.lower()) }}
                                    </span>
                                    {% if event.featured %}
                                        <span class="badge bg-warning position-absolute" style="top: 10px; right: 10px;">
                                            {{ get_text('Featured') }}
                                        </span>
                                    {% endif %}
                                </div>
                                <div class="card-body">
                                    <h5 class="card-title text-truncate-2">{{ get_text(event.name) }}</h5>
                                    <p class="card-text text-truncate-3">{{ get_text(event.description)[:120] }}{% if get_text(event.description)|length > 120 %}...{% endif %}</p>
                                    <div class="d-flex flex-column gap-1 mb-3">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-map-marker-alt text-primary me-2"></i>
                                            <small>{{ get_text(event.location) }}</small>
                                        </div>
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-calendar-alt text-primary me-2"></i>
                                            <small>{{ format_date(event.start_date, 'full') }}</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-footer bg-transparent border-top-0">
                                    <a href="{{ url_for('event_details', event_id=event.id) }}" class="btn btn-primary w-100">
                                        <i class="fas fa-info-circle me-1"></i> {{ get_text('view_details') }}
                                    </a>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="alert alert-info shadow-sm">
                    <i class="fas fa-info-circle me-2"></i>
                    {{ get_text('no_events_found') }}. {% if search_query %}{{ get_text('try_adjusting') }}.{% endif %}
                </div>
                <div class="text-center py-5">
                    <img src="{{ url_for('static', filename='images/no-results.png') }}" alt="No results" class="img-fluid mb-4" style="max-width: 200px;">
                    <h3>{{ get_text('no_events_found') }}</h3>
                    <p class="text-muted">{{ get_text('try_adjusting') }}</p>
                    <a href="{{ url_for('events_list') }}" class="btn btn-primary mt-3">
                        <i class="fas fa-sync-alt me-1"></i> {{ get_text('reset_filters') }}
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add animation classes to elements when they come into view
        const animateOnScroll = () => {
            const elements = document.querySelectorAll('.animate-on-scroll');

            elements.forEach(element => {
                const elementPosition = element.getBoundingClientRect().top;
                const windowHeight = window.innerHeight;

                if (elementPosition < windowHeight - 50) {
                    element.classList.add('visible', 'animated');
                }
            });
        };

        window.addEventListener('scroll', animateOnScroll);
        animateOnScroll(); // Run once on page load
    });
</script>
{% endblock %}
