{% extends "base.html" %}

{% block title %}库存管理 - 牛排餐厅管理系统{% endblock %}
{% block page_title %}库存管理{% endblock %}

{% block content %}
<!-- 库存概览 -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card text-white bg-danger">
            <div class="card-header">
                <i class="fas fa-exclamation-triangle"></i>
                紧急库存
            </div>
            <div class="card-body">
                <h4 class="card-title">{{ critical_stock|length }}</h4>
                <p class="card-text">需要立即补货</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card text-white bg-warning">
            <div class="card-header">
                <i class="fas fa-exclamation-circle"></i>
                低库存
            </div>
            <div class="card-body">
                <h4 class="card-title">{{ low_stock|length }}</h4>
                <p class="card-text">建议补货</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card text-white bg-success">
            <div class="card-header">
                <i class="fas fa-check-circle"></i>
                正常库存
            </div>
            <div class="card-body">
                <h4 class="card-title">{{ normal_stock|length }}</h4>
                <p class="card-text">库存充足</p>
            </div>
        </div>
    </div>
</div>

<!-- 操作按钮 -->
<div class="row mb-3">
    <div class="col-12">
        <div class="btn-group" role="group">
            <a href="{{ url_for('inventory.add_ingredient') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> 添加原料
            </a>
            <a href="{{ url_for('inventory.menu_management') }}" class="btn btn-info">
                <i class="fas fa-utensils"></i> 菜品管理
            </a>
            <button class="btn btn-success" onclick="exportInventory()">
                <i class="fas fa-download"></i> 导出库存
            </button>
        </div>
    </div>
</div>

<!-- 库存列表 -->
<div class="card">
    <div class="card-header">
        <h5><i class="fas fa-boxes"></i> 库存列表</h5>
        <div class="float-end">
            <input type="text" class="form-control form-control-sm" id="searchInput" placeholder="搜索原料...">
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover" id="inventoryTable">
                <thead>
                    <tr>
                        <th>原料名称</th>
                        <th>当前库存</th>
                        <th>最低库存</th>
                        <th>单位</th>
                        <th>单价</th>
                        <th>供应商</th>
                        <th>状态</th>
                        <th>最后更新</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for ingredient in ingredients %}
                    <tr>
                        <td>{{ ingredient.name }}</td>
                        <td>
                            <span class="fw-bold">{{ ingredient.current_stock }}</span>
                        </td>
                        <td>{{ ingredient.min_stock }}</td>
                        <td>{{ ingredient.unit }}</td>
                        <td>¥{{ "%.2f"|format(ingredient.unit_cost) }}</td>
                        <td>{{ ingredient.supplier or '-' }}</td>
                        <td>
                            {% if ingredient.current_stock <= 5 %}
                                <span class="badge bg-danger">紧急</span>
                            {% elif ingredient.current_stock <= ingredient.min_stock %}
                                <span class="badge bg-warning">偏低</span>
                            {% else %}
                                <span class="badge bg-success">正常</span>
                            {% endif %}
                        </td>
                        <td>
                            <small class="text-muted">
                                {{ ingredient.last_updated.strftime('%m-%d %H:%M') if ingredient.last_updated else '-' }}
                            </small>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <button class="btn btn-outline-primary" 
                                        onclick="updateStock({{ ingredient.id }}, '{{ ingredient.name }}', {{ ingredient.current_stock }})">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-outline-info" 
                                        onclick="viewHistory({{ ingredient.id }})">
                                    <i class="fas fa-history"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 更新库存模态框 -->
<div class="modal fade" id="updateStockModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">更新库存</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="updateStockForm">
                    <input type="hidden" id="ingredientId">
                    <div class="mb-3">
                        <label class="form-label">原料名称</label>
                        <input type="text" class="form-control" id="ingredientName" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">当前库存</label>
                        <input type="text" class="form-control" id="currentStock" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">操作类型</label>
                        <select class="form-select" id="action" required>
                            <option value="">请选择操作</option>
                            <option value="add">入库（增加）</option>
                            <option value="subtract">出库（减少）</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">数量</label>
                        <input type="number" class="form-control" id="quantity" step="0.1" min="0.1" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">备注</label>
                        <textarea class="form-control" id="reason" rows="2" placeholder="请说明操作原因"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="submitStockUpdate()">确认更新</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 搜索功能
$('#searchInput').on('keyup', function() {
    var value = $(this).val().toLowerCase();
    $('#inventoryTable tbody tr').filter(function() {
        $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
    });
});

// 更新库存
function updateStock(id, name, currentStock) {
    $('#ingredientId').val(id);
    $('#ingredientName').val(name);
    $('#currentStock').val(currentStock);
    $('#action').val('');
    $('#quantity').val('');
    $('#reason').val('');
    $('#updateStockModal').modal('show');
}

// 提交库存更新
function submitStockUpdate() {
    const form = $('#updateStockForm');
    const formData = new FormData();
    
    formData.append('action', $('#action').val());
    formData.append('quantity', $('#quantity').val());
    formData.append('reason', $('#reason').val());
    
    const ingredientId = $('#ingredientId').val();
    
    $.ajax({
        url: '/inventory/update/' + ingredientId,
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            $('#updateStockModal').modal('hide');
            location.reload();
        },
        error: function() {
            alert('更新失败，请重试');
        }
    });
}

// 查看历史记录
function viewHistory(id) {
    // 这里可以实现查看库存变更历史的功能
    alert('查看库存变更历史功能待实现');
}

// 导出库存
function exportInventory() {
    // 这里可以实现导出库存报表的功能
    alert('导出库存功能待实现');
}

// 定时刷新低库存提醒
setInterval(function() {
    $.get('/inventory/api/low-stock', function(data) {
        if (data.length > 0) {
            // 可以在这里添加桌面通知或其他提醒方式
            console.log('低库存商品:', data);
        }
    });
}, 60000); // 每分钟检查一次
</script>
{% endblock %}
