{% extends 'base.html' %}

{% block title %}Register - Sports Events{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">{{ get_text('Register') }}</h4>
                </div>
                <div class="card-body">
                    <form method="post">
                        <div class="mb-3">
                            <label for="username" class="form-label">{{ get_text('Username') }}</label>
                            <input type="text" class="form-control" id="username" name="username" required>
                        </div>
                        <div class="mb-3">
                            <label for="email" class="form-label">{{ get_text('Email address') }}</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>
                        <div class="mb-3">
                            <label for="password" class="form-label">{{ get_text('Password') }}</label>
                            <input type="password" class="form-control" id="password" name="password" required>
                        </div>
                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">{{ get_text('Confirm Password') }}</label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                        </div>
                        <button type="submit" class="btn btn-primary w-100">{{ get_text('Register') }}</button>
                    </form>
                </div>
                <div class="card-footer text-center">
                    <p class="mb-0">{{ get_text('Already have an account?') }} <a href="{{ url_for('login') }}">{{ get_text('Login') }}</a></p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
