{% extends "base.html" %}

{% block title %}登录 - 牛排餐厅管理系统{% endblock %}

{% block content %}
<div class="login-container">
    <!-- 背景装饰 -->
    <div class="login-bg">
        <div class="floating-shapes">
            <div class="shape shape-1"></div>
            <div class="shape shape-2"></div>
            <div class="shape shape-3"></div>
            <div class="shape shape-4"></div>
        </div>
    </div>

    <div class="container">
        <div class="row justify-content-center align-items-center min-vh-100">
            <div class="col-xl-5 col-lg-6 col-md-8">
                <div class="login-card card border-0 shadow-lg">
                    <div class="card-body p-0">
                        <!-- 登录头部 -->
                        <div class="login-header text-center p-5 pb-4">
                            <div class="login-logo mb-4">
                                <div class="logo-circle">
                                    <i class="fas fa-utensils fa-3x text-white"></i>
                                </div>
                            </div>
                            <h2 class="fw-bold mb-2">牛排餐厅</h2>
                            <h5 class="text-muted mb-0">管理系统</h5>
                            <p class="text-muted mt-3">请登录您的管理账户</p>
                        </div>

                        <!-- 登录表单 -->
                        <div class="login-form px-5 pb-5">
                            <form method="POST" id="loginForm">
                                <div class="form-floating mb-4">
                                    <input type="text"
                                           class="form-control form-control-lg"
                                           id="username"
                                           name="username"
                                           placeholder="用户名"
                                           required>
                                    <label for="username">
                                        <i class="fas fa-user me-2"></i>用户名
                                    </label>
                                </div>

                                <div class="form-floating mb-4">
                                    <input type="password"
                                           class="form-control form-control-lg"
                                           id="password"
                                           name="password"
                                           placeholder="密码"
                                           required>
                                    <label for="password">
                                        <i class="fas fa-lock me-2"></i>密码
                                    </label>
                                </div>

                                <div class="form-check mb-4">
                                    <input type="checkbox" class="form-check-input" id="remember">
                                    <label class="form-check-label" for="remember">
                                        记住我的登录状态
                                    </label>
                                </div>

                                <button type="submit" class="btn btn-primary btn-lg w-100 mb-4">
                                    <i class="fas fa-sign-in-alt me-2"></i>
                                    <span class="btn-text">登录系统</span>
                                    <span class="btn-loading d-none">
                                        <span class="spinner-border spinner-border-sm me-2"></span>
                                        登录中...
                                    </span>
                                </button>
                            </form>

                            <!-- 快速链接 -->
                            <div class="text-center mb-4">
                                <a href="{{ url_for('reservation.index') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-calendar-plus me-2"></i>
                                    客户预订入口
                                </a>
                            </div>

                            <!-- 演示账户 -->
                            <div class="demo-accounts">
                                <h6 class="text-center mb-3">
                                    <i class="fas fa-info-circle text-info me-2"></i>
                                    演示账户
                                </h6>
                                <div class="row g-2">
                                    <div class="col-12">
                                        <div class="demo-account-item" onclick="fillDemoAccount('admin', 'admin123')">
                                            <div class="d-flex align-items-center">
                                                <div class="demo-avatar bg-danger">
                                                    <i class="fas fa-crown"></i>
                                                </div>
                                                <div class="ms-3">
                                                    <div class="fw-bold">管理员</div>
                                                    <small class="text-muted">admin / admin123</small>
                                                </div>
                                                <div class="ms-auto">
                                                    <i class="fas fa-arrow-right text-muted"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="demo-account-item" onclick="fillDemoAccount('waiter', 'waiter123')">
                                            <div class="text-center">
                                                <div class="demo-avatar bg-info mx-auto mb-2">
                                                    <i class="fas fa-user-tie"></i>
                                                </div>
                                                <div class="fw-bold small">服务员</div>
                                                <small class="text-muted">waiter</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="demo-account-item" onclick="fillDemoAccount('chef', 'chef123')">
                                            <div class="text-center">
                                                <div class="demo-avatar bg-success mx-auto mb-2">
                                                    <i class="fas fa-chef-hat"></i>
                                                </div>
                                                <div class="fw-bold small">厨师</div>
                                                <small class="text-muted">chef</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    /* 登录容器 */
    .login-container {
        position: relative;
        min-height: 100vh;
        overflow: hidden;
    }

    /* 背景装饰 */
    .login-bg {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        z-index: -1;
    }

    .floating-shapes {
        position: absolute;
        width: 100%;
        height: 100%;
        overflow: hidden;
    }

    .shape {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.1);
        animation: float 20s infinite linear;
    }

    .shape-1 {
        width: 80px;
        height: 80px;
        top: 20%;
        left: 10%;
        animation-delay: 0s;
    }

    .shape-2 {
        width: 120px;
        height: 120px;
        top: 60%;
        right: 10%;
        animation-delay: -5s;
    }

    .shape-3 {
        width: 60px;
        height: 60px;
        bottom: 20%;
        left: 20%;
        animation-delay: -10s;
    }

    .shape-4 {
        width: 100px;
        height: 100px;
        top: 10%;
        right: 30%;
        animation-delay: -15s;
    }

    @keyframes float {
        0%, 100% {
            transform: translateY(0px) rotate(0deg);
        }
        33% {
            transform: translateY(-30px) rotate(120deg);
        }
        66% {
            transform: translateY(30px) rotate(240deg);
        }
    }

    /* 登录卡片 */
    .login-card {
        border-radius: 2rem;
        backdrop-filter: blur(20px);
        background: rgba(255, 255, 255, 0.95);
        animation: slideInUp 0.8s ease-out;
        overflow: hidden;
    }

    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(50px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Logo */
    .logo-circle {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto;
        box-shadow: 0 0.5rem 1rem rgba(102, 126, 234, 0.3);
        animation: logoFloat 3s ease-in-out infinite;
    }

    @keyframes logoFloat {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
    }

    /* 表单控件 */
    .form-floating > .form-control {
        border-radius: 1rem;
        border: 2px solid rgba(0, 0, 0, 0.1);
        padding: 1.5rem 1rem 0.5rem;
        height: auto;
        transition: all 0.3s ease;
    }

    .form-floating > .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        transform: translateY(-2px);
    }

    .form-floating > label {
        padding: 1rem;
        color: #6c757d;
        transition: all 0.3s ease;
    }

    /* 登录按钮 */
    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 1rem;
        padding: 1rem 2rem;
        font-weight: 600;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 0.5rem 1rem rgba(102, 126, 234, 0.4);
    }

    .btn-primary:active {
        transform: translateY(0);
    }

    /* 演示账户 */
    .demo-accounts {
        background: rgba(102, 126, 234, 0.05);
        border-radius: 1rem;
        padding: 1.5rem;
        border: 1px solid rgba(102, 126, 234, 0.1);
    }

    .demo-account-item {
        background: white;
        border-radius: 0.75rem;
        padding: 1rem;
        margin-bottom: 0.5rem;
        cursor: pointer;
        transition: all 0.3s ease;
        border: 1px solid rgba(0, 0, 0, 0.05);
    }

    .demo-account-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
        border-color: #667eea;
    }

    .demo-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.2rem;
    }

    /* 复选框 */
    .form-check-input:checked {
        background-color: #667eea;
        border-color: #667eea;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .login-card {
            margin: 1rem;
            border-radius: 1.5rem;
        }

        .login-header {
            padding: 2rem 1.5rem 1rem !important;
        }

        .login-form {
            padding: 0 1.5rem 2rem !important;
        }

        .logo-circle {
            width: 60px;
            height: 60px;
        }

        .logo-circle i {
            font-size: 2rem !important;
        }
    }

    /* 加载状态 */
    .btn-loading .spinner-border {
        width: 1rem;
        height: 1rem;
    }

    /* 输入框图标 */
    .form-floating label i {
        opacity: 0.7;
    }

    .form-floating > .form-control:focus + label i {
        opacity: 1;
        color: #667eea;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 页面加载动画
    setTimeout(function() {
        $('.login-card').addClass('loaded');
    }, 100);

    // 自动聚焦到用户名输入框
    setTimeout(function() {
        $('#username').focus();
    }, 800);

    // 表单提交处理
    $('#loginForm').on('submit', function(e) {
        const submitBtn = $(this).find('button[type="submit"]');
        const btnText = submitBtn.find('.btn-text');
        const btnLoading = submitBtn.find('.btn-loading');

        // 显示加载状态
        btnText.addClass('d-none');
        btnLoading.removeClass('d-none');
        submitBtn.prop('disabled', true);

        // 添加提交动画
        submitBtn.addClass('submitting');

        // 如果是演示环境，可以在这里添加延迟
        // setTimeout(() => {
        //     this.submit();
        // }, 1000);
    });

    // 输入框焦点效果
    $('.form-control').on('focus', function() {
        $(this).closest('.form-floating').addClass('focused');
    }).on('blur', function() {
        if (!$(this).val()) {
            $(this).closest('.form-floating').removeClass('focused');
        }
    });

    // 回车键提交表单
    $('.form-control').keypress(function(e) {
        if (e.which == 13) {
            $('#loginForm').submit();
        }
    });

    // 演示账户点击效果
    $('.demo-account-item').on('click', function() {
        $(this).addClass('clicked');
        setTimeout(() => {
            $(this).removeClass('clicked');
        }, 200);
    });
});

// 填充演示账户信息
function fillDemoAccount(username, password) {
    $('#username').val(username).trigger('input');
    $('#password').val(password).trigger('input');

    // 添加填充动画
    $('#username, #password').addClass('filled');
    setTimeout(() => {
        $('#username, #password').removeClass('filled');
    }, 600);

    // 自动聚焦到登录按钮
    setTimeout(() => {
        $('button[type="submit"]').focus();
    }, 300);
}

// 添加动画CSS
$('<style>')
    .prop('type', 'text/css')
    .html(`
        .login-card.loaded {
            animation: none;
        }

        .focused {
            transform: scale(1.02);
        }

        .clicked {
            transform: scale(0.98) !important;
        }

        .submitting {
            transform: scale(0.98);
        }

        .filled {
            animation: fillPulse 0.6s ease-in-out;
        }

        @keyframes fillPulse {
            0%, 100% {
                background-color: transparent;
            }
            50% {
                background-color: rgba(102, 126, 234, 0.1);
            }
        }

        /* 键盘导航增强 */
        .form-control:focus,
        .btn:focus,
        .demo-account-item:focus {
            outline: 2px solid #667eea;
            outline-offset: 2px;
        }

        /* 错误状态 */
        .form-control.is-invalid {
            border-color: #dc3545;
            animation: shake 0.5s ease-in-out;
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        /* 成功状态 */
        .form-control.is-valid {
            border-color: #28a745;
        }

        /* 加载按钮动画 */
        .btn-loading .spinner-border {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    `)
    .appendTo('head');

// 表单验证增强
function validateForm() {
    let isValid = true;

    // 验证用户名
    const username = $('#username').val().trim();
    if (username.length < 3) {
        $('#username').addClass('is-invalid').removeClass('is-valid');
        isValid = false;
    } else {
        $('#username').addClass('is-valid').removeClass('is-invalid');
    }

    // 验证密码
    const password = $('#password').val();
    if (password.length < 6) {
        $('#password').addClass('is-invalid').removeClass('is-valid');
        isValid = false;
    } else {
        $('#password').addClass('is-valid').removeClass('is-invalid');
    }

    return isValid;
}

// 实时验证
$('#username, #password').on('input', function() {
    const field = $(this);
    const value = field.val().trim();
    const minLength = field.attr('id') === 'username' ? 3 : 6;

    if (value.length >= minLength) {
        field.addClass('is-valid').removeClass('is-invalid');
    } else if (value.length > 0) {
        field.addClass('is-invalid').removeClass('is-valid');
    } else {
        field.removeClass('is-valid is-invalid');
    }
});

// 键盘快捷键
$(document).keydown(function(e) {
    // Alt + 1-3 快速选择演示账户
    if (e.altKey) {
        switch(e.which) {
            case 49: // Alt + 1
                fillDemoAccount('admin', 'admin123');
                e.preventDefault();
                break;
            case 50: // Alt + 2
                fillDemoAccount('waiter', 'waiter123');
                e.preventDefault();
                break;
            case 51: // Alt + 3
                fillDemoAccount('chef', 'chef123');
                e.preventDefault();
                break;
        }
    }
});
</script>
{% endblock %}
