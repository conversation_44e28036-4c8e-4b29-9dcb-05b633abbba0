{% extends "base.html" %}

{% block title %}登录 - 牛排餐厅管理系统{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-xl-6 col-lg-8 col-md-10">
            <div class="card o-hidden border-0 shadow-lg my-5">
                <div class="card-body p-0">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="p-5">
                                <div class="text-center">
                                    <h1 class="h4 text-gray-900 mb-4">
                                        <i class="fas fa-utensils fa-2x text-primary mb-3"></i><br>
                                        牛排餐厅管理系统
                                    </h1>
                                    <p class="text-muted mb-4">请登录您的账户</p>
                                </div>
                                
                                <form method="POST" class="user">
                                    <div class="form-group mb-3">
                                        <input type="text" 
                                               class="form-control form-control-user" 
                                               id="username" 
                                               name="username"
                                               placeholder="用户名" 
                                               required>
                                    </div>
                                    
                                    <div class="form-group mb-3">
                                        <input type="password" 
                                               class="form-control form-control-user"
                                               id="password" 
                                               name="password"
                                               placeholder="密码" 
                                               required>
                                    </div>
                                    
                                    <div class="form-group mb-3">
                                        <div class="custom-control custom-checkbox small">
                                            <input type="checkbox" class="custom-control-input" id="remember">
                                            <label class="custom-control-label" for="remember">记住我</label>
                                        </div>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-primary btn-user btn-block">
                                        <i class="fas fa-sign-in-alt"></i>
                                        登录
                                    </button>
                                </form>
                                
                                <hr>
                                
                                <div class="text-center">
                                    <a class="small" href="{{ url_for('reservation.index') }}">
                                        <i class="fas fa-calendar-plus"></i>
                                        客户预订入口
                                    </a>
                                </div>
                                
                                <!-- 演示账户信息 -->
                                <div class="mt-4">
                                    <div class="alert alert-info">
                                        <h6><i class="fas fa-info-circle"></i> 演示账户</h6>
                                        <small>
                                            <strong>管理员：</strong> admin / admin123<br>
                                            <strong>服务员：</strong> waiter / waiter123<br>
                                            <strong>厨师：</strong> chef / chef123
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.form-control-user {
    font-size: 0.8rem;
    border-radius: 10rem;
    padding: 1.5rem 1rem;
}

.btn-user {
    font-size: 0.8rem;
    border-radius: 10rem;
    padding: 0.75rem 1rem;
}

.card {
    border-radius: 1rem;
}

body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.custom-control-label::before {
    border-radius: 0.25rem;
}

.custom-control-input:checked ~ .custom-control-label::before {
    background-color: #667eea;
    border-color: #667eea;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 自动聚焦到用户名输入框
    $('#username').focus();
    
    // 回车键提交表单
    $('#password').keypress(function(e) {
        if (e.which == 13) {
            $('form').submit();
        }
    });
});
</script>
{% endblock %}
