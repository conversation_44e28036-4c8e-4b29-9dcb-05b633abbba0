{% extends "base.html" %}

{% block title %}牛排餐厅 - 在线预订{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 餐厅介绍 -->
    <div class="hero-section text-center py-5 mb-5" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 1rem;">
        <div class="container">
            <h1 class="display-4 mb-3">
                <i class="fas fa-utensils"></i>
                欢迎来到精品牛排餐厅
            </h1>
            <p class="lead mb-4">品味顶级牛排，享受优质服务</p>
            <a href="#reservation" class="btn btn-light btn-lg">
                <i class="fas fa-calendar-plus"></i>
                立即预订
            </a>
        </div>
    </div>
    
    <!-- 特色菜品展示 -->
    <div class="row mb-5">
        <div class="col-12">
            <h2 class="text-center mb-4">
                <i class="fas fa-star"></i>
                招牌菜品
            </h2>
        </div>
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <img src="https://via.placeholder.com/400x250/667eea/ffffff?text=菲力牛排" class="card-img-top" alt="菲力牛排">
                <div class="card-body">
                    <h5 class="card-title">菲力牛排</h5>
                    <p class="card-text">嫩滑菲力牛排，口感极佳，是牛排爱好者的首选。</p>
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="h5 text-primary mb-0">¥188</span>
                        <small class="text-muted">制作时间：15分钟</small>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <img src="https://via.placeholder.com/400x250/764ba2/ffffff?text=西冷牛排" class="card-img-top" alt="西冷牛排">
                <div class="card-body">
                    <h5 class="card-title">西冷牛排</h5>
                    <p class="card-text">经典西冷牛排，肉质鲜美，纹理清晰，口感丰富。</p>
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="h5 text-primary mb-0">¥158</span>
                        <small class="text-muted">制作时间：18分钟</small>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <img src="https://via.placeholder.com/400x250/667eea/ffffff?text=T骨牛排" class="card-img-top" alt="T骨牛排">
                <div class="card-body">
                    <h5 class="card-title">T骨牛排</h5>
                    <p class="card-text">T骨牛排，一次享受两种口感，菲力与西冷的完美结合。</p>
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="h5 text-primary mb-0">¥228</span>
                        <small class="text-muted">制作时间：20分钟</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 在线预订表单 -->
    <div id="reservation" class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-calendar-plus"></i>
                        在线预订
                    </h3>
                </div>
                <div class="card-body">
                    <form action="{{ url_for('reservation.make_reservation') }}" method="POST" id="reservationForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">姓名 *</label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">手机号 *</label>
                                <input type="tel" class="form-control" id="phone" name="phone" required>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">邮箱</label>
                                <input type="email" class="form-control" id="email" name="email">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="party_size" class="form-label">用餐人数 *</label>
                                <select class="form-select" id="party_size" name="party_size" required>
                                    <option value="">请选择人数</option>
                                    <option value="1">1人</option>
                                    <option value="2">2人</option>
                                    <option value="3">3人</option>
                                    <option value="4">4人</option>
                                    <option value="5">5人</option>
                                    <option value="6">6人</option>
                                    <option value="7">7人</option>
                                    <option value="8">8人</option>
                                    <option value="9">9人</option>
                                    <option value="10">10人</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="reservation_date" class="form-label">预订日期 *</label>
                                <input type="date" class="form-control" id="reservation_date" name="reservation_date" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="reservation_time" class="form-label">预订时间 *</label>
                                <select class="form-select" id="reservation_time" name="reservation_time" required>
                                    <option value="">请先选择日期和人数</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="special_requests" class="form-label">特殊要求</label>
                            <textarea class="form-control" id="special_requests" name="special_requests" rows="3" placeholder="如有特殊饮食要求或其他需求，请在此说明"></textarea>
                        </div>
                        
                        <div class="text-center">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-check"></i>
                                确认预订
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 餐厅信息 -->
    <div class="row mt-5">
        <div class="col-md-4">
            <div class="card text-center">
                <div class="card-body">
                    <i class="fas fa-clock fa-3x text-primary mb-3"></i>
                    <h5 class="card-title">营业时间</h5>
                    <p class="card-text">
                        周一至周日<br>
                        11:00 - 22:00
                    </p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card text-center">
                <div class="card-body">
                    <i class="fas fa-map-marker-alt fa-3x text-primary mb-3"></i>
                    <h5 class="card-title">餐厅地址</h5>
                    <p class="card-text">
                        北京市朝阳区<br>
                        三里屯太古里南区
                    </p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card text-center">
                <div class="card-body">
                    <i class="fas fa-phone fa-3x text-primary mb-3"></i>
                    <h5 class="card-title">联系电话</h5>
                    <p class="card-text">
                        预订热线<br>
                        010-8888-8888
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 设置最小日期为今天
    var today = new Date().toISOString().split('T')[0];
    $('#reservation_date').attr('min', today);
    
    // 当日期或人数改变时，更新可用时间
    $('#reservation_date, #party_size').change(function() {
        updateAvailableTimes();
    });
    
    function updateAvailableTimes() {
        var date = $('#reservation_date').val();
        var partySize = $('#party_size').val();
        
        if (date && partySize) {
            $.get('/reservation/api/available-times', {
                date: date,
                party_size: partySize
            }, function(data) {
                var timeSelect = $('#reservation_time');
                timeSelect.empty();
                
                if (data.available_times && data.available_times.length > 0) {
                    timeSelect.append('<option value="">请选择时间</option>');
                    data.available_times.forEach(function(time) {
                        timeSelect.append('<option value="' + time + '">' + time + '</option>');
                    });
                } else {
                    timeSelect.append('<option value="">该日期暂无可用时间</option>');
                }
            });
        }
    }
});
</script>
{% endblock %}
