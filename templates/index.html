{% extends "base.html" %}

{% block title %}牛排餐厅 - 在线预订{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 餐厅介绍 -->
    <div class="hero-section text-center py-5 mb-5 position-relative overflow-hidden" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 2rem; min-height: 60vh; display: flex; align-items: center;">
        <!-- 背景装饰 -->
        <div class="position-absolute top-0 start-0 w-100 h-100" style="background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"hero-pattern\" width=\"20\" height=\"20\" patternUnits=\"userSpaceOnUse\"><circle cx=\"10\" cy=\"10\" r=\"1\" fill=\"white\" opacity=\"0.1\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23hero-pattern)\"/></svg>'); pointer-events: none;"></div>

        <!-- 浮动元素 -->
        <div class="floating-elements position-absolute w-100 h-100">
            <div class="floating-icon position-absolute" style="top: 20%; left: 10%; animation: float 6s ease-in-out infinite;">
                <i class="fas fa-wine-glass text-white opacity-25 fa-2x"></i>
            </div>
            <div class="floating-icon position-absolute" style="top: 60%; right: 15%; animation: float 8s ease-in-out infinite reverse;">
                <i class="fas fa-utensils text-white opacity-25 fa-3x"></i>
            </div>
            <div class="floating-icon position-absolute" style="bottom: 30%; left: 20%; animation: float 7s ease-in-out infinite;">
                <i class="fas fa-fire text-white opacity-25 fa-2x"></i>
            </div>
        </div>

        <div class="container position-relative z-index-1">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="hero-content">
                        <h1 class="display-3 mb-4 fw-bold" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.3);">
                            <i class="fas fa-utensils me-3"></i>
                            精品牛排餐厅
                        </h1>
                        <p class="lead mb-4 fs-4" style="text-shadow: 1px 1px 2px rgba(0,0,0,0.3);">
                            品味顶级牛排 · 享受优质服务 · 创造美好回忆
                        </p>
                        <div class="hero-stats row text-center mb-4">
                            <div class="col-md-4 mb-3">
                                <div class="stat-item">
                                    <h3 class="fw-bold mb-1">5★</h3>
                                    <small>客户评价</small>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="stat-item">
                                    <h3 class="fw-bold mb-1">1000+</h3>
                                    <small>满意客户</small>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="stat-item">
                                    <h3 class="fw-bold mb-1">30+</h3>
                                    <small>精选菜品</small>
                                </div>
                            </div>
                        </div>
                        <div class="hero-actions">
                            <a href="#reservation" class="btn btn-light btn-lg me-3 px-5 py-3 rounded-pill">
                                <i class="fas fa-calendar-plus me-2"></i>
                                立即预订
                            </a>
                            <a href="#menu" class="btn btn-outline-light btn-lg px-5 py-3 rounded-pill">
                                <i class="fas fa-utensils me-2"></i>
                                查看菜单
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 特色菜品展示 -->
    <div id="menu" class="row mb-5">
        <div class="col-12 text-center mb-5">
            <h2 class="display-5 fw-bold mb-3">
                <i class="fas fa-star text-warning me-3"></i>
                招牌菜品
            </h2>
            <p class="lead text-muted">精选优质食材，匠心烹制每一道美味</p>
        </div>

        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card menu-card h-100 border-0 shadow-sm">
                <div class="position-relative overflow-hidden">
                    <img src="https://images.unsplash.com/photo-1546833999-b9f581a1996d?w=400&h=250&fit=crop&crop=center" class="card-img-top" alt="菲力牛排" style="height: 250px; object-fit: cover; transition: transform 0.3s ease;">
                    <div class="position-absolute top-0 end-0 m-3">
                        <span class="badge bg-danger rounded-pill">招牌</span>
                    </div>
                    <div class="card-img-overlay d-flex align-items-end p-0">
                        <div class="w-100 bg-gradient-dark text-white p-3" style="background: linear-gradient(transparent, rgba(0,0,0,0.7));">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="h4 mb-0 fw-bold">¥188</span>
                                <div class="rating">
                                    <i class="fas fa-star text-warning"></i>
                                    <i class="fas fa-star text-warning"></i>
                                    <i class="fas fa-star text-warning"></i>
                                    <i class="fas fa-star text-warning"></i>
                                    <i class="fas fa-star text-warning"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <h5 class="card-title fw-bold">菲力牛排</h5>
                    <p class="card-text text-muted">嫩滑菲力牛排，口感极佳，选用优质牛肉，是牛排爱好者的首选。</p>
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">
                            <i class="fas fa-clock me-1"></i>
                            制作时间：20分钟
                        </small>
                        <small class="text-success">
                            <i class="fas fa-fire me-1"></i>
                            热销
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card menu-card h-100 border-0 shadow-sm">
                <div class="position-relative overflow-hidden">
                    <img src="https://images.unsplash.com/photo-1558030006-450675393462?w=400&h=250&fit=crop&crop=center" class="card-img-top" alt="西冷牛排" style="height: 250px; object-fit: cover; transition: transform 0.3s ease;">
                    <div class="position-absolute top-0 end-0 m-3">
                        <span class="badge bg-success rounded-pill">经典</span>
                    </div>
                    <div class="card-img-overlay d-flex align-items-end p-0">
                        <div class="w-100 bg-gradient-dark text-white p-3" style="background: linear-gradient(transparent, rgba(0,0,0,0.7));">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="h4 mb-0 fw-bold">¥158</span>
                                <div class="rating">
                                    <i class="fas fa-star text-warning"></i>
                                    <i class="fas fa-star text-warning"></i>
                                    <i class="fas fa-star text-warning"></i>
                                    <i class="fas fa-star text-warning"></i>
                                    <i class="far fa-star text-warning"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <h5 class="card-title fw-bold">西冷牛排</h5>
                    <p class="card-text text-muted">经典西冷牛排，肉质鲜美，纹理清晰，口感丰富层次分明。</p>
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">
                            <i class="fas fa-clock me-1"></i>
                            制作时间：18分钟
                        </small>
                        <small class="text-primary">
                            <i class="fas fa-award me-1"></i>
                            经典
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card menu-card h-100 border-0 shadow-sm">
                <div class="position-relative overflow-hidden">
                    <img src="https://images.unsplash.com/photo-1544025162-d76694265947?w=400&h=250&fit=crop&crop=center" class="card-img-top" alt="T骨牛排" style="height: 250px; object-fit: cover; transition: transform 0.3s ease;">
                    <div class="position-absolute top-0 end-0 m-3">
                        <span class="badge bg-warning rounded-pill">精选</span>
                    </div>
                    <div class="card-img-overlay d-flex align-items-end p-0">
                        <div class="w-100 bg-gradient-dark text-white p-3" style="background: linear-gradient(transparent, rgba(0,0,0,0.7));">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="h4 mb-0 fw-bold">¥228</span>
                                <div class="rating">
                                    <i class="fas fa-star text-warning"></i>
                                    <i class="fas fa-star text-warning"></i>
                                    <i class="fas fa-star text-warning"></i>
                                    <i class="fas fa-star text-warning"></i>
                                    <i class="fas fa-star text-warning"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <h5 class="card-title fw-bold">T骨牛排</h5>
                    <p class="card-text text-muted">T骨牛排，一次享受两种口感，菲力与西冷的完美结合。</p>
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">
                            <i class="fas fa-clock me-1"></i>
                            制作时间：22分钟
                        </small>
                        <small class="text-warning">
                            <i class="fas fa-crown me-1"></i>
                            精选
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 在线预订表单 -->
    <div id="reservation" class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-calendar-plus"></i>
                        在线预订
                    </h3>
                </div>
                <div class="card-body">
                    <form action="{{ url_for('reservation.make_reservation') }}" method="POST" id="reservationForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">姓名 *</label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">手机号 *</label>
                                <input type="tel" class="form-control" id="phone" name="phone" required>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">邮箱</label>
                                <input type="email" class="form-control" id="email" name="email">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="party_size" class="form-label">用餐人数 *</label>
                                <select class="form-select" id="party_size" name="party_size" required>
                                    <option value="">请选择人数</option>
                                    <option value="1">1人</option>
                                    <option value="2">2人</option>
                                    <option value="3">3人</option>
                                    <option value="4">4人</option>
                                    <option value="5">5人</option>
                                    <option value="6">6人</option>
                                    <option value="7">7人</option>
                                    <option value="8">8人</option>
                                    <option value="9">9人</option>
                                    <option value="10">10人</option>
                                </select>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="reservation_date" class="form-label">预订日期 *</label>
                                <input type="date" class="form-control" id="reservation_date" name="reservation_date" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="reservation_time" class="form-label">预订时间 *</label>
                                <select class="form-select" id="reservation_time" name="reservation_time" required>
                                    <option value="">请先选择日期和人数</option>
                                </select>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="special_requests" class="form-label">特殊要求</label>
                            <textarea class="form-control" id="special_requests" name="special_requests" rows="3" placeholder="如有特殊饮食要求或其他需求，请在此说明"></textarea>
                        </div>

                        <div class="text-center">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-check"></i>
                                确认预订
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 餐厅信息 -->
    <div class="row mt-5">
        <div class="col-md-4">
            <div class="card text-center">
                <div class="card-body">
                    <i class="fas fa-clock fa-3x text-primary mb-3"></i>
                    <h5 class="card-title">营业时间</h5>
                    <p class="card-text">
                        周一至周日<br>
                        11:00 - 22:00
                    </p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card text-center">
                <div class="card-body">
                    <i class="fas fa-map-marker-alt fa-3x text-primary mb-3"></i>
                    <h5 class="card-title">餐厅地址</h5>
                    <p class="card-text">
                        北京市朝阳区<br>
                        三里屯太古里南区
                    </p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card text-center">
                <div class="card-body">
                    <i class="fas fa-phone fa-3x text-primary mb-3"></i>
                    <h5 class="card-title">联系电话</h5>
                    <p class="card-text">
                        预订热线<br>
                        010-8888-8888
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    /* 浮动动画 */
    @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-20px); }
    }

    /* 菜品卡片悬停效果 */
    .menu-card:hover img {
        transform: scale(1.05);
    }

    .menu-card {
        transition: all 0.3s ease;
    }

    .menu-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 1rem 3rem rgba(0,0,0,0.175) !important;
    }

    /* Hero区域动画 */
    .hero-content {
        animation: fadeInUp 1s ease-out;
    }

    .hero-stats .stat-item {
        animation: fadeInUp 1s ease-out;
        animation-delay: 0.3s;
        animation-fill-mode: both;
    }

    .hero-actions {
        animation: fadeInUp 1s ease-out;
        animation-delay: 0.6s;
        animation-fill-mode: both;
    }

    /* 预订表单动画 */
    .reservation-form {
        animation: slideInUp 0.8s ease-out;
    }

    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(50px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* 餐厅信息卡片动画 */
    .info-card {
        transition: all 0.3s ease;
    }

    .info-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.15);
    }

    /* 按钮悬停效果增强 */
    .btn-light:hover {
        transform: translateY(-2px);
        box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.15);
    }

    .btn-outline-light:hover {
        transform: translateY(-2px);
        box-shadow: 0 0.5rem 1rem rgba(255,255,255,0.3);
    }

    /* 滚动显示动画 */
    .scroll-reveal {
        opacity: 0;
        transform: translateY(50px);
        transition: all 0.6s ease;
    }

    .scroll-reveal.revealed {
        opacity: 1;
        transform: translateY(0);
    }

    /* 星级评分动画 */
    .rating i {
        transition: all 0.2s ease;
    }

    .rating:hover i {
        transform: scale(1.2);
    }

    /* 加载动画 */
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        transition: opacity 0.5s ease;
    }

    .loading-spinner {
        width: 50px;
        height: 50px;
        border: 3px solid rgba(255,255,255,0.3);
        border-top: 3px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 页面加载动画
    $('.loading-overlay').fadeOut(1000);

    // 设置最小日期为今天
    var today = new Date().toISOString().split('T')[0];
    $('#reservation_date').attr('min', today);

    // 滚动显示动画
    function revealOnScroll() {
        $('.scroll-reveal').each(function() {
            var elementTop = $(this).offset().top;
            var elementBottom = elementTop + $(this).outerHeight();
            var viewportTop = $(window).scrollTop();
            var viewportBottom = viewportTop + $(window).height();

            if (elementBottom > viewportTop && elementTop < viewportBottom) {
                $(this).addClass('revealed');
            }
        });
    }

    // 初始检查和滚动监听
    revealOnScroll();
    $(window).scroll(revealOnScroll);

    // 为元素添加滚动显示类
    $('.menu-card, .info-card, .reservation-form').addClass('scroll-reveal');

    // 平滑滚动
    $('a[href^="#"]').on('click', function(event) {
        var target = $(this.getAttribute('href'));
        if (target.length) {
            event.preventDefault();
            $('html, body').stop().animate({
                scrollTop: target.offset().top - 100
            }, 1000, 'easeInOutExpo');
        }
    });

    // 数字计数动画
    function animateCounter(element, target) {
        var current = 0;
        var increment = target / 100;
        var timer = setInterval(function() {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            $(element).text(Math.floor(current));
        }, 20);
    }

    // 当统计数字进入视口时开始动画
    var statsAnimated = false;
    $(window).scroll(function() {
        if (!statsAnimated && $('.hero-stats').offset().top < $(window).scrollTop() + $(window).height()) {
            statsAnimated = true;
            animateCounter('.stat-item:eq(1) h3', 1000);
            animateCounter('.stat-item:eq(2) h3', 30);
        }
    });

    // 当日期或人数改变时，更新可用时间
    $('#reservation_date, #party_size').change(function() {
        updateAvailableTimes();
    });

    function updateAvailableTimes() {
        var date = $('#reservation_date').val();
        var partySize = $('#party_size').val();

        if (date && partySize) {
            // 显示加载状态
            $('#reservation_time').html('<option value="">加载中...</option>');

            $.get('/reservation/api/available-times', {
                date: date,
                party_size: partySize
            }, function(data) {
                var timeSelect = $('#reservation_time');
                timeSelect.empty();

                if (data.available_times && data.available_times.length > 0) {
                    timeSelect.append('<option value="">请选择时间</option>');
                    data.available_times.forEach(function(time) {
                        timeSelect.append('<option value="' + time + '">' + time + '</option>');
                    });
                } else {
                    timeSelect.append('<option value="">该日期暂无可用时间</option>');
                }
            }).fail(function() {
                $('#reservation_time').html('<option value="">加载失败，请重试</option>');
            });
        }
    }

    // 表单提交增强
    $('#reservationForm').on('submit', function(e) {
        var submitBtn = $(this).find('button[type="submit"]');
        var originalText = submitBtn.html();

        submitBtn.html('<span class="loading-spinner me-2"></span>提交中...').prop('disabled', true);

        // 如果表单验证失败，恢复按钮状态
        setTimeout(function() {
            if (!document.getElementById('reservationForm').checkValidity()) {
                submitBtn.html(originalText).prop('disabled', false);
            }
        }, 100);
    });

    // 菜品卡片点击效果
    $('.menu-card').on('click', function() {
        $(this).addClass('pulse');
        setTimeout(() => {
            $(this).removeClass('pulse');
        }, 600);
    });

    // 添加视差滚动效果
    $(window).scroll(function() {
        var scrolled = $(this).scrollTop();
        $('.floating-elements').css('transform', 'translateY(' + (scrolled * 0.5) + 'px)');
    });
});

// 页面加载完成后移除加载动画
window.addEventListener('load', function() {
    setTimeout(function() {
        $('.loading-overlay').fadeOut(500);
    }, 500);
});
</script>

<!-- 页面加载动画 -->
<div class="loading-overlay">
    <div class="loading-spinner"></div>
</div>
{% endblock %}
