{% extends "base.html" %}

{% block title %}客户管理 - 牛排餐厅管理系统{% endblock %}
{% block page_title %}客户管理{% endblock %}

{% block content %}
<!-- 客户统计 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-white bg-primary">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">总客户数</h6>
                        <h4>{{ total_customers }}</h4>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-success">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">本月新客户</h6>
                        <h4>{{ new_customers_this_month }}</h4>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-user-plus fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-warning">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">VIP客户</h6>
                        <h4>{{ vip_customers }}</h4>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-crown fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-info">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">今日生日</h6>
                        <h4>{{ today_birthdays or 0 }}</h4>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-birthday-cake fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 功能菜单 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-cogs"></i> 客户管理功能</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('customer.list_customers') }}" class="btn btn-outline-primary btn-lg w-100">
                            <i class="fas fa-list fa-2x d-block mb-2"></i>
                            客户列表
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('customer.add_customer') }}" class="btn btn-outline-success btn-lg w-100">
                            <i class="fas fa-user-plus fa-2x d-block mb-2"></i>
                            添加客户
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('customer.birthday_customers') }}" class="btn btn-outline-warning btn-lg w-100">
                            <i class="fas fa-birthday-cake fa-2x d-block mb-2"></i>
                            生日提醒
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('customer.loyalty_report') }}" class="btn btn-outline-info btn-lg w-100">
                            <i class="fas fa-chart-line fa-2x d-block mb-2"></i>
                            忠诚度报告
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 最近客户活动 -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-star"></i> VIP客户</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>客户</th>
                                <th>消费总额</th>
                                <th>积分</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% if vip_customers_list %}
                                {% for customer in vip_customers_list %}
                                <tr>
                                    <td>
                                        <div>{{ customer.name }}</div>
                                        <small class="text-muted">{{ customer.phone }}</small>
                                    </td>
                                    <td>¥{{ "%.2f"|format(customer.total_spent) }}</td>
                                    <td>
                                        <span class="badge bg-warning">{{ customer.points }}</span>
                                    </td>
                                </tr>
                                {% endfor %}
                            {% else %}
                            <tr>
                                <td colspan="3" class="text-center text-muted">暂无VIP客户</td>
                            </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-birthday-cake"></i> 近期生日客户</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>客户</th>
                                <th>生日</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% if upcoming_birthdays %}
                                {% for customer in upcoming_birthdays %}
                                <tr>
                                    <td>
                                        <div>{{ customer.name }}</div>
                                        <small class="text-muted">{{ customer.phone }}</small>
                                    </td>
                                    <td>{{ customer.birthday.strftime('%m-%d') if customer.birthday else '-' }}</td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary" onclick="sendBirthdayGreeting({{ customer.id }})">
                                            <i class="fas fa-gift"></i>
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                            {% else %}
                            <tr>
                                <td colspan="3" class="text-center text-muted">近期无生日客户</td>
                            </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 客户搜索 -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-search"></i> 快速搜索客户</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <input type="text" class="form-control" id="customerSearch" placeholder="输入客户姓名或手机号进行搜索...">
                    </div>
                    <div class="col-md-4">
                        <button class="btn btn-primary w-100" onclick="searchCustomer()">
                            <i class="fas fa-search"></i> 搜索
                        </button>
                    </div>
                </div>
                <div id="searchResults" class="mt-3"></div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 客户搜索功能
function searchCustomer() {
    const query = $('#customerSearch').val();
    if (query.length < 2) {
        alert('请输入至少2个字符进行搜索');
        return;
    }
    
    $.get('/customer/api/search', {q: query}, function(data) {
        let html = '';
        if (data.length > 0) {
            html = '<div class="table-responsive"><table class="table table-sm"><thead><tr><th>姓名</th><th>手机号</th><th>积分</th><th>消费总额</th><th>操作</th></tr></thead><tbody>';
            data.forEach(function(customer) {
                html += `<tr>
                    <td>${customer.name}</td>
                    <td>${customer.phone}</td>
                    <td><span class="badge bg-warning">${customer.points}</span></td>
                    <td>¥${customer.total_spent.toFixed(2)}</td>
                    <td><a href="/customer/detail/${customer.id}" class="btn btn-sm btn-outline-primary">查看</a></td>
                </tr>`;
            });
            html += '</tbody></table></div>';
        } else {
            html = '<div class="alert alert-info">未找到匹配的客户</div>';
        }
        $('#searchResults').html(html);
    });
}

// 回车搜索
$('#customerSearch').keypress(function(e) {
    if (e.which == 13) {
        searchCustomer();
    }
});

// 发送生日祝福
function sendBirthdayGreeting(customerId) {
    alert('生日祝福功能待实现，客户ID: ' + customerId);
}
</script>
{% endblock %}
