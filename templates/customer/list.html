{% extends "base.html" %}

{% block title %}客户列表 - 牛排餐厅管理系统{% endblock %}
{% block page_title %}客户列表{% endblock %}

{% block content %}
<div class="row mb-3">
    <div class="col-md-6">
        <form method="GET" class="d-flex">
            <input type="text" class="form-control me-2" name="search" value="{{ search }}" placeholder="搜索客户姓名、手机号或邮箱...">
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-search"></i> 搜索
            </button>
        </form>
    </div>
    <div class="col-md-6 text-end">
        <a href="{{ url_for('customer.add_customer') }}" class="btn btn-success">
            <i class="fas fa-user-plus"></i> 添加客户
        </a>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5><i class="fas fa-users"></i> 客户信息</h5>
    </div>
    <div class="card-body">
        {% if customers.items %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>客户编号</th>
                        <th>姓名</th>
                        <th>手机号</th>
                        <th>邮箱</th>
                        <th>生日</th>
                        <th>积分</th>
                        <th>消费总额</th>
                        <th>等级</th>
                        <th>注册时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for customer in customers.items %}
                    <tr>
                        <td><strong>#{{ customer.id }}</strong></td>
                        <td>{{ customer.name }}</td>
                        <td>{{ customer.phone }}</td>
                        <td>{{ customer.email or '-' }}</td>
                        <td>
                            {% if customer.birthday %}
                                {{ customer.birthday.strftime('%m-%d') }}
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            <span class="badge bg-warning">{{ customer.points }}</span>
                        </td>
                        <td>¥{{ "%.2f"|format(customer.total_spent) }}</td>
                        <td>
                            {% if customer.total_spent >= 1000 %}
                                <span class="badge bg-danger">VIP</span>
                            {% elif customer.total_spent >= 100 %}
                                <span class="badge bg-info">常客</span>
                            {% else %}
                                <span class="badge bg-secondary">新客</span>
                            {% endif %}
                        </td>
                        <td>{{ customer.created_at.strftime('%Y-%m-%d') if customer.created_at else '-' }}</td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="{{ url_for('customer.detail', customer_id=customer.id) }}" class="btn btn-outline-primary">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('customer.edit_customer', customer_id=customer.id) }}" class="btn btn-outline-warning">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button class="btn btn-outline-success" onclick="adjustPoints({{ customer.id }}, '{{ customer.name }}', {{ customer.points }})">
                                    <i class="fas fa-coins"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- 分页 -->
        {% if customers.pages > 1 %}
        <nav aria-label="客户分页">
            <ul class="pagination justify-content-center">
                {% if customers.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('customer.list_customers', page=customers.prev_num, search=search) }}">上一页</a>
                </li>
                {% endif %}
                
                {% for page_num in customers.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != customers.page %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('customer.list_customers', page=page_num, search=search) }}">{{ page_num }}</a>
                        </li>
                        {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% endif %}
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">…</span>
                    </li>
                    {% endif %}
                {% endfor %}
                
                {% if customers.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('customer.list_customers', page=customers.next_num, search=search) }}">下一页</a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
        
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-users fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">暂无客户记录</h5>
            {% if search %}
            <p class="text-muted">没有找到匹配"{{ search }}"的客户</p>
            {% else %}
            <p class="text-muted">点击上方按钮添加第一个客户</p>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>

<!-- 积分调整模态框 -->
<div class="modal fade" id="pointsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">调整客户积分</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="pointsForm">
                    <input type="hidden" id="customerId">
                    <div class="mb-3">
                        <label class="form-label">客户姓名</label>
                        <input type="text" class="form-control" id="customerName" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">当前积分</label>
                        <input type="text" class="form-control" id="currentPoints" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">操作类型</label>
                        <select class="form-select" id="action" required>
                            <option value="">请选择操作</option>
                            <option value="add">增加积分</option>
                            <option value="subtract">扣除积分</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">积分数量</label>
                        <input type="number" class="form-control" id="points" min="1" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">操作原因</label>
                        <textarea class="form-control" id="reason" rows="2" placeholder="请说明积分调整原因"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="submitPointsAdjustment()">确认调整</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function adjustPoints(customerId, customerName, currentPoints) {
    $('#customerId').val(customerId);
    $('#customerName').val(customerName);
    $('#currentPoints').val(currentPoints);
    $('#action').val('');
    $('#points').val('');
    $('#reason').val('');
    $('#pointsModal').modal('show');
}

function submitPointsAdjustment() {
    const customerId = $('#customerId').val();
    const action = $('#action').val();
    const points = $('#points').val();
    const reason = $('#reason').val();
    
    if (!action || !points) {
        alert('请填写完整信息');
        return;
    }
    
    $.ajax({
        url: '/customer/points/' + customerId,
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            action: action,
            points: parseInt(points),
            reason: reason
        }),
        success: function(response) {
            if (response.success) {
                alert(response.message);
                $('#pointsModal').modal('hide');
                location.reload();
            } else {
                alert(response.message);
            }
        },
        error: function() {
            alert('操作失败，请重试');
        }
    });
}
</script>
{% endblock %}
