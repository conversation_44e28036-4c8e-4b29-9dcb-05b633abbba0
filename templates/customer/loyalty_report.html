{% extends "base.html" %}

{% block title %}忠诚度报告 - 牛排餐厅管理系统{% endblock %}
{% block page_title %}客户忠诚度报告{% endblock %}

{% block extra_css %}
<style>
    .loyalty-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        margin-bottom: 20px;
        overflow: hidden;
        transition: all 0.3s ease;
    }
    
    .loyalty-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }
    
    .vip-header {
        background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
        color: #333;
        padding: 20px;
    }
    
    .stats-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
    }
    
    .customer-tier {
        display: inline-block;
        padding: 5px 15px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 600;
        text-transform: uppercase;
    }
    
    .tier-vip {
        background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
        color: #333;
    }
    
    .tier-regular {
        background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
        color: white;
    }
    
    .tier-new {
        background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
        color: white;
    }
    
    .customer-item {
        border-bottom: 1px solid #eee;
        padding: 15px 0;
        transition: background 0.3s ease;
    }
    
    .customer-item:hover {
        background: #f8f9fa;
    }
    
    .customer-item:last-child {
        border-bottom: none;
    }
    
    .customer-avatar {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: 1.2rem;
    }
    
    .vip-avatar {
        background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
        color: #333;
    }
    
    .stats-card {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        border-radius: 15px;
        padding: 30px;
        text-align: center;
        margin-bottom: 20px;
        transition: all 0.3s ease;
    }
    
    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }
    
    .stats-number {
        font-size: 2.5rem;
        font-weight: bold;
        color: #333;
        margin-bottom: 10px;
    }
    
    .stats-label {
        font-size: 1.1rem;
        color: #666;
        font-weight: 500;
    }
    
    .chart-container {
        background: white;
        border-radius: 15px;
        padding: 20px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    
    .action-btn {
        border-radius: 25px;
        padding: 10px 25px;
        font-weight: 600;
        transition: all 0.3s ease;
        border: none;
        margin: 5px;
    }
    
    .action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    }
    
    .rank-badge {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        color: white;
        margin-right: 10px;
    }
    
    .rank-1 { background: #ffd700; color: #333; }
    .rank-2 { background: #c0c0c0; color: #333; }
    .rank-3 { background: #cd7f32; color: white; }
    .rank-other { background: #667eea; color: white; }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 客户分层统计 -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="stats-card">
                <div class="stats-number">{{ vip_customers|length }}</div>
                <div class="stats-label">
                    <i class="fas fa-crown me-2"></i>VIP客户
                </div>
                <small class="text-muted">消费≥¥1000</small>
            </div>
        </div>
        <div class="col-md-4">
            <div class="stats-card">
                <div class="stats-number">{{ regular_customers }}</div>
                <div class="stats-label">
                    <i class="fas fa-user me-2"></i>常规客户
                </div>
                <small class="text-muted">消费¥100-999</small>
            </div>
        </div>
        <div class="col-md-4">
            <div class="stats-card">
                <div class="stats-number">{{ new_customers }}</div>
                <div class="stats-label">
                    <i class="fas fa-user-plus me-2"></i>新客户
                </div>
                <small class="text-muted">消费<¥100</small>
            </div>
        </div>
    </div>
    
    <!-- VIP客户排行榜 -->
    <div class="loyalty-card">
        <div class="vip-header">
            <h4 class="mb-0">
                <i class="fas fa-trophy me-2"></i>
                VIP客户排行榜
            </h4>
            <small>按累计消费金额排序的前20名客户</small>
        </div>
        
        <div class="card-body p-4">
            {% if vip_customers %}
                {% for customer in vip_customers %}
                <div class="customer-item">
                    <div class="row align-items-center">
                        <div class="col-md-1">
                            <span class="rank-badge {% if loop.index == 1 %}rank-1{% elif loop.index == 2 %}rank-2{% elif loop.index == 3 %}rank-3{% else %}rank-other{% endif %}">
                                {{ loop.index }}
                            </span>
                        </div>
                        <div class="col-md-1">
                            <div class="customer-avatar vip-avatar">
                                {{ customer.name[0] }}
                            </div>
                        </div>
                        <div class="col-md-3">
                            <h6 class="mb-1">{{ customer.name }}</h6>
                            <small class="text-muted">{{ customer.phone }}</small>
                        </div>
                        <div class="col-md-2">
                            <span class="customer-tier tier-vip">
                                <i class="fas fa-crown me-1"></i>VIP
                            </span>
                        </div>
                        <div class="col-md-2">
                            <small class="text-muted">累计消费</small>
                            <div class="fw-bold text-success h5 mb-0">¥{{ "%.2f"|format(customer.total_spent) }}</div>
                        </div>
                        <div class="col-md-2">
                            {% if customer.birthday %}
                            <small class="text-muted">生日</small>
                            <div>{{ customer.birthday.strftime('%m月%d日') }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-1 text-end">
                            <a href="{{ url_for('customer.detail', customer_id=customer.id) }}" 
                               class="action-btn btn btn-sm" 
                               style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                                <i class="fas fa-eye"></i>
                            </a>
                        </div>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-crown fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">暂无VIP客户</h5>
                    <p class="text-muted">还没有客户达到VIP标准（消费≥¥1000）</p>
                </div>
            {% endif %}
        </div>
    </div>
    
    <!-- 客户增长趋势 -->
    <div class="loyalty-card">
        <div class="stats-header">
            <h4 class="mb-0">
                <i class="fas fa-chart-line me-2"></i>
                客户增长趋势
            </h4>
            <small>按月统计的新客户注册数量</small>
        </div>
        
        <div class="card-body p-4">
            <div class="chart-container">
                <canvas id="customerGrowthChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
    
    <!-- 操作按钮 -->
    <div class="text-center mt-4">
        <a href="{{ url_for('customer.index') }}" 
           class="action-btn btn btn-lg" 
           style="background: linear-gradient(135deg, #636e72 0%, #2d3436 100%); color: white;">
            <i class="fas fa-arrow-left me-2"></i>返回客户管理
        </a>
        <a href="{{ url_for('customer.list_customers') }}" 
           class="action-btn btn btn-lg" 
           style="background: linear-gradient(135deg, #00b894 0%, #00cec9 100%); color: white;">
            <i class="fas fa-users me-2"></i>查看所有客户
        </a>
        <button class="action-btn btn btn-lg" 
                style="background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%); color: white;"
                onclick="exportReport()">
            <i class="fas fa-download me-2"></i>导出报告
        </button>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(document).ready(function() {
    // 客户增长趋势图
    const ctx = document.getElementById('customerGrowthChart').getContext('2d');
    const monthlyData = {{ monthly_stats|tojson }};
    
    const labels = monthlyData.map(item => `${item.year}年${item.month}月`);
    const data = monthlyData.map(item => item.count);
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: '新客户数量',
                data: data,
                borderColor: 'rgb(102, 126, 234)',
                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: '客户增长趋势'
                },
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            }
        }
    });
    
    // 初始化工具提示
    $('[title]').tooltip();
    
    // 添加排行榜动画
    $('.customer-item').each(function(index) {
        $(this).delay(index * 100).fadeIn(500);
    });
});

function exportReport() {
    // 导出报告功能
    alert('导出功能开发中...');
}
</script>
{% endblock %}
