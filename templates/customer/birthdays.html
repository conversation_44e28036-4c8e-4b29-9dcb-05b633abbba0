{% extends "base.html" %}

{% block title %}生日提醒 - 牛排餐厅管理系统{% endblock %}
{% block page_title %}生日提醒{% endblock %}

{% block extra_css %}
<style>
    .birthday-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        margin-bottom: 20px;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .birthday-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }

    .birthday-header {
        background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
        color: white;
        padding: 20px;
    }

    .customer-item {
        border-bottom: 1px solid #eee;
        padding: 15px 0;
        transition: background 0.3s ease;
    }

    .customer-item:hover {
        background: #f8f9fa;
    }

    .customer-item:last-child {
        border-bottom: none;
    }

    .customer-avatar {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: 1.2rem;
    }

    .birthday-badge {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        color: white;
        border-radius: 20px;
        padding: 5px 15px;
        font-size: 0.85rem;
        font-weight: 600;
    }

    .week-day {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #666;
    }

    .empty-state i {
        font-size: 4rem;
        margin-bottom: 20px;
        opacity: 0.5;
    }

    .action-btn {
        border-radius: 25px;
        padding: 8px 20px;
        font-weight: 600;
        transition: all 0.3s ease;
        border: none;
        margin: 5px;
    }

    .action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 今日生日客户 -->
    <div class="birthday-card">
        <div class="birthday-header">
            <h4 class="mb-0">
                <i class="fas fa-birthday-cake me-2"></i>
                今日生日客户 ({{ today_birthdays|length }})
            </h4>
            <small>今天是 {{ today.strftime('%Y年%m月%d日') }}</small>
        </div>

        <div class="card-body p-4">
            {% if today_birthdays %}
                {% for customer in today_birthdays %}
                <div class="customer-item">
                    <div class="row align-items-center">
                        <div class="col-md-1">
                            <div class="customer-avatar">
                                {{ customer.name[0] }}
                            </div>
                        </div>
                        <div class="col-md-3">
                            <h6 class="mb-1">{{ customer.name }}</h6>
                            <small class="text-muted">{{ customer.phone }}</small>
                        </div>
                        <div class="col-md-2">
                            <span class="birthday-badge">
                                <i class="fas fa-gift me-1"></i>
                                今日生日
                            </span>
                        </div>
                        <div class="col-md-2">
                            <small class="text-muted">累计消费</small>
                            <div class="fw-bold text-success">¥{{ "%.2f"|format(customer.total_spent) }}</div>
                        </div>
                        <div class="col-md-2">
                            {% if customer.preferences %}
                            <small class="text-muted">偏好</small>
                            <div class="small">{{ customer.preferences[:30] }}{% if customer.preferences|length > 30 %}...{% endif %}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-2 text-end">
                            <a href="{{ url_for('customer.detail', customer_id=customer.id) }}"
                               class="action-btn btn btn-sm"
                               style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                                <i class="fas fa-eye me-1"></i>查看详情
                            </a>
                        </div>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="empty-state">
                    <i class="fas fa-birthday-cake"></i>
                    <h5>今天没有客户生日</h5>
                    <p>今天没有客户过生日，明天再来看看吧！</p>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- 本周生日客户 -->
    <div class="birthday-card">
        <div class="birthday-header">
            <h4 class="mb-0">
                <i class="fas fa-calendar-week me-2"></i>
                本周生日客户
            </h4>
            <small>本周其他日期的生日客户</small>
        </div>

        <div class="card-body p-4">
            {% if week_birthdays %}
                {% for day_data in week_birthdays %}
                <div class="week-day">
                    <h6 class="mb-3">
                        <i class="fas fa-calendar-day me-2"></i>
                        {{ day_data.date.strftime('%m月%d日') }}
                        <span class="badge bg-primary ms-2">{{ day_data.customers|length }}人</span>
                    </h6>

                    {% for customer in day_data.customers %}
                    <div class="customer-item">
                        <div class="row align-items-center">
                            <div class="col-md-1">
                                <div class="customer-avatar">
                                    {{ customer.name[0] }}
                                </div>
                            </div>
                            <div class="col-md-3">
                                <h6 class="mb-1">{{ customer.name }}</h6>
                                <small class="text-muted">{{ customer.phone }}</small>
                            </div>
                            <div class="col-md-2">
                                <small class="text-muted">生日</small>
                                <div>{{ customer.birthday.strftime('%m月%d日') if customer.birthday }}</div>
                            </div>
                            <div class="col-md-2">
                                <small class="text-muted">累计消费</small>
                                <div class="fw-bold text-success">¥{{ "%.2f"|format(customer.total_spent) }}</div>
                            </div>
                            <div class="col-md-2">
                                {% if customer.preferences %}
                                <small class="text-muted">偏好</small>
                                <div class="small">{{ customer.preferences[:30] }}{% if customer.preferences|length > 30 %}...{% endif %}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-2 text-end">
                                <a href="{{ url_for('customer.detail', customer_id=customer.id) }}"
                                   class="action-btn btn btn-sm"
                                   style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                                    <i class="fas fa-eye me-1"></i>查看详情
                                </a>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% endfor %}
            {% else %}
                <div class="empty-state">
                    <i class="fas fa-calendar-times"></i>
                    <h5>本周没有其他生日客户</h5>
                    <p>本周其他日期没有客户生日</p>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- 操作按钮 -->
    <div class="text-center mt-4">
        <a href="{{ url_for('customer.index') }}"
           class="action-btn btn btn-lg"
           style="background: linear-gradient(135deg, #636e72 0%, #2d3436 100%); color: white;">
            <i class="fas fa-arrow-left me-2"></i>返回客户管理
        </a>
        <a href="{{ url_for('customer.list_customers') }}"
           class="action-btn btn btn-lg"
           style="background: linear-gradient(135deg, #00b894 0%, #00cec9 100%); color: white;">
            <i class="fas fa-users me-2"></i>查看所有客户
        </a>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 初始化工具提示
    $('[title]').tooltip();

    // 添加生日提醒动画
    $('.birthday-badge').each(function(index) {
        $(this).delay(index * 200).fadeIn(500);
    });
});
</script>
{% endblock %}
