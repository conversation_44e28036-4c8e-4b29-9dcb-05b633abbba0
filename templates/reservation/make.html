{% extends "base.html" %}

{% block title %}在线预订 - 牛排餐厅{% endblock %}
{% block page_title %}在线预订{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5><i class="fas fa-calendar-plus"></i> 预订桌位</h5>
            </div>
            <div class="card-body">
                <form method="POST" id="reservationForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">姓名 *</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">手机号 *</label>
                            <input type="tel" class="form-control" id="phone" name="phone" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">邮箱</label>
                            <input type="email" class="form-control" id="email" name="email">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="party_size" class="form-label">用餐人数 *</label>
                            <select class="form-select" id="party_size" name="party_size" required>
                                <option value="">请选择人数</option>
                                {% for i in range(1, 11) %}
                                <option value="{{ i }}">{{ i }}人</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="reservation_date" class="form-label">预订日期 *</label>
                            <input type="date" class="form-control" id="reservation_date" name="reservation_date" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="reservation_time" class="form-label">预订时间 *</label>
                            <select class="form-select" id="reservation_time" name="reservation_time" required>
                                <option value="">请先选择日期和人数</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="special_requests" class="form-label">特殊要求</label>
                        <textarea class="form-control" id="special_requests" name="special_requests" rows="3" placeholder="如有特殊饮食要求或其他需求，请在此说明"></textarea>
                    </div>
                    
                    <div class="text-center">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-check"></i> 确认预订
                        </button>
                        <a href="{{ url_for('reservation.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> 返回
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 设置最小日期为今天
    var today = new Date().toISOString().split('T')[0];
    $('#reservation_date').attr('min', today);
    
    // 当日期或人数改变时，更新可用时间
    $('#reservation_date, #party_size').change(function() {
        updateAvailableTimes();
    });
    
    function updateAvailableTimes() {
        var date = $('#reservation_date').val();
        var partySize = $('#party_size').val();
        
        if (date && partySize) {
            $.get('/reservation/api/available-times', {
                date: date,
                party_size: partySize
            }, function(data) {
                var timeSelect = $('#reservation_time');
                timeSelect.empty();
                
                if (data.available_times && data.available_times.length > 0) {
                    timeSelect.append('<option value="">请选择时间</option>');
                    data.available_times.forEach(function(time) {
                        timeSelect.append('<option value="' + time + '">' + time + '</option>');
                    });
                } else {
                    timeSelect.append('<option value="">该日期暂无可用时间</option>');
                }
            });
        }
    }
});
</script>
{% endblock %}
