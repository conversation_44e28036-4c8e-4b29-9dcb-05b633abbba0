{% extends "base.html" %}

{% block title %}预订管理 - 牛排餐厅管理系统{% endblock %}
{% block page_title %}预订管理{% endblock %}

{% block content %}
<!-- 日期筛选 -->
<div class="row mb-3">
    <div class="col-md-6">
        <form method="GET" class="d-flex">
            <input type="date" class="form-control me-2" name="date" value="{{ current_date }}" onchange="this.form.submit()">
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-search"></i> 筛选
            </button>
        </form>
    </div>
    <div class="col-md-6 text-end">
        <a href="{{ url_for('reservation.make_reservation') }}" class="btn btn-success">
            <i class="fas fa-plus"></i> 新增预订
        </a>
    </div>
</div>

<!-- 预订统计 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-white bg-info">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">今日预订</h6>
                        <h4>{{ reservations.total if reservations.items else 0 }}</h4>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-calendar-check fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-success">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">已确认</h6>
                        <h4>{{ reservations.items | selectattr('status', 'equalto', 'confirmed') | list | length }}</h4>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-warning">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">待确认</h6>
                        <h4>{{ reservations.items | selectattr('status', 'equalto', 'pending') | list | length }}</h4>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-danger">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">已取消</h6>
                        <h4>{{ reservations.items | selectattr('status', 'equalto', 'cancelled') | list | length }}</h4>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-times-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 预订列表 -->
<div class="card">
    <div class="card-header">
        <h5><i class="fas fa-list"></i> 预订列表</h5>
    </div>
    <div class="card-body">
        {% if reservations.items %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>预订号</th>
                        <th>客户信息</th>
                        <th>预订时间</th>
                        <th>人数</th>
                        <th>桌位</th>
                        <th>状态</th>
                        <th>特殊要求</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for reservation in reservations.items %}
                    <tr>
                        <td><strong>#{{ reservation.id }}</strong></td>
                        <td>
                            <div>{{ reservation.customer.name }}</div>
                            <small class="text-muted">{{ reservation.customer.phone }}</small>
                        </td>
                        <td>
                            <div>{{ reservation.reservation_date.strftime('%Y-%m-%d') }}</div>
                            <small class="text-muted">{{ reservation.reservation_time.strftime('%H:%M') }}</small>
                        </td>
                        <td>{{ reservation.party_size }}人</td>
                        <td>
                            <span class="badge bg-primary">{{ reservation.table.number }}</span>
                            <small class="text-muted d-block">{{ reservation.table.capacity }}人座</small>
                        </td>
                        <td>
                            {% if reservation.status == 'confirmed' %}
                                <span class="badge bg-success">已确认</span>
                            {% elif reservation.status == 'cancelled' %}
                                <span class="badge bg-danger">已取消</span>
                            {% elif reservation.status == 'completed' %}
                                <span class="badge bg-info">已完成</span>
                            {% elif reservation.status == 'no_show' %}
                                <span class="badge bg-warning">未到店</span>
                            {% else %}
                                <span class="badge bg-secondary">待确认</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if reservation.special_requests %}
                                <small>{{ reservation.special_requests[:30] }}{% if reservation.special_requests|length > 30 %}...{% endif %}</small>
                            {% else %}
                                <span class="text-muted">无</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                {% if reservation.status == 'confirmed' %}
                                <button class="btn btn-outline-success" onclick="updateStatus({{ reservation.id }}, 'completed')">
                                    <i class="fas fa-check"></i>
                                </button>
                                <button class="btn btn-outline-warning" onclick="updateStatus({{ reservation.id }}, 'no_show')">
                                    <i class="fas fa-user-times"></i>
                                </button>
                                {% endif %}
                                {% if reservation.status in ['confirmed', 'pending'] %}
                                <button class="btn btn-outline-danger" onclick="updateStatus({{ reservation.id }}, 'cancelled')">
                                    <i class="fas fa-times"></i>
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- 分页 -->
        {% if reservations.pages > 1 %}
        <nav aria-label="预订分页">
            <ul class="pagination justify-content-center">
                {% if reservations.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('reservation.manage', page=reservations.prev_num, date=current_date) }}">上一页</a>
                </li>
                {% endif %}
                
                {% for page_num in reservations.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != reservations.page %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('reservation.manage', page=page_num, date=current_date) }}">{{ page_num }}</a>
                        </li>
                        {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% endif %}
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">…</span>
                    </li>
                    {% endif %}
                {% endfor %}
                
                {% if reservations.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('reservation.manage', page=reservations.next_num, date=current_date) }}">下一页</a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
        
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">暂无预订记录</h5>
            <p class="text-muted">选择的日期没有预订记录</p>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function updateStatus(reservationId, newStatus) {
    const statusText = {
        'confirmed': '确认',
        'cancelled': '取消',
        'completed': '完成',
        'no_show': '标记为未到店'
    };
    
    if (confirm(`确定要${statusText[newStatus]}这个预订吗？`)) {
        $.ajax({
            url: '/reservation/update-status/' + reservationId,
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({status: newStatus}),
            success: function(response) {
                if (response.success) {
                    alert(response.message);
                    location.reload();
                } else {
                    alert(response.message);
                }
            },
            error: function() {
                alert('操作失败，请重试');
            }
        });
    }
}
</script>
{% endblock %}
