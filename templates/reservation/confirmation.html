{% extends "base.html" %}

{% block title %}预订确认 - 牛排餐厅{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header bg-success text-white text-center">
                    <h3><i class="fas fa-check-circle"></i> 预订成功！</h3>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <h4>感谢您的预订</h4>
                        <p class="text-muted">我们已收到您的预订信息，请按时到店用餐</p>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-info-circle text-primary"></i> 预订信息</h6>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>预订号：</strong></td>
                                    <td>{{ reservation.id }}</td>
                                </tr>
                                <tr>
                                    <td><strong>客户姓名：</strong></td>
                                    <td>{{ reservation.customer.name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>联系电话：</strong></td>
                                    <td>{{ reservation.customer.phone }}</td>
                                </tr>
                                <tr>
                                    <td><strong>用餐人数：</strong></td>
                                    <td>{{ reservation.party_size }}人</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-calendar-alt text-primary"></i> 时间地点</h6>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>预订日期：</strong></td>
                                    <td>{{ reservation.reservation_date.strftime('%Y年%m月%d日') }}</td>
                                </tr>
                                <tr>
                                    <td><strong>预订时间：</strong></td>
                                    <td>{{ reservation.reservation_time.strftime('%H:%M') }}</td>
                                </tr>
                                <tr>
                                    <td><strong>桌位号：</strong></td>
                                    <td>{{ reservation.table.number }}</td>
                                </tr>
                                <tr>
                                    <td><strong>桌位容量：</strong></td>
                                    <td>{{ reservation.table.capacity }}人座</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    {% if reservation.special_requests %}
                    <div class="mt-3">
                        <h6><i class="fas fa-comment text-primary"></i> 特殊要求</h6>
                        <p class="text-muted">{{ reservation.special_requests }}</p>
                    </div>
                    {% endif %}
                    
                    <div class="alert alert-info mt-4">
                        <h6><i class="fas fa-info-circle"></i> 温馨提示</h6>
                        <ul class="mb-0">
                            <li>请提前10分钟到店，我们将为您安排座位</li>
                            <li>如需取消或修改预订，请提前2小时联系我们</li>
                            <li>餐厅联系电话：010-8888-8888</li>
                            <li>餐厅地址：北京市朝阳区三里屯太古里南区</li>
                        </ul>
                    </div>
                    
                    <div class="text-center mt-4">
                        <a href="{{ url_for('reservation.index') }}" class="btn btn-primary">
                            <i class="fas fa-home"></i> 返回首页
                        </a>
                        <button class="btn btn-outline-primary" onclick="window.print()">
                            <i class="fas fa-print"></i> 打印预订单
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
@media print {
    .btn, .navbar, .sidebar {
        display: none !important;
    }
    .card {
        border: none !important;
        box-shadow: none !important;
    }
}
</style>
{% endblock %}
