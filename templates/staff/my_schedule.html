{% extends "base.html" %}

{% block title %}我的排班 - 牛排餐厅管理系统{% endblock %}
{% block page_title %}我的排班{% endblock %}

{% block content %}
<!-- 个人信息卡片 -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                <i class="fas fa-user-circle fa-4x text-primary mb-3"></i>
                <h5>{{ current_user.name }}</h5>
                <p class="text-muted">{{ current_user.role }}</p>
                <p><i class="fas fa-envelope"></i> {{ current_user.email }}</p>
                {% if current_user.phone %}
                <p><i class="fas fa-phone"></i> {{ current_user.phone }}</p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <div class="row">
            <div class="col-md-6 mb-3">
                <div class="card text-white bg-primary">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">本周排班</h6>
                                <h4>{{ schedules|length }}天</h4>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-calendar-week fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6 mb-3">
                <div class="card text-white bg-success">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">本周工时</h6>
                                <h4>{{ total_hours or 0 }}h</h4>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-clock fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 打卡按钮 -->
        <div class="card">
            <div class="card-body">
                <h6><i class="fas fa-fingerprint"></i> 考勤打卡</h6>
                <div class="row">
                    <div class="col-md-6">
                        <button class="btn btn-success w-100" onclick="clockIn()">
                            <i class="fas fa-sign-in-alt"></i> 上班打卡
                        </button>
                    </div>
                    <div class="col-md-6">
                        <button class="btn btn-warning w-100" onclick="clockOut()">
                            <i class="fas fa-sign-out-alt"></i> 下班打卡
                        </button>
                    </div>
                </div>
                <div id="clockMessage" class="mt-2"></div>
            </div>
        </div>
    </div>
</div>

<!-- 本周排班表 -->
<div class="card">
    <div class="card-header">
        <h5><i class="fas fa-calendar-alt"></i> 本周排班表</h5>
    </div>
    <div class="card-body">
        {% if schedules %}
        <div class="table-responsive">
            <table class="table table-bordered">
                <thead class="table-light">
                    <tr>
                        <th>日期</th>
                        <th>星期</th>
                        <th>班次时间</th>
                        <th>岗位</th>
                        <th>考勤状态</th>
                        <th>工作时长</th>
                    </tr>
                </thead>
                <tbody>
                    {% for schedule in schedules %}
                    {% set attendance = attendance_dict.get(schedule.date) %}
                    <tr>
                        <td>{{ schedule.date.strftime('%m-%d') }}</td>
                        <td>
                            {% set weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'] %}
                            {{ weekdays[schedule.date.weekday()] }}
                        </td>
                        <td>
                            {{ schedule.shift_start.strftime('%H:%M') }} - 
                            {{ schedule.shift_end.strftime('%H:%M') }}
                        </td>
                        <td>
                            <span class="badge bg-info">{{ schedule.position or '通用' }}</span>
                        </td>
                        <td>
                            {% if attendance %}
                                {% if attendance.clock_out %}
                                    <span class="badge bg-success">已完成</span>
                                {% elif attendance.clock_in %}
                                    <span class="badge bg-primary">工作中</span>
                                {% else %}
                                    <span class="badge bg-warning">未打卡</span>
                                {% endif %}
                            {% else %}
                                {% if schedule.date < today %}
                                    <span class="badge bg-danger">缺勤</span>
                                {% elif schedule.date == today %}
                                    <span class="badge bg-secondary">待打卡</span>
                                {% else %}
                                    <span class="badge bg-light text-dark">未开始</span>
                                {% endif %}
                            {% endif %}
                        </td>
                        <td>
                            {% if attendance and attendance.total_hours %}
                                {{ "%.1f"|format(attendance.total_hours) }}小时
                                {% if attendance.overtime_hours and attendance.overtime_hours > 0 %}
                                    <small class="text-warning">(加班{{ "%.1f"|format(attendance.overtime_hours) }}h)</small>
                                {% endif %}
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">本周暂无排班</h5>
            <p class="text-muted">请联系管理员安排排班</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- 考勤历史 -->
<div class="card mt-4">
    <div class="card-header">
        <h6><i class="fas fa-history"></i> 最近考勤记录</h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-sm">
                <thead>
                    <tr>
                        <th>日期</th>
                        <th>上班时间</th>
                        <th>下班时间</th>
                        <th>工作时长</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody>
                    {% if recent_attendances %}
                        {% for attendance in recent_attendances %}
                        <tr>
                            <td>{{ attendance.date.strftime('%m-%d') }}</td>
                            <td>
                                {% if attendance.clock_in %}
                                    {{ attendance.clock_in.strftime('%H:%M') }}
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if attendance.clock_out %}
                                    {{ attendance.clock_out.strftime('%H:%M') }}
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if attendance.total_hours %}
                                    {{ "%.1f"|format(attendance.total_hours) }}h
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if attendance.status == 'present' %}
                                    <span class="badge bg-success">正常</span>
                                {% elif attendance.status == 'late' %}
                                    <span class="badge bg-warning">迟到</span>
                                {% elif attendance.status == 'absent' %}
                                    <span class="badge bg-danger">缺勤</span>
                                {% else %}
                                    <span class="badge bg-secondary">{{ attendance.status }}</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    {% else %}
                    <tr>
                        <td colspan="5" class="text-center text-muted">暂无考勤记录</td>
                    </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 上班打卡
function clockIn() {
    $.ajax({
        url: '/staff/clock-in',
        method: 'POST',
        success: function(response) {
            if (response.success) {
                $('#clockMessage').html('<div class="alert alert-success">' + response.message + '</div>');
                setTimeout(function() {
                    location.reload();
                }, 2000);
            } else {
                $('#clockMessage').html('<div class="alert alert-danger">' + response.message + '</div>');
            }
        },
        error: function() {
            $('#clockMessage').html('<div class="alert alert-danger">打卡失败，请重试</div>');
        }
    });
}

// 下班打卡
function clockOut() {
    $.ajax({
        url: '/staff/clock-out',
        method: 'POST',
        success: function(response) {
            if (response.success) {
                $('#clockMessage').html('<div class="alert alert-success">' + response.message + '</div>');
                setTimeout(function() {
                    location.reload();
                }, 2000);
            } else {
                $('#clockMessage').html('<div class="alert alert-danger">' + response.message + '</div>');
            }
        },
        error: function() {
            $('#clockMessage').html('<div class="alert alert-danger">打卡失败，请重试</div>');
        }
    });
}

// 自动清除消息
setTimeout(function() {
    $('#clockMessage .alert').fadeOut();
}, 5000);
</script>
{% endblock %}
