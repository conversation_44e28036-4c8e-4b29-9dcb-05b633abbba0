{% extends "base.html" %}

{% block title %}员工列表 - 牛排餐厅管理系统{% endblock %}
{% block page_title %}员工列表{% endblock %}

{% block content %}
<div class="row mb-3">
    <div class="col-md-6">
        <input type="text" class="form-control" id="searchInput" placeholder="搜索员工姓名、用户名或手机号...">
    </div>
    <div class="col-md-6 text-end">
        <a href="{{ url_for('auth.register') }}" class="btn btn-success">
            <i class="fas fa-user-plus"></i> 添加员工
        </a>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5><i class="fas fa-users"></i> 员工信息</h5>
    </div>
    <div class="card-body">
        {% if employees %}
        <div class="table-responsive">
            <table class="table table-hover" id="employeeTable">
                <thead>
                    <tr>
                        <th>员工编号</th>
                        <th>姓名</th>
                        <th>用户名</th>
                        <th>邮箱</th>
                        <th>手机号</th>
                        <th>职位</th>
                        <th>入职日期</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for employee in employees %}
                    <tr>
                        <td><strong>#{{ employee.id }}</strong></td>
                        <td>{{ employee.name }}</td>
                        <td>{{ employee.username }}</td>
                        <td>{{ employee.email }}</td>
                        <td>{{ employee.phone or '-' }}</td>
                        <td>
                            {% if employee.role == 'admin' %}
                                <span class="badge bg-danger">管理员</span>
                            {% elif employee.role == 'manager' %}
                                <span class="badge bg-warning">经理</span>
                            {% elif employee.role == 'waiter' %}
                                <span class="badge bg-info">服务员</span>
                            {% elif employee.role == 'chef' %}
                                <span class="badge bg-success">厨师</span>
                            {% else %}
                                <span class="badge bg-secondary">员工</span>
                            {% endif %}
                        </td>
                        <td>{{ employee.hire_date.strftime('%Y-%m-%d') if employee.hire_date else '-' }}</td>
                        <td>
                            {% if employee.is_active %}
                                <span class="badge bg-success">在职</span>
                            {% else %}
                                <span class="badge bg-secondary">离职</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <button class="btn btn-outline-primary" onclick="viewEmployee({{ employee.id }})">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-outline-warning" onclick="editEmployee({{ employee.id }})">
                                    <i class="fas fa-edit"></i>
                                </button>
                                {% if employee.role != 'admin' %}
                                <button class="btn btn-outline-danger" onclick="toggleEmployeeStatus({{ employee.id }}, {{ employee.is_active|lower }})">
                                    {% if employee.is_active %}
                                        <i class="fas fa-user-times"></i>
                                    {% else %}
                                        <i class="fas fa-user-check"></i>
                                    {% endif %}
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-users fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">暂无员工记录</h5>
            <p class="text-muted">点击上方按钮添加第一个员工</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- 员工详情模态框 -->
<div class="modal fade" id="employeeModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">员工详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="employeeDetails">
                <!-- 员工详情内容将通过AJAX加载 -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 搜索功能
$('#searchInput').on('keyup', function() {
    var value = $(this).val().toLowerCase();
    $('#employeeTable tbody tr').filter(function() {
        $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
    });
});

// 查看员工详情
function viewEmployee(employeeId) {
    // 这里可以通过AJAX加载员工详细信息
    $('#employeeDetails').html('<div class="text-center"><i class="fas fa-spinner fa-spin"></i> 加载中...</div>');
    $('#employeeModal').modal('show');
    
    // 模拟加载员工详情
    setTimeout(function() {
        $('#employeeDetails').html(`
            <div class="row">
                <div class="col-md-6">
                    <h6>基本信息</h6>
                    <p><strong>员工编号：</strong> #${employeeId}</p>
                    <p><strong>姓名：</strong> 张三</p>
                    <p><strong>用户名：</strong> zhangsan</p>
                    <p><strong>邮箱：</strong> <EMAIL></p>
                </div>
                <div class="col-md-6">
                    <h6>工作信息</h6>
                    <p><strong>职位：</strong> 服务员</p>
                    <p><strong>入职日期：</strong> 2024-01-01</p>
                    <p><strong>状态：</strong> <span class="badge bg-success">在职</span></p>
                </div>
            </div>
        `);
    }, 1000);
}

// 编辑员工
function editEmployee(employeeId) {
    alert('编辑员工功能待实现，员工ID: ' + employeeId);
}

// 切换员工状态
function toggleEmployeeStatus(employeeId, isActive) {
    const action = isActive ? '停用' : '启用';
    if (confirm(`确定要${action}这个员工吗？`)) {
        // 这里应该发送AJAX请求到后端
        alert(`员工状态已${action}`);
        location.reload();
    }
}
</script>
{% endblock %}
