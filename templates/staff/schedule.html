{% extends "base.html" %}

{% block title %}排班管理 - 牛排餐厅管理系统{% endblock %}
{% block page_title %}排班管理{% endblock %}

{% block content %}
<!-- 操作按钮 -->
<div class="row mb-3">
    <div class="col-md-6">
        <h6>本周排班 ({{ week_dates[0].strftime('%Y-%m-%d') }} 至 {{ week_dates[6].strftime('%Y-%m-%d') }})</h6>
    </div>
    <div class="col-md-6 text-end">
        <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addScheduleModal">
            <i class="fas fa-plus"></i> 添加排班
        </button>
        <button class="btn btn-info" onclick="exportSchedule()">
            <i class="fas fa-download"></i> 导出排班表
        </button>
    </div>
</div>

<!-- 排班表 -->
<div class="card">
    <div class="card-header">
        <h5><i class="fas fa-calendar-week"></i> 本周排班表</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered">
                <thead class="table-light">
                    <tr>
                        <th style="width: 120px;">员工</th>
                        {% for date in week_dates %}
                        <th class="text-center">
                            {{ date.strftime('%m-%d') }}<br>
                            <small>
                                {% set weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'] %}
                                {{ weekdays[date.weekday()] }}
                            </small>
                        </th>
                        {% endfor %}
                    </tr>
                </thead>
                <tbody>
                    {% for employee in employees %}
                    <tr>
                        <td>
                            <div><strong>{{ employee.name }}</strong></div>
                            <small class="text-muted">{{ employee.role }}</small>
                        </td>
                        {% for date in week_dates %}
                        <td class="text-center">
                            {% set date_str = date.strftime('%Y-%m-%d') %}
                            {% set day_schedules = schedule_dict.get(date_str, []) | selectattr('employee_id', 'equalto', employee.id) | list %}
                            {% if day_schedules %}
                                {% for schedule in day_schedules %}
                                <div class="schedule-item mb-1">
                                    <small class="badge bg-primary">
                                        {{ schedule.shift_start.strftime('%H:%M') }}-{{ schedule.shift_end.strftime('%H:%M') }}
                                    </small>
                                    {% if schedule.position %}
                                    <br><small class="text-muted">{{ schedule.position }}</small>
                                    {% endif %}
                                    <br>
                                    <button class="btn btn-sm btn-outline-danger" onclick="deleteSchedule({{ schedule.id }})">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                                {% endfor %}
                            {% else %}
                                <button class="btn btn-sm btn-outline-success" onclick="quickAddSchedule({{ employee.id }}, '{{ date_str }}')">
                                    <i class="fas fa-plus"></i>
                                </button>
                            {% endif %}
                        </td>
                        {% endfor %}
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 排班统计 -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-chart-bar"></i> 本周排班统计</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>员工</th>
                                <th>排班天数</th>
                                <th>预计工时</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for employee in employees %}
                            {% set employee_schedules = schedules | selectattr('employee_id', 'equalto', employee.id) | list %}
                            <tr>
                                <td>{{ employee.name }}</td>
                                <td>{{ employee_schedules | length }}天</td>
                                <td>
                                    {% set total_hours = 0 %}
                                    {% for schedule in employee_schedules %}
                                        {% set hours = ((schedule.shift_end.hour * 60 + schedule.shift_end.minute) - (schedule.shift_start.hour * 60 + schedule.shift_start.minute)) / 60 %}
                                        {% set total_hours = total_hours + hours %}
                                    {% endfor %}
                                    {{ "%.1f"|format(total_hours) }}h
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-info-circle"></i> 排班提醒</h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success"></i> 确保每个班次至少有一名服务员</li>
                    <li><i class="fas fa-check text-success"></i> 厨师班次要覆盖营业时间</li>
                    <li><i class="fas fa-exclamation text-warning"></i> 注意员工工时不要超过法定标准</li>
                    <li><i class="fas fa-info text-info"></i> 周末和节假日需要增加人手</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- 添加排班模态框 -->
<div class="modal fade" id="addScheduleModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">添加排班</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="scheduleForm">
                    <div class="mb-3">
                        <label class="form-label">员工</label>
                        <select class="form-select" id="employee_id" required>
                            <option value="">请选择员工</option>
                            {% for employee in employees %}
                            <option value="{{ employee.id }}">{{ employee.name }} ({{ employee.role }})</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">日期</label>
                        <input type="date" class="form-control" id="date" required>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">开始时间</label>
                            <input type="time" class="form-control" id="shift_start" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">结束时间</label>
                            <input type="time" class="form-control" id="shift_end" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">岗位</label>
                        <select class="form-select" id="position">
                            <option value="">请选择岗位</option>
                            <option value="服务员">服务员</option>
                            <option value="厨师">厨师</option>
                            <option value="收银员">收银员</option>
                            <option value="清洁员">清洁员</option>
                            <option value="管理">管理</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="submitSchedule()">添加排班</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function quickAddSchedule(employeeId, date) {
    $('#employee_id').val(employeeId);
    $('#date').val(date);
    $('#shift_start').val('09:00');
    $('#shift_end').val('17:00');
    $('#position').val('');
    $('#addScheduleModal').modal('show');
}

function submitSchedule() {
    const formData = {
        employee_id: $('#employee_id').val(),
        date: $('#date').val(),
        shift_start: $('#shift_start').val(),
        shift_end: $('#shift_end').val(),
        position: $('#position').val()
    };
    
    if (!formData.employee_id || !formData.date || !formData.shift_start || !formData.shift_end) {
        alert('请填写完整信息');
        return;
    }
    
    $.ajax({
        url: '/staff/add-schedule',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(formData),
        success: function(response) {
            if (response.success) {
                alert(response.message);
                $('#addScheduleModal').modal('hide');
                location.reload();
            } else {
                alert(response.message);
            }
        },
        error: function() {
            alert('添加失败，请重试');
        }
    });
}

function deleteSchedule(scheduleId) {
    if (confirm('确定要删除这个排班吗？')) {
        // 这里应该发送删除请求
        alert('删除排班功能待实现，排班ID: ' + scheduleId);
    }
}

function exportSchedule() {
    alert('导出排班表功能待实现');
}
</script>
{% endblock %}
