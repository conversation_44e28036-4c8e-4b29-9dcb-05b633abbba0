{% extends "base.html" %}

{% block title %}考勤管理 - 牛排餐厅管理系统{% endblock %}
{% block page_title %}考勤管理{% endblock %}

{% block content %}
<!-- 日期筛选 -->
<div class="row mb-3">
    <div class="col-md-6">
        <form method="GET" class="d-flex">
            <input type="date" class="form-control me-2" name="date" value="{{ current_date }}" onchange="this.form.submit()">
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-search"></i> 筛选
            </button>
        </form>
    </div>
    <div class="col-md-6 text-end">
        <button class="btn btn-success" onclick="exportAttendance()">
            <i class="fas fa-download"></i> 导出考勤
        </button>
    </div>
</div>

<!-- 考勤统计 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-white bg-success">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">出勤人数</h6>
                        <h4>{{ attendances|length }}</h4>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-user-check fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-info">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">应出勤</h6>
                        <h4>{{ scheduled_employees|length }}</h4>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-warning">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">迟到人数</h6>
                        <h4>{{ attendances | selectattr('status', 'equalto', 'late') | list | length }}</h4>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-danger">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">缺勤人数</h6>
                        <h4>{{ attendances | selectattr('status', 'equalto', 'absent') | list | length }}</h4>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-user-times fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 考勤列表 -->
<div class="card">
    <div class="card-header">
        <h5><i class="fas fa-clipboard-list"></i> {{ current_date }} 考勤记录</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>员工</th>
                        <th>职位</th>
                        <th>排班时间</th>
                        <th>上班打卡</th>
                        <th>下班打卡</th>
                        <th>工作时长</th>
                        <th>加班时长</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for employee in scheduled_employees %}
                    {% set attendance = attendance_dict.get(employee.id) %}
                    <tr>
                        <td>
                            <div>{{ employee.name }}</div>
                            <small class="text-muted">{{ employee.username }}</small>
                        </td>
                        <td>
                            {% if employee.role == 'manager' %}
                                <span class="badge bg-warning">经理</span>
                            {% elif employee.role == 'waiter' %}
                                <span class="badge bg-info">服务员</span>
                            {% elif employee.role == 'chef' %}
                                <span class="badge bg-success">厨师</span>
                            {% else %}
                                <span class="badge bg-secondary">员工</span>
                            {% endif %}
                        </td>
                        <td>
                            {% set schedule = employee.schedules | selectattr('date', 'equalto', current_date | string | strptime('%Y-%m-%d') | date) | first %}
                            {% if schedule %}
                                {{ schedule.shift_start.strftime('%H:%M') }} - {{ schedule.shift_end.strftime('%H:%M') }}
                            {% else %}
                                <span class="text-muted">无排班</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if attendance and attendance.clock_in %}
                                {{ attendance.clock_in.strftime('%H:%M:%S') }}
                            {% else %}
                                <span class="text-muted">未打卡</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if attendance and attendance.clock_out %}
                                {{ attendance.clock_out.strftime('%H:%M:%S') }}
                            {% else %}
                                <span class="text-muted">未打卡</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if attendance and attendance.total_hours %}
                                {{ "%.1f"|format(attendance.total_hours) }}h
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if attendance and attendance.overtime_hours %}
                                <span class="text-warning">{{ "%.1f"|format(attendance.overtime_hours) }}h</span>
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if attendance %}
                                {% if attendance.status == 'present' %}
                                    <span class="badge bg-success">正常</span>
                                {% elif attendance.status == 'late' %}
                                    <span class="badge bg-warning">迟到</span>
                                {% elif attendance.status == 'early_leave' %}
                                    <span class="badge bg-info">早退</span>
                                {% elif attendance.status == 'absent' %}
                                    <span class="badge bg-danger">缺勤</span>
                                {% else %}
                                    <span class="badge bg-secondary">{{ attendance.status }}</span>
                                {% endif %}
                            {% else %}
                                <span class="badge bg-secondary">未记录</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <button class="btn btn-outline-primary" onclick="viewAttendanceDetail({{ employee.id }})">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-outline-warning" onclick="editAttendance({{ employee.id }})">
                                    <i class="fas fa-edit"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function viewAttendanceDetail(employeeId) {
    alert('查看考勤详情功能待实现，员工ID: ' + employeeId);
}

function editAttendance(employeeId) {
    alert('编辑考勤记录功能待实现，员工ID: ' + employeeId);
}

function exportAttendance() {
    alert('导出考勤报表功能待实现');
}
</script>
{% endblock %}
