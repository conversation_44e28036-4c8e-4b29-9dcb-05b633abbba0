{% extends "base.html" %}

{% block title %}员工管理 - 牛排餐厅管理系统{% endblock %}
{% block page_title %}员工管理{% endblock %}

{% block content %}
<!-- 快速统计 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-white bg-primary">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">总员工数</h6>
                        <h4>{{ total_employees or 0 }}</h4>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-success">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">今日出勤</h6>
                        <h4>{{ today_attendance or 0 }}</h4>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-user-check fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-info">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">本月工时</h6>
                        <h4>{{ monthly_hours or 0 }}h</h4>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-warning">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">加班工时</h6>
                        <h4>{{ overtime_hours or 0 }}h</h4>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-user-clock fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 功能菜单 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-cogs"></i> 管理功能</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('staff.list_employees') }}" class="btn btn-outline-primary btn-lg w-100">
                            <i class="fas fa-users fa-2x d-block mb-2"></i>
                            员工列表
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('auth.register') }}" class="btn btn-outline-success btn-lg w-100">
                            <i class="fas fa-user-plus fa-2x d-block mb-2"></i>
                            添加员工
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('staff.attendance') }}" class="btn btn-outline-info btn-lg w-100">
                            <i class="fas fa-calendar-check fa-2x d-block mb-2"></i>
                            考勤管理
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('staff.schedule') }}" class="btn btn-outline-warning btn-lg w-100">
                            <i class="fas fa-calendar-alt fa-2x d-block mb-2"></i>
                            排班管理
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 今日考勤快览 -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-clock"></i> 今日考勤状态</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>员工</th>
                                <th>上班时间</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% if today_attendances %}
                                {% for attendance in today_attendances %}
                                <tr>
                                    <td>{{ attendance.employee.name }}</td>
                                    <td>
                                        {% if attendance.clock_in %}
                                            {{ attendance.clock_in.strftime('%H:%M') }}
                                        {% else %}
                                            <span class="text-muted">未打卡</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if attendance.clock_out %}
                                            <span class="badge bg-success">已下班</span>
                                        {% elif attendance.clock_in %}
                                            <span class="badge bg-primary">工作中</span>
                                        {% else %}
                                            <span class="badge bg-warning">未到岗</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            {% else %}
                            <tr>
                                <td colspan="3" class="text-center text-muted">暂无考勤记录</td>
                            </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-calendar"></i> 今日排班</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>员工</th>
                                <th>班次</th>
                                <th>岗位</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% if today_schedules %}
                                {% for schedule in today_schedules %}
                                <tr>
                                    <td>{{ schedule.employee.name }}</td>
                                    <td>
                                        {{ schedule.shift_start.strftime('%H:%M') }} - 
                                        {{ schedule.shift_end.strftime('%H:%M') }}
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ schedule.position or '通用' }}</span>
                                    </td>
                                </tr>
                                {% endfor %}
                            {% else %}
                            <tr>
                                <td colspan="3" class="text-center text-muted">暂无排班记录</td>
                            </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 定时刷新考勤数据
setInterval(function() {
    // 这里可以添加AJAX请求来刷新考勤数据
    console.log('刷新考勤数据...');
}, 60000); // 每分钟刷新一次
</script>
{% endblock %}
