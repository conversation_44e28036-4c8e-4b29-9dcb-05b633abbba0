#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增加酒水饮品 - 白酒、香槟、威士忌、无酒精饮品
"""

from app import app, db
from models import MenuItem, Category

def add_beverages_and_spirits():
    with app.app_context():
        # 获取分类
        categories = {cat.name: cat.id for cat in Category.query.all()}
        
        # 定义新增酒水饮品
        new_beverages = [
            # 威士忌系列 (Whisky & Bourbon)
            {'name': '麦卡伦18年单一麦芽威士忌', 'category': '红酒', 'price': 1288, 'description': '苏格兰顶级单一麦芽威士忌，雪莉桶陈酿，口感醇厚复杂', 'prep_time': 2},
            {'name': '山崎12年日本威士忌', 'category': '红酒', 'price': 888, 'description': '日本威士忌代表作，口感细腻，果香与橡木香平衡', 'prep_time': 2},
            {'name': '杰克丹尼田纳西威士忌', 'category': '红酒', 'price': 388, 'description': '美国经典威士忌，炭过滤工艺，口感顺滑甘甜', 'prep_time': 2},
            {'name': '皇家礼炮21年苏格兰威士忌', 'category': '红酒', 'price': 688, 'description': '苏格兰调和威士忌，口感丰富层次，适合庆祝场合', 'prep_time': 2},
            {'name': '蓝牌威士忌', 'category': '红酒', 'price': 1588, 'description': '尊尼获加蓝牌，顶级调和威士忌，口感极致顺滑', 'prep_time': 2},
            
            # 香槟系列 (Champagne)
            {'name': '凯歌香槟', 'category': '红酒', 'price': 588, 'description': '法国香槟区经典香槟，气泡细腻持久，口感清新', 'prep_time': 2},
            {'name': '酩悦香槟', 'category': '红酒', 'price': 688, 'description': '法国顶级香槟品牌，庆祝必备，口感优雅', 'prep_time': 2},
            {'name': '库克香槟', 'category': '红酒', 'price': 888, 'description': '法国香槟之王，奢华品质，适合重要场合', 'prep_time': 2},
            {'name': '路易王妃粉红香槟', 'category': '红酒', 'price': 1288, 'description': '顶级粉红香槟，浪漫色泽，口感精致', 'prep_time': 2},
            {'name': '唐·培里侬香槟', 'category': '红酒', 'price': 1888, 'description': '香槟之王，Dom Pérignon，极致奢华体验', 'prep_time': 2},
            
            # 白酒系列 (Chinese Spirits)
            {'name': '茅台酒', 'category': '红酒', 'price': 2888, 'description': '中国国酒，酱香型白酒代表，适合商务宴请', 'prep_time': 2},
            {'name': '五粮液', 'category': '红酒', 'price': 1888, 'description': '浓香型白酒典型，五种粮食酿造，口感醇厚', 'prep_time': 2},
            {'name': '剑南春', 'category': '红酒', 'price': 888, 'description': '中国名酒，浓香型，口感绵甜爽净', 'prep_time': 2},
            {'name': '水井坊', 'category': '红酒', 'price': 688, 'description': '中国高端白酒，传统工艺，口感清香', 'prep_time': 2},
            {'name': '国窖1573', 'category': '红酒', 'price': 1288, 'description': '泸州老窖高端产品，浓香型，历史悠久', 'prep_time': 2},
            
            # 无酒精饮品系列 (Non-Alcoholic Beverages)
            {'name': '现榨苹果汁', 'category': '咖啡茶饮', 'price': 38, 'description': '新鲜苹果现榨，富含维生素，清甜爽口', 'prep_time': 3},
            {'name': '现榨胡萝卜汁', 'category': '咖啡茶饮', 'price': 42, 'description': '新鲜胡萝卜现榨，富含胡萝卜素，营养健康', 'prep_time': 3},
            {'name': '蔓越莓汁', 'category': '咖啡茶饮', 'price': 45, 'description': '进口蔓越莓汁，酸甜可口，富含抗氧化物', 'prep_time': 2},
            {'name': '柠檬蜂蜜茶', 'category': '咖啡茶饮', 'price': 35, 'description': '新鲜柠檬配天然蜂蜜，清香解腻', 'prep_time': 5},
            {'name': '薄荷柠檬水', 'category': '咖啡茶饮', 'price': 32, 'description': '新鲜薄荷叶配柠檬，清新怡人', 'prep_time': 3},
            {'name': '气泡水', 'category': '咖啡茶饮', 'price': 25, 'description': '进口天然气泡水，清爽解腻，配餐佳选', 'prep_time': 1},
            {'name': '可口可乐', 'category': '咖啡茶饮', 'price': 18, 'description': '经典碳酸饮料，冰镇享用', 'prep_time': 1},
            {'name': '雪碧', 'category': '咖啡茶饮', 'price': 18, 'description': '柠檬味碳酸饮料，清爽怡人', 'prep_time': 1},
            {'name': '橙汁汽水', 'category': '咖啡茶饮', 'price': 22, 'description': '天然橙汁配气泡水，果香浓郁', 'prep_time': 2},
            {'name': '冰红茶', 'category': '咖啡茶饮', 'price': 20, 'description': '冰镇红茶，清香甘甜，夏日首选', 'prep_time': 2},
            
            # 精品茶饮系列 (Premium Teas)
            {'name': '龙井绿茶', 'category': '咖啡茶饮', 'price': 58, 'description': '西湖龙井，清香淡雅，回甘悠长', 'prep_time': 5},
            {'name': '铁观音乌龙茶', 'category': '咖啡茶饮', 'price': 68, 'description': '福建安溪铁观音，兰花香，甘醇回甜', 'prep_time': 5},
            {'name': '普洱茶', 'category': '咖啡茶饮', 'price': 78, 'description': '云南普洱熟茶，醇厚甘滑，养胃佳品', 'prep_time': 8},
            {'name': '大红袍', 'category': '咖啡茶饮', 'price': 88, 'description': '武夷山大红袍，岩韵明显，香气持久', 'prep_time': 6},
            {'name': '白毫银针', 'category': '咖啡茶饮', 'price': 98, 'description': '福建白茶，清淡甘甜，具有药用价值', 'prep_time': 6},
            
            # 精品咖啡系列 (Specialty Coffee)
            {'name': '美式咖啡', 'category': '咖啡茶饮', 'price': 32, 'description': '经典美式咖啡，浓郁香醇，可选冰/热', 'prep_time': 3},
            {'name': '摩卡咖啡', 'category': '咖啡茶饮', 'price': 45, 'description': '咖啡配巧克力和奶泡，香甜浓郁', 'prep_time': 5},
            {'name': '焦糖玛奇朵', 'category': '咖啡茶饮', 'price': 48, 'description': '意式浓缩配焦糖和奶泡，层次丰富', 'prep_time': 6},
            {'name': '爱尔兰咖啡', 'category': '咖啡茶饮', 'price': 68, 'description': '咖啡配爱尔兰威士忌和奶油，温暖醇香', 'prep_time': 8},
            {'name': '手冲单品咖啡', 'category': '咖啡茶饮', 'price': 78, 'description': '精选咖啡豆手工冲泡，口感纯净', 'prep_time': 10},
            
            # 特色饮品系列 (Signature Drinks)
            {'name': '维珍莫吉托', 'category': '咖啡茶饮', 'price': 58, 'description': '无酒精版莫吉托，薄荷柠檬气泡水，清新怡人', 'prep_time': 5},
            {'name': '蓝莓气泡饮', 'category': '咖啡茶饮', 'price': 52, 'description': '新鲜蓝莓配气泡水，颜值与口感并存', 'prep_time': 4},
            {'name': '草莓奶昔', 'category': '咖啡茶饮', 'price': 48, 'description': '新鲜草莓配香草冰淇淋，香甜浓郁', 'prep_time': 5},
            {'name': '芒果椰汁', 'category': '咖啡茶饮', 'price': 45, 'description': '热带芒果配椰汁，浓郁果香', 'prep_time': 3},
            {'name': '柠檬蜂蜜气泡水', 'category': '咖啡茶饮', 'price': 38, 'description': '柠檬蜂蜜配天然气泡水，清香解腻', 'prep_time': 3},
        ]
        
        # 添加新饮品
        added_count = 0
        for item_data in new_beverages:
            if item_data['category'] in categories:
                menu_item = MenuItem(
                    name=item_data['name'],
                    category_id=categories[item_data['category']],
                    price=item_data['price'],
                    description=item_data['description'],
                    preparation_time=item_data['prep_time']
                )
                db.session.add(menu_item)
                added_count += 1
        
        db.session.commit()
        
        print(f"成功添加 {added_count} 种酒水饮品！")
        
        # 统计各类型饮品数量和价格范围
        beverage_stats = {
            '威士忌': {'count': 5, 'price_range': '¥388-1588'},
            '香槟': {'count': 5, 'price_range': '¥588-1888'},
            '白酒': {'count': 5, 'price_range': '¥688-2888'},
            '无酒精饮品': {'count': 10, 'price_range': '¥18-45'},
            '精品茶饮': {'count': 5, 'price_range': '¥58-98'},
            '精品咖啡': {'count': 5, 'price_range': '¥32-78'},
            '特色饮品': {'count': 5, 'price_range': '¥38-58'}
        }
        
        print("\n酒水饮品分类统计:")
        for category, stats in beverage_stats.items():
            print(f"- {category}: {stats['count']}种, 价格区间: {stats['price_range']}")
        
        print(f"\n总新增饮品: {sum(stats['count'] for stats in beverage_stats.values())} 种")
        print("饮品策略: 覆盖高中低端，满足不同消费需求和场合")

if __name__ == '__main__':
    add_beverages_and_spirits()
