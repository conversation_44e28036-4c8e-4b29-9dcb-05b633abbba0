<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Find and explore upcoming sports events, tournaments, and championships from around the world.">
    <meta name="keywords" content="sports, events, tournaments, championships, football, basketball, tennis, swimming, athletics, golf, formula 1">
    <title>欧洲冠军联赛决赛2025 - 体育赛事</title>
    <!-- Favicon -->
    <link rel="icon" href="/static/favicon.ico?q=1747998751" type="image/x-icon">
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/static/css/main.css?q=1747309699">
    <link rel="stylesheet" href="/static/css/custom.css?q=1747728358">
    <!-- Integrated Featured Events CSS -->
    <link rel="stylesheet" href="/static/css/integrated_featured_events.css?q=1748252361">
    
<!-- Leaflet CSS for interactive maps -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin="/">

<style>
    /* 赛事详情图片样式 */
    .event-detail-image {
        transition: transform 0.3s ease;
    }

    .event-image:hover .event-detail-image {
        transform: scale(1.02);
    }

    /* 相关赛事卡片样式 */
    .hover-shadow {
        transition: all 0.3s ease;
        border-radius: 0.75rem;
        overflow: hidden;
        border: none;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .hover-shadow:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 20px rgba(0,0,0,0.15);
    }

    .hover-shadow:hover .related-event-image {
        transform: scale(1.05);
    }

    .related-event-image {
        transition: transform 0.3s ease;
    }

    /* 强制显示图片 */
    .event-detail-image, .related-event-image {
        display: block !important;
        opacity: 1 !important;
        visibility: visible !important;
    }

    /* 徽章样式优化 */
    .badge {
        font-weight: 500;
        letter-spacing: 0.5px;
    }

    /* 标签页内容样式 */
    .tab-content {
        background: white;
        border-radius: 0 0 1rem 1rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .nav-tabs {
        border-bottom: none;
    }

    .nav-tabs .nav-link {
        border: none;
        border-radius: 1rem 1rem 0 0;
        margin-right: 0.5rem;
        background: #f8f9fa;
        color: #6c757d;
        transition: all 0.3s ease;
    }

    .nav-tabs .nav-link.active {
        background: white;
        color: #495057;
        box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
    }
</style>

    <!-- Open Graph tags for social sharing -->
    <meta property="og:title" content="UEFA Champions League Final 2025 - Sports Events">
    <meta property="og:description" content="The UEFA Champions League Final is the most prestigious club football match in Europe, featuring the two best teams from the tournament. The match determines th">
    <meta property="og:image" content="/static/images/events/uefa_champions_league_final_2025.jpg">
    <meta property="og:url" content="http://localhost/events/2">
    <meta property="og:type" content="website">
</head>
<body>
    <!-- Skip to content link for accessibility -->
    <a href="#main-content" class="skip-link">Skip to content</a>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-trophy"></i> 体育赛事
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link " href="/" aria-current="">
                            <i class="fas fa-home me-1"></i> 首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link " href="/events" aria-current="">
                            <i class="fas fa-calendar-alt me-1"></i> 赛事
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="categoriesDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-list me-1"></i> 分类
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="categoriesDropdown">
                            <li><a class="dropdown-item" href="/events?category=1">足球</a></li>
                            <li><a class="dropdown-item" href="/events?category=2">篮球</a></li>
                            <li><a class="dropdown-item" href="/events?category=3">网球</a></li>
                            <li><a class="dropdown-item" href="/events?category=4">游泳</a></li>
                            <li><a class="dropdown-item" href="/events?category=5">田径</a></li>
                            <li><a class="dropdown-item" href="/events?category=6">高尔夫</a></li>
                            <li><a class="dropdown-item" href="/events?category=7">一级方程式</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Coming Soon">
                            <i class="fas fa-newspaper me-1"></i> News
                        </a>
                    </li>
                </ul>
                <div class="d-flex align-items-center">
                    <div class="search-container position-relative me-3">
                        <form class="search-form" action="/events" method="get">
                            <div class="input-group">
                                <input class="form-control search-autocomplete" type="search" name="q" placeholder="搜索赛事" aria-label="搜索赛事" autocomplete="off">
                                <button class="btn btn-primary" type="submit">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </form>
                        <div class="search-results position-absolute w-100 bg-white shadow-lg rounded-bottom p-2" style="display: none; z-index: 1000;"></div>
                    </div>
                    <ul class="navbar-nav">
                        <li class="nav-item">
                            <a class="nav-link" href="/login" data-bs-toggle="tooltip" data-bs-placement="bottom" title="登录">
                                <i class="fas fa-sign-in-alt"></i>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/register" data-bs-toggle="tooltip" data-bs-placement="bottom" title="注册">
                                <i class="fas fa-user-plus"></i>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Language Switcher -->
                <div class="navbar-nav ms-auto">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="languageDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-globe me-1"></i>
                            中文
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="languageDropdown">
                            <li>
                                <a class="dropdown-item " href="/set_language/en">
                                    <i class="fas fa-flag-usa me-2"></i> English
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item active" href="/set_language/zh">
                                    <i class="fas fa-flag me-2" style="color: #de2910;"></i> 中文
                                </a>
                            </li>
                        </ul>
                    </li>
                </div>
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    
        
    

    <!-- Main Content -->
    <main id="main-content" class="py-4">
        
<div class="container">
    <!-- Breadcrumb Navigation -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="/">首页</a></li>
            <li class="breadcrumb-item"><a href="/events">赛事</a></li>
            <li class="breadcrumb-item"><a href="/events?category=1">足球</a></li>
            <li class="breadcrumb-item active" aria-current="page">欧洲冠军联赛决赛2025</li>
        </ol>
    </nav>

    <!-- Event Header -->
    <div class="row mb-5 event-header">
        <div class="col-lg-8 col-md-7 order-md-1 order-2">
            <div class="d-flex align-items-center mb-3">
                <span class="badge bg-primary me-2">足球</span>
                
                    <span class="badge bg-warning me-2">精选</span>
                
                <span class="text-muted">1 天</span>
            </div>
            <h1 class="mb-3">欧洲冠军联赛决赛2025</h1>
            <div class="event-meta mb-4">
                <div class="event-meta-item">
                    <i class="fas fa-map-marker-alt"></i>
                    <span>德国慕尼黑</span>
                </div>
                <div class="event-meta-item">
                    <i class="fas fa-calendar-alt"></i>
                    <span>2025年五月31日</span>
                </div>
            </div>
            <div class="countdown mb-4" data-event-date="2025-05-31">
                <!-- Countdown will be inserted by JavaScript -->
            </div>
            <p class="lead">欧洲冠军联赛决赛是欧洲最具声望的俱乐部足球比赛，由锦标赛中的两支最佳球队参加。这场比赛决定欧洲冠军联赛的冠军，这是欧洲足球的顶级俱乐部赛事。</p>
            <div class="d-flex mt-4">
                <a href="#schedule" class="btn btn-primary me-2">
                    <i class="fas fa-calendar-alt me-1"></i> 查看赛程
                </a>
                <div class="share-buttons ms-auto">
                    <a href="#" class="share-button share-facebook" aria-label="Share on Facebook">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <a href="#" class="share-button share-twitter" aria-label="Share on Twitter">
                        <i class="fab fa-twitter"></i>
                    </a>
                    <a href="#" class="share-button share-email" aria-label="Share via Email">
                        <i class="fas fa-envelope"></i>
                    </a>
                </div>
            </div>
        </div>
        <div class="col-lg-4 col-md-5 order-md-2 order-1 mb-md-0 mb-4">
            <div class="event-image shadow-lg" style="border-radius: 1rem; overflow: hidden; position: relative;">
                <img src="/static/images/homepage/featured/champions_league_featured.jpg"
                     class="img-fluid event-detail-image"
                     alt="UEFA Champions League Final 2025"
                     style="width: 100%; height: 380px; object-fit: cover; display: block !important; opacity: 1 !important; visibility: visible !important; border-radius: 1rem; transition: transform 0.3s ease;"
                     onerror="console.log('Event detail image failed:', this.src); this.src='/static/images/events/uefa_champions_league_final_2025.jpg'; this.style.background='linear-gradient(135deg, #f8f9fa, #e9ecef)'; this.style.display='block';"
                     onload="console.log('Event detail image loaded:', this.src); this.style.display='block'; this.style.opacity='1';"
                     loading="lazy">
                
                    <div class="position-absolute top-0 end-0 m-3">
                        <span class="badge bg-warning text-dark px-3 py-2" style="border-radius: 1rem; backdrop-filter: blur(10px);">
                            <i class="fas fa-star me-1"></i>精选
                        </span>
                    </div>
                
                <div class="position-absolute bottom-0 start-0 m-3">
                    <span class="badge bg-primary px-3 py-2" style="border-radius: 1rem; backdrop-filter: blur(10px); background: rgba(13, 110, 253, 0.9);">
                        <i class="fas fa-tag me-1"></i>足球
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Tabs Navigation -->
    <ul class="nav nav-tabs mb-4" id="eventTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="info-tab" data-bs-toggle="tab" data-bs-target="#info" type="button" role="tab" aria-controls="info" aria-selected="true">
                <i class="fas fa-info-circle me-2"></i>概览
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="schedule-tab" data-bs-toggle="tab" data-bs-target="#schedule" type="button" role="tab" aria-controls="schedule" aria-selected="false">
                <i class="fas fa-calendar-alt me-2"></i>赛程
                
                    <span class="badge rounded-pill bg-primary ms-1">3</span>
                
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="venue-tab" data-bs-toggle="tab" data-bs-target="#venue" type="button" role="tab" aria-controls="venue" aria-selected="false">
                <i class="fas fa-map-marked-alt me-2"></i>场馆
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="history-tab" data-bs-toggle="tab" data-bs-target="#history" type="button" role="tab" aria-controls="history" aria-selected="false">
                <i class="fas fa-history me-2"></i>历史
                
                    <span class="badge rounded-pill bg-primary ms-1">4</span>
                
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="comments-tab" data-bs-toggle="tab" data-bs-target="#comments" type="button" role="tab" aria-controls="comments" aria-selected="false">
                <i class="fas fa-comments me-2"></i>Comments
                
            </button>
        </li>
    </ul>

    <!-- Tabs Content -->
    <div class="tab-content" id="eventTabsContent">
        <!-- Basic Information Tab -->
        <div class="tab-pane fade show active" id="info" role="tabpanel" aria-labelledby="info-tab">
            <div class="row">
                <div class="col-lg-8">
                    <div class="card shadow-sm mb-4">
                        <div class="card-body">
                            <h3 class="card-title">关于赛事</h3>
                            <div class="card-text">
                                欧洲冠军联赛决赛是欧洲最具声望的俱乐部足球比赛，由锦标赛中的两支最佳球队参加。这场比赛决定欧洲冠军联赛的冠军，这是欧洲足球的顶级俱乐部赛事。
                            </div>

                            
                            <h4 class="mt-4">亮点</h4>
                            <div class="row mt-3">
                                
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="bg-primary rounded-circle p-3 me-3">
                                            <i class="fas fa-star text-white"></i>
                                        </div>
                                        <div>
                                            <h5 class="mb-1">精英赛事</h5>
                                            <p class="mb-0 text-muted">来自欧洲联赛的顶级32支俱乐部</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="bg-primary rounded-circle p-3 me-3">
                                            <i class="fas fa-music text-white"></i>
                                        </div>
                                        <div>
                                            <h5 class="mb-1">标志性主题曲</h5>
                                            <p class="mb-0 text-muted">著名的欧洲冠军联赛主题曲</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="bg-primary rounded-circle p-3 me-3">
                                            <i class="fas fa-crown text-white"></i>
                                        </div>
                                        <div>
                                            <h5 class="mb-1">欧洲荣耀</h5>
                                            <p class="mb-0 text-muted">欧洲最具声望的俱乐部赛事</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="bg-primary rounded-circle p-3 me-3">
                                            <i class="fas fa-money-bill-wave text-white"></i>
                                        </div>
                                        <div>
                                            <h5 class="mb-1">奖金</h5>
                                            <p class="mb-0 text-muted">总奖金超过20亿欧元</p>
                                        </div>
                                    </div>
                                </div>
                                
                            </div>
                            
                        </div>
                    </div>

                    <div class="card shadow-sm">
                        <div class="card-body">
                            <h4 class="card-title">相关赛事</h4>
                            <div class="row g-3 mt-2">
                                
                                    
                                    <div class="col-md-4">
                                        <div class="card h-100 hover-shadow">
                                            <img src="/static/images/homepage/featured/fifa_world_cup_featured.jpg"
                                                 class="card-img-top related-event-image"
                                                 alt="FIFA World Cup 2026"
                                                 style="height: 140px; object-fit: cover; display: block !important; opacity: 1 !important; visibility: visible !important; border-radius: 0.5rem 0.5rem 0 0; transition: transform 0.3s ease;"
                                                 onerror="console.log('Related event image failed:', this.src); this.src='/static/images/events/fifa_world_cup_2026.jpg'; this.style.background='#f8f9fa'; this.style.display='block';"
                                                 onload="console.log('Related event image loaded:', this.src); this.style.display='block'; this.style.opacity='1';">
                                            <div class="card-body p-3">
                                                <h6 class="card-title text-truncate">FIFA世界杯2026</h6>
                                                <div class="d-flex align-items-center">
                                                    <small class="text-muted">2026年六月10日</small>
                                                    <a href="/events/1" class="btn btn-sm btn-outline-primary ms-auto">查看</a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-4">
                                        <div class="card h-100 hover-shadow">
                                            <img src="/static/images/events/premier_league_2025_26.jpg"
                                                 class="card-img-top related-event-image"
                                                 alt="Premier League 2025-26"
                                                 style="height: 140px; object-fit: cover; display: block !important; opacity: 1 !important; visibility: visible !important; border-radius: 0.5rem 0.5rem 0 0; transition: transform 0.3s ease;"
                                                 onerror="console.log('Related event image failed:', this.src); this.src='/static/images/events/premier_league_2025_26.jpg'; this.style.background='#f8f9fa'; this.style.display='block';"
                                                 onload="console.log('Related event image loaded:', this.src); this.style.display='block'; this.style.opacity='1';">
                                            <div class="card-body p-3">
                                                <h6 class="card-title text-truncate">英超联赛2025-26</h6>
                                                <div class="d-flex align-items-center">
                                                    <small class="text-muted">2025年八月15日</small>
                                                    <a href="/events/3" class="btn btn-sm btn-outline-primary ms-auto">查看</a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>赛事详情</h5>
                        </div>
                        <div class="card-body">
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item d-flex align-items-center py-3">
                                    <i class="fas fa-map-marker-alt text-primary me-3"></i>
                                    <div>
                                        <strong>地点</strong>
                                        <div>德国慕尼黑</div>
                                    </div>
                                </li>
                                <li class="list-group-item d-flex align-items-center py-3">
                                    <i class="fas fa-calendar-alt text-primary me-3"></i>
                                    <div>
                                        <strong>日期</strong>
                                        <div>May 31, 2025</div>
                                    </div>
                                </li>
                                <li class="list-group-item d-flex align-items-center py-3">
                                    <i class="fas fa-calendar-check text-primary me-3"></i>
                                    <div>
                                        <strong>日期</strong>
                                        <div>May 31, 2025</div>
                                    </div>
                                </li>
                                <li class="list-group-item d-flex align-items-center py-3">
                                    <i class="fas fa-clock text-primary me-3"></i>
                                    <div>
                                        <strong>持续时间</strong>
                                        <div>1 天</div>
                                    </div>
                                </li>
                                <li class="list-group-item d-flex align-items-center py-3">
                                    <i class="fas fa-tag text-primary me-3"></i>
                                    <div>
                                        <strong>类别</strong>
                                        <div>足球</div>
                                    </div>
                                </li>
                            </ul>
                        </div>
                        <div class="card-footer bg-light">
                            <a href="#" class="btn btn-primary w-100">
                                <i class="fas fa-ticket-alt me-2"></i>获取门票
                            </a>
                        </div>
                    </div>

                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="fas fa-share-alt me-2"></i>分享此赛事</h5>
                        </div>
                        <div class="card-body">
                            <p class="text-muted mb-3">与您的朋友和家人分享此赛事</p>
                            <div class="d-flex justify-content-between">
                                <a href="#" class="btn btn-outline-primary flex-grow-1 me-2">
                                    <i class="fab fa-facebook-f me-2"></i>Facebook
                                </a>
                                <a href="#" class="btn btn-outline-info flex-grow-1 me-2">
                                    <i class="fab fa-twitter me-2"></i>Twitter
                                </a>
                                <a href="#" class="btn btn-outline-success flex-grow-1">
                                    <i class="fas fa-envelope me-2"></i>邮箱
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="card shadow-sm">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="fas fa-bell me-2"></i>提醒</h5>
                        </div>
                        <div class="card-body">
                            <p>不要错过这个赛事！设置提醒以在开始前收到通知。</p>
                            <form>
                                <div class="mb-3">
                                    <label for="reminderEmail" class="form-label">邮箱</label>
                                    <input type="email" class="form-control" id="reminderEmail" placeholder="<EMAIL>">
                                </div>
                                <div class="mb-3">
                                    <label for="reminderTime" class="form-label">提醒我</label>
                                    <select class="form-select" id="reminderTime">
                                        <option value="1">1 天前</option>
                                        <option value="3">3 天前</option>
                                        <option value="7">1 周前</option>
                                        <option value="14">2 周前</option>
                                    </select>
                                </div>
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-bell me-2"></i>设置提醒
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Schedule Tab -->
        <div class="tab-pane fade" id="schedule" role="tabpanel" aria-labelledby="schedule-tab">
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <h3 class="mb-0">event_schedule</h3>
                        <div>
                            <button class="btn btn-outline-primary btn-sm me-2" id="calendarViewBtn">
                                <i class="fas fa-calendar-alt me-1"></i> calendar_view
                            </button>
                            <button class="btn btn-primary btn-sm" id="listViewBtn">
                                <i class="fas fa-list me-1"></i> list_view
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            
                <!-- Timeline View -->
                <div class="row" id="listView">
                    <div class="col-12">
                        <div class="card shadow-sm">
                            <div class="card-body p-0">
                                <div class="timeline">
                                    
                                        <div class="timeline-item">
                                            <div class="timeline-date bg-primary text-white rounded p-2 text-center">
                                                <div class="fw-bold">四月29日</div>
                                                <div>2025</div>
                                                <div>20:00</div>
                                            </div>
                                            <div class="timeline-content card shadow-sm">
                                                <div class="card-body">
                                                    <h5 class="card-title">Semi-final First Leg</h5>
                                                    <h6 class="card-subtitle mb-2 text-muted">
                                                        <i class="fas fa-map-marker-alt me-1"></i> Various Venues
                                                    </h6>
                                                    <p class="card-text">First leg of the semi-finals</p>
                                                    <a href="#" class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-bell me-1"></i> 设置提醒
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    
                                        <div class="timeline-item">
                                            <div class="timeline-date bg-primary text-white rounded p-2 text-center">
                                                <div class="fw-bold">五月6日</div>
                                                <div>2025</div>
                                                <div>20:00</div>
                                            </div>
                                            <div class="timeline-content card shadow-sm">
                                                <div class="card-body">
                                                    <h5 class="card-title">Semi-final Second Leg</h5>
                                                    <h6 class="card-subtitle mb-2 text-muted">
                                                        <i class="fas fa-map-marker-alt me-1"></i> Various Venues
                                                    </h6>
                                                    <p class="card-text">Second leg of the semi-finals</p>
                                                    <a href="#" class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-bell me-1"></i> 设置提醒
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    
                                        <div class="timeline-item">
                                            <div class="timeline-date bg-primary text-white rounded p-2 text-center">
                                                <div class="fw-bold">五月31日</div>
                                                <div>2025</div>
                                                <div>20:00</div>
                                            </div>
                                            <div class="timeline-content card shadow-sm">
                                                <div class="card-body">
                                                    <h5 class="card-title">决赛</h5>
                                                    <h6 class="card-subtitle mb-2 text-muted">
                                                        <i class="fas fa-map-marker-alt me-1"></i> Allianz Arena, Munich
                                                    </h6>
                                                    <p class="card-text">UEFA Champions League Final</p>
                                                    <a href="#" class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-bell me-1"></i> 设置提醒
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Calendar View (hidden by default) -->
                <div class="row" id="calendarView" style="display: none;">
                    <div class="col-12">
                        <div class="card shadow-sm">
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead class="bg-primary text-white">
                                            <tr>
                                                <th>date_time</th>
                                                <th>title</th>
                                                <th>地点</th>
                                                <th>描述</th>
                                                <th>actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            
                                                <tr>
                                                    <td class="align-middle">
                                                        <div class="fw-bold">2025年四月29日</div>
                                                        <div>20:00</div>
                                                    </td>
                                                    <td class="align-middle fw-bold">Semi-final First Leg</td>
                                                    <td class="align-middle">
                                                        <i class="fas fa-map-marker-alt text-primary me-1"></i> Various Venues
                                                    </td>
                                                    <td class="align-middle">First leg of the semi-finals</td>
                                                    <td class="align-middle">
                                                        <div class="btn-group btn-group-sm">
                                                            <a href="#" class="btn btn-outline-primary" data-bs-toggle="tooltip" title="add_to_calendar">
                                                                <i class="fas fa-calendar-plus"></i>
                                                            </a>
                                                            <a href="#" class="btn btn-outline-primary" data-bs-toggle="tooltip" title="设置提醒">
                                                                <i class="fas fa-bell"></i>
                                                            </a>
                                                        </div>
                                                    </td>
                                                </tr>
                                            
                                                <tr>
                                                    <td class="align-middle">
                                                        <div class="fw-bold">2025年五月6日</div>
                                                        <div>20:00</div>
                                                    </td>
                                                    <td class="align-middle fw-bold">Semi-final Second Leg</td>
                                                    <td class="align-middle">
                                                        <i class="fas fa-map-marker-alt text-primary me-1"></i> Various Venues
                                                    </td>
                                                    <td class="align-middle">Second leg of the semi-finals</td>
                                                    <td class="align-middle">
                                                        <div class="btn-group btn-group-sm">
                                                            <a href="#" class="btn btn-outline-primary" data-bs-toggle="tooltip" title="add_to_calendar">
                                                                <i class="fas fa-calendar-plus"></i>
                                                            </a>
                                                            <a href="#" class="btn btn-outline-primary" data-bs-toggle="tooltip" title="设置提醒">
                                                                <i class="fas fa-bell"></i>
                                                            </a>
                                                        </div>
                                                    </td>
                                                </tr>
                                            
                                                <tr>
                                                    <td class="align-middle">
                                                        <div class="fw-bold">2025年五月31日</div>
                                                        <div>20:00</div>
                                                    </td>
                                                    <td class="align-middle fw-bold">决赛</td>
                                                    <td class="align-middle">
                                                        <i class="fas fa-map-marker-alt text-primary me-1"></i> Allianz Arena, Munich
                                                    </td>
                                                    <td class="align-middle">UEFA Champions League Final</td>
                                                    <td class="align-middle">
                                                        <div class="btn-group btn-group-sm">
                                                            <a href="#" class="btn btn-outline-primary" data-bs-toggle="tooltip" title="add_to_calendar">
                                                                <i class="fas fa-calendar-plus"></i>
                                                            </a>
                                                            <a href="#" class="btn btn-outline-primary" data-bs-toggle="tooltip" title="设置提醒">
                                                                <i class="fas fa-bell"></i>
                                                            </a>
                                                        </div>
                                                    </td>
                                                </tr>
                                            
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <script>
                    document.addEventListener('DOMContentLoaded', function() {
                        const listViewBtn = document.getElementById('listViewBtn');
                        const calendarViewBtn = document.getElementById('calendarViewBtn');
                        const listView = document.getElementById('listView');
                        const calendarView = document.getElementById('calendarView');

                        listViewBtn.addEventListener('click', function() {
                            listView.style.display = 'flex';
                            calendarView.style.display = 'none';
                            listViewBtn.classList.add('btn-primary');
                            listViewBtn.classList.remove('btn-outline-primary');
                            calendarViewBtn.classList.add('btn-outline-primary');
                            calendarViewBtn.classList.remove('btn-primary');
                        });

                        calendarViewBtn.addEventListener('click', function() {
                            listView.style.display = 'none';
                            calendarView.style.display = 'flex';
                            calendarViewBtn.classList.add('btn-primary');
                            calendarViewBtn.classList.remove('btn-outline-primary');
                            listViewBtn.classList.add('btn-outline-primary');
                            listViewBtn.classList.remove('btn-primary');
                        });
                    });
                </script>
            
        </div>

        <!-- Venue Tab -->
        <div class="tab-pane fade" id="venue" role="tabpanel" aria-labelledby="venue-tab">
            <div class="row">
                <div class="col-lg-8">
                    <h3 class="mb-4">赛事场馆</h3>
                    <div class="card shadow-sm mb-4">
                        <div class="card-body p-0">
                            <!-- Interactive Map (using OpenStreetMap) -->
                            <div id="venue-map" class="rounded" style="height: 400px; width: 100%;"></div>
                        </div>
                    </div>

                    
                    <div class="card shadow-sm mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">场馆信息</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6><i class="fas fa-building text-primary me-2"></i>设施详情</h6>
                                    <ul class="list-unstyled ms-4 mb-4">
                                        <li class="mb-2"><strong>名称:</strong> Allianz Arena</li>
                                        <li class="mb-2"><strong>容量:</strong> 75,000</li>
                                        <li class="mb-2"><strong>场地类型:</strong> Natural Grass</li>
                                        <li class="mb-2"><strong>启用时间:</strong> 2005</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6><i class="fas fa-info-circle text-primary me-2"></i>设施服务</h6>
                                    <ul class="list-unstyled ms-4 mb-4">
                                        
                                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>拜仁慕尼黑博物馆</li>
                                        
                                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>球场小酒馆</li>
                                        
                                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>VIP休息室</li>
                                        
                                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>球迷商店</li>
                                        
                                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>地下停车场</li>
                                        
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                </div>

                <div class="col-lg-4">
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="fas fa-directions me-2"></i>交通指南</h5>
                        </div>
                        <div class="card-body">
                            <h6 class="mb-3">地址</h6>
                            <p class="mb-3">德国慕尼黑</p>

                            
                            <h6 class="mb-2"><i class="fas fa-subway text-primary me-2"></i>公共交通</h6>
                            <ul class="list-unstyled ms-4 mb-3">
                                
                                <li class="mb-2"><i class="fas fa-arrow-right text-primary me-2"></i>U-Bahn U6 to Fröttmaning</li>
                                
                                <li class="mb-2"><i class="fas fa-arrow-right text-primary me-2"></i>Bus 635</li>
                                
                                <li class="mb-2"><i class="fas fa-arrow-right text-primary me-2"></i>Special Match Day Shuttles</li>
                                
                                <li class="mb-2"><i class="fas fa-arrow-right text-primary me-2"></i>Park &amp; Ride</li>
                                
                            </ul>
                            

                            <h6 class="mb-2"><i class="fas fa-car text-primary me-2"></i>自驾车</h6>
                            <ul class="list-unstyled ms-4 mb-3">
                                <li class="mb-2">现场提供停车位</li>
                                <li class="mb-2">GPS坐标: 40.7128° N, 74.0060° W</li>
                            </ul>

                            <a href="https://www.google.com/maps/search/?api=1&query=Munich%2C%20Germany" class="btn btn-primary w-100" target="_blank">
                                <i class="fas fa-map-marked-alt me-2"></i>获取路线
                            </a>
                        </div>
                    </div>

                    <div class="card shadow-sm">
                        <div class="card-header">
                            <h5 class="mb-0">附近住宿</h5>
                        </div>
                        <div class="card-body p-0">
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item d-flex align-items-center py-3">
                                    <i class="fas fa-hotel text-primary me-3"></i>
                                    <div>
                                        <strong>大酒店</strong>
                                        <div class="text-muted">0.5 英里外</div>
                                    </div>
                                    <a href="#" class="btn btn-sm btn-outline-primary ms-auto">预订</a>
                                </li>
                                <li class="list-group-item d-flex align-items-center py-3">
                                    <i class="fas fa-hotel text-primary me-3"></i>
                                    <div>
                                        <strong>城市套房</strong>
                                        <div class="text-muted">0.8 英里外</div>
                                    </div>
                                    <a href="#" class="btn btn-sm btn-outline-primary ms-auto">预订</a>
                                </li>
                                <li class="list-group-item d-flex align-items-center py-3">
                                    <i class="fas fa-hotel text-primary me-3"></i>
                                    <div>
                                        <strong>体育度假村</strong>
                                        <div class="text-muted">1.2 英里外</div>
                                    </div>
                                    <a href="#" class="btn btn-sm btn-outline-primary ms-auto">预订</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Historical Data Tab -->
        <div class="tab-pane fade" id="history" role="tabpanel" aria-labelledby="history-tab">
            <h3>historical_data</h3>
            
                <div class="accordion" id="historyAccordion">
                    
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="heading2458279844464">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                        data-bs-target="#collapse2458279844464" aria-expanded="false" aria-controls="collapse2458279844464">
                                    2024 - 欧洲冠军联赛2023-24
                                </button>
                            </h2>
                            <div id="collapse2458279844464" class="accordion-collapse collapse" aria-labelledby="heading2458279844464"
                                 data-bs-parent="#historyAccordion">
                                <div class="accordion-body">
                                    <p>2023-24赛季欧洲冠军联赛决赛在英国伦敦温布利球场举行，皇家马德里2-0击败多特蒙德。皇家马德里赢得了他们的第15个欧洲杯/欧洲冠军联赛冠军。</p>
                                    <p><strong>result:</strong> 冠军：皇家马德里</p>
                                </div>
                            </div>
                        </div>
                    
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="heading2458279844768">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                        data-bs-target="#collapse2458279844768" aria-expanded="false" aria-controls="collapse2458279844768">
                                    2023 - 欧洲冠军联赛2022-23
                                </button>
                            </h2>
                            <div id="collapse2458279844768" class="accordion-collapse collapse" aria-labelledby="heading2458279844768"
                                 data-bs-parent="#historyAccordion">
                                <div class="accordion-body">
                                    <p>2022-23赛季欧洲冠军联赛决赛在土耳其伊斯坦布尔的阿塔图尔克奥林匹克体育场举行，曼城1-0击败国际米兰。</p>
                                    <p><strong>result:</strong> 冠军：曼城</p>
                                </div>
                            </div>
                        </div>
                    
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="heading2458279998480">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                        data-bs-target="#collapse2458279998480" aria-expanded="false" aria-controls="collapse2458279998480">
                                    2022 - 欧洲冠军联赛2021-22
                                </button>
                            </h2>
                            <div id="collapse2458279998480" class="accordion-collapse collapse" aria-labelledby="heading2458279998480"
                                 data-bs-parent="#historyAccordion">
                                <div class="accordion-body">
                                    <p>2021-22赛季欧洲冠军联赛决赛在法国圣但尼的法兰西体育场举行，皇家马德里1-0击败利物浦。</p>
                                    <p><strong>result:</strong> 冠军：皇家马德里</p>
                                </div>
                            </div>
                        </div>
                    
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="heading2458280200944">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                        data-bs-target="#collapse2458280200944" aria-expanded="false" aria-controls="collapse2458280200944">
                                    2021 - 欧洲冠军联赛2020-21
                                </button>
                            </h2>
                            <div id="collapse2458280200944" class="accordion-collapse collapse" aria-labelledby="heading2458280200944"
                                 data-bs-parent="#historyAccordion">
                                <div class="accordion-body">
                                    <p>2020-21赛季欧洲冠军联赛决赛在葡萄牙波尔图的巨龙体育场举行，切尔西1-0击败曼城。</p>
                                    <p><strong>result:</strong> Winner: Chelsea</p>
                                </div>
                            </div>
                        </div>
                    
                </div>
            
        </div>

        <!-- Comments Tab -->
        <div class="tab-pane fade" id="comments" role="tabpanel" aria-labelledby="comments-tab">
            <h3>Comments</h3>

            <div class="alert alert-info mb-4">
                Please <a href="/login?next=/events/2">login</a> to leave a comment
            </div>

            
                <div class="alert alert-light">
                    No comments yet. Be the first to comment!
                </div>
            
        </div>
    </div>
</div>

    </main>

    <!-- Back to top button -->
    <button class="back-to-top" aria-label="back_to_top">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- Dark mode toggle -->
    <button class="dark-mode-toggle" aria-label="Toggle dark mode">
        <i class="fas fa-moon"></i>
    </button>

    <!-- Cookie consent banner -->
    <div class="cookie-banner" style="display: none;">
        <p>We use cookies to enhance your experience. By continuing to visit this site you agree to our use of cookies.</p>
        <div class="btn-group">
            <button class="btn btn-primary accept-cookies">Accept</button>
            <button class="btn btn-outline-secondary decline-cookies">Decline</button>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-5 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 col-md-6 mb-4 mb-md-0">
                    <h5 class="mb-3"><i class="fas fa-trophy me-2"></i>体育赛事</h5>
                    <p class="mb-3">您获取全球体育赛事信息和更新的一站式平台。</p>
                    <div class="social-links mb-4">
                        <a href="#" class="me-2 text-white" aria-label="Facebook"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="me-2 text-white" aria-label="Twitter"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="me-2 text-white" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="me-2 text-white" aria-label="YouTube"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>
                <div class="col-lg-2 col-md-6 mb-4 mb-md-0">
                    <h5 class="mb-3">quick_links</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="/" class="text-white"><i class="fas fa-chevron-right me-1"></i>首页</a></li>
                        <li class="mb-2"><a href="/events" class="text-white"><i class="fas fa-chevron-right me-1"></i>赛事</a></li>
                        <li class="mb-2"><a href="#" class="text-white"><i class="fas fa-chevron-right me-1"></i>关于</a></li>
                        <li class="mb-2"><a href="#" class="text-white"><i class="fas fa-chevron-right me-1"></i>联系我们</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-6 mb-4 mb-md-0">
                    <h5 class="mb-3">分类</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="/events?category=1" class="text-white"><i class="fas fa-chevron-right me-1"></i>足球</a></li>
                        <li class="mb-2"><a href="/events?category=2" class="text-white"><i class="fas fa-chevron-right me-1"></i>篮球</a></li>
                        <li class="mb-2"><a href="/events?category=3" class="text-white"><i class="fas fa-chevron-right me-1"></i>网球</a></li>
                        <li class="mb-2"><a href="/events?category=7" class="text-white"><i class="fas fa-chevron-right me-1"></i>一级方程式</a></li>
                    </ul>
                </div>
                <div class="col-lg-4 col-md-6 mb-4 mb-md-0">
                    <h5 class="mb-3">新闻通讯</h5>
                    <p class="mb-3">订阅我们的新闻通讯，获取最新体育赛事更新</p>
                    <form class="newsletter-form">
                        <div class="input-group">
                            <input type="email" class="form-control" placeholder="输入您的邮箱" aria-label="输入您的邮箱" required>
                            <button class="btn btn-primary" type="submit">订阅</button>
                        </div>
                    </form>
                    <div class="mt-4">
                        <h5 class="mb-3">联系我们</h5>
                        <address class="mb-0">
                            <p class="mb-2"><i class="fas fa-map-marker-alt me-2"></i>北京市朝阳区体育场路123号</p>
                            <p class="mb-2"><i class="fas fa-phone me-2"></i>+86 10 1234 5678</p>
                            <p class="mb-0"><i class="fas fa-envelope me-2"></i><EMAIL></p>
                        </address>
                    </div>
                </div>
            </div>
            <hr class="my-4">
            <div class="row align-items-center">
                <div class="col-md-6 text-center text-md-start">
                    <p class="mb-md-0">&copy; 2025 体育赛事. 版权所有.</p>
                </div>
                <div class="col-md-6 text-center text-md-end">
                    <ul class="list-inline mb-0">
                        <li class="list-inline-item"><a href="#" class="text-white">隐私政策</a></li>
                        <li class="list-inline-item">|</li>
                        <li class="list-inline-item"><a href="#" class="text-white">服务条款</a></li>
                        <li class="list-inline-item">|</li>
                        <li class="list-inline-item"><a href="#" class="text-white">Cookie政策</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Ultimate Image Display Fix - Prevent Disappearing -->
    <style>
        /* 强制显示所有图片 - 最高优先级 */
        img, .card-img-top, .event-list-image, .event-detail-image, .category-image, .related-event-image {
            display: block !important;
            opacity: 1 !important;
            visibility: visible !important;
            transition: none !important;
            animation: none !important;
            max-width: 100% !important;
            height: auto !important;
        }

        /* 特定图片类型的强制显示 */
        .category-card img,
        .event-card img,
        .card img,
        img[src*="/static/images/"] {
            display: block !important;
            opacity: 1 !important;
            visibility: visible !important;
            transition: none !important;
            animation: none !important;
        }

        /* 防止任何隐藏机制 */
        .force-show-images img,
        .force-show-images .card-img-top,
        .force-show-images .event-list-image,
        .force-show-images .event-detail-image,
        .force-show-images .category-image,
        .force-show-images .related-event-image {
            display: block !important;
            opacity: 1 !important;
            visibility: visible !important;
            transition: none !important;
            animation: none !important;
        }

        /* 覆盖可能的Bootstrap或其他CSS */
        .d-none img, .invisible img, .opacity-0 img {
            display: block !important;
            opacity: 1 !important;
            visibility: visible !important;
        }

        /* 确保图片容器也显示 */
        .category-card, .event-card, .card {
            display: block !important;
            opacity: 1 !important;
            visibility: visible !important;
        }
    </style>

    <script>
        // 简化的图片显示修复 - 避免性能问题
        (function() {
            'use strict';

            // 立即添加保护类
            document.documentElement.classList.add('force-show-images');

            // 简化的图片保护函数
            function fixImages() {
                try {
                    const images = document.querySelectorAll('img');
                    images.forEach(img => {
                        // 只设置基本的显示样式，不锁定属性
                        if (img.style.display === 'none' || img.style.opacity === '0' || img.style.visibility === 'hidden') {
                            img.style.display = 'block';
                            img.style.opacity = '1';
                            img.style.visibility = 'visible';
                        }

                        // 移除可能的隐藏类
                        img.classList.remove('d-none', 'invisible', 'opacity-0');
                    });
                } catch (error) {
                    console.log('Image fix error:', error);
                }
            }

            // 页面加载时执行
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', fixImages);
            } else {
                fixImages();
            }

            // 页面完全加载后执行
            window.addEventListener('load', fixImages);

            // 延迟执行一次，确保所有动态内容加载完成
            setTimeout(fixImages, 1000);
        })();
    </script>
    <!-- Custom JS -->
    <script src="/static/js/main.js?q=1747309725"></script>
    <script src="/static/js/custom.js?q=1747726551"></script>
    <!-- Page-specific JavaScript -->
    
<!-- Leaflet JS for interactive maps -->
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin="/"></script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize map when venue tab is shown
        const venueTab = document.getElementById('venue-tab');
        if (venueTab) {
            venueTab.addEventListener('shown.bs.tab', function (e) {
                initMap();
            });
        }

        // Initialize map function
        function initMap() {
            // Check if map is already initialized
            if (window.venueMap) return;

            // Create map centered on event location
            
            const lat = 48.2188;
            const lng = 11.6242;
            

            window.venueMap = L.map('venue-map').setView([lat, lng], 13);

            // Add OpenStreetMap tiles
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            }).addTo(window.venueMap);

            // Add marker for event location
            const marker = L.marker([lat, lng]).addTo(window.venueMap);
            marker.bindPopup("<b>UEFA Champions League Final 2025</b><br>Munich, Germany").openPopup();

            // Add nearby points of interest
            const pois = [
                { name: "Grand Hotel", type: "hotel", lat: 40.7158, lng: -74.0090 },
                { name: "City Suites", type: "hotel", lat: 40.7188, lng: -74.0120 },
                { name: "Central Station", type: "transport", lat: 40.7100, lng: -74.0040 },
                { name: "Restaurant Row", type: "food", lat: 40.7148, lng: -74.0020 },
                { name: "Shopping Mall", type: "shopping", lat: 40.7110, lng: -74.0080 }
            ];

            // Add POI markers with different colors based on type
            pois.forEach(poi => {
                let markerColor = 'blue';
                let icon = 'home';

                if (poi.type === 'hotel') {
                    markerColor = 'orange';
                    icon = 'home';
                } else if (poi.type === 'transport') {
                    markerColor = 'green';
                    icon = 'subway';
                } else if (poi.type === 'food') {
                    markerColor = 'red';
                    icon = 'utensils';
                } else if (poi.type === 'shopping') {
                    markerColor = 'purple';
                    icon = 'shopping-bag';
                }

                const poiMarker = L.marker([poi.lat, poi.lng]).addTo(window.venueMap);
                poiMarker.bindPopup(`<b>${poi.name}</b><br>${poi.type}`);
            });

            // Resize map when window is resized
            window.addEventListener('resize', function() {
                window.venueMap.invalidateSize();
            });
        }

        // Initialize tabs with history support
        const triggerTabList = document.querySelectorAll('#eventTabs button');
        triggerTabList.forEach(function (triggerEl) {
            const tabTrigger = new bootstrap.Tab(triggerEl);

            triggerEl.addEventListener('click', function (e) {
                e.preventDefault();
                tabTrigger.show();

                // Update URL hash
                const tabId = triggerEl.getAttribute('data-bs-target');
                window.location.hash = tabId;
            });
        });

        // Show tab based on URL hash
        if (window.location.hash) {
            try {
                const activeTab = document.querySelector(`#eventTabs button[data-bs-target="${window.location.hash}"]`);
                if (activeTab) {
                    const tab = new bootstrap.Tab(activeTab);
                    tab.show();

                    // Initialize map if venue tab is active
                    if (window.location.hash === '#venue') {
                        setTimeout(initMap, 100); // Small delay to ensure the tab is visible
                    }
                }
            } catch (error) {
                console.error('Error showing tab:', error);
            }
        }

        // Initialize countdown timer
        const countdownElement = document.querySelector('.countdown');
        if (countdownElement) {
            const eventDate = new Date(countdownElement.dataset.eventDate).getTime();

            const countdownTimer = setInterval(() => {
                const now = new Date().getTime();
                const distance = eventDate - now;

                if (distance < 0) {
                    clearInterval(countdownTimer);
                    countdownElement.innerHTML = '<div class="alert alert-info">此赛事已开始！</div>';
                    return;
                }

                const days = Math.floor(distance / (1000 * 60 * 60 * 24));
                const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
                const seconds = Math.floor((distance % (1000 * 60)) / 1000);

                countdownElement.innerHTML = `
                    <div class="countdown-item">
                        <span class="countdown-number">${days}</span>
                        <span class="countdown-label">天</span>
                    </div>
                    <div class="countdown-item">
                        <span class="countdown-number">${hours}</span>
                        <span class="countdown-label">小时</span>
                    </div>
                    <div class="countdown-item">
                        <span class="countdown-number">${minutes}</span>
                        <span class="countdown-label">分钟</span>
                    </div>
                    <div class="countdown-item">
                        <span class="countdown-number">${seconds}</span>
                        <span class="countdown-label">秒</span>
                    </div>
                `;
            }, 1000);
        }
    });
</script>


    <!-- Accessibility script -->
    <script>
        // Add 'aria-current' to active navigation items
        document.addEventListener('DOMContentLoaded', function() {
            const currentPath = window.location.pathname;
            document.querySelectorAll('.navbar-nav .nav-link').forEach(link => {
                if (link.getAttribute('href') === currentPath) {
                    link.setAttribute('aria-current', 'page');
                    link.classList.add('active');
                }
            });
        });
    </script>
</body>
</html>