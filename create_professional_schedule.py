#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建专业西餐厅排班表
"""

from app import app, db
from models import User, Schedule
from datetime import datetime, date, timedelta, time

def create_professional_schedule():
    with app.app_context():
        # 删除现有排班
        Schedule.query.delete()

        # 获取所有员工
        all_staff = User.query.filter(User.role != 'admin').all()

        if not all_staff:
            print("没有找到员工，请先添加员工数据")
            return

        # 按职位分组员工
        managers = [s for s in all_staff if s.role == 'manager']
        chefs = [s for s in all_staff if s.role == 'chef']
        waiters = [s for s in all_staff if s.role == 'waiter']
        cashiers = [s for s in all_staff if s.role == 'cashier']
        support_staff = [s for s in all_staff if s.role == 'employee']

        print(f"员工统计: 管理{len(managers)}人, 厨师{len(chefs)}人, 服务员{len(waiters)}人, 收银/吧台{len(cashiers)}人, 后勤{len(support_staff)}人")

        # 生成未来30天的排班
        start_date = date.today()
        schedules = []

        # 定义班次时间
        morning_prep = time(8, 0)    # 8:00 - 准备时间
        lunch_start = time(11, 0)    # 11:00 - 午餐开始
        lunch_end = time(15, 0)      # 15:00 - 午餐结束
        dinner_prep = time(16, 0)    # 16:00 - 晚餐准备
        dinner_start = time(17, 30)  # 17:30 - 晚餐开始
        dinner_end = time(23, 0)     # 23:00 - 晚餐结束
        closing_time = time(23, 59)  # 23:59 - 收尾工作

        for day_offset in range(30):
            current_date = start_date + timedelta(days=day_offset)
            weekday = current_date.weekday()  # 0=周一, 6=周日
            is_weekend = weekday >= 5

            # 管理层排班 - 确保每天有管理人员在岗
            if managers:
                # 总经理 - 周一到周五全天
                if weekday < 5:  # 工作日
                    schedules.append(Schedule(
                        employee_id=managers[0].id,  # 总经理
                        date=current_date,
                        shift_start=time(9, 0),
                        shift_end=time(18, 0),
                        position='总经理-日班'
                    ))

                # 副总经理/楼面经理 - 轮班覆盖营业时间
                manager_index = day_offset % (len(managers) - 1) + 1
                if is_weekend:
                    # 周末全天班
                    schedules.append(Schedule(
                        employee_id=managers[manager_index].id,
                        date=current_date,
                        shift_start=time(10, 0),
                        shift_end=time(23, 30),
                        position='值班经理-全天'
                    ))
                else:
                    # 工作日晚班
                    schedules.append(Schedule(
                        employee_id=managers[manager_index].id,
                        date=current_date,
                        shift_start=time(16, 0),
                        shift_end=time(23, 30),
                        position='值班经理-晚班'
                    ))

            # 厨房团队排班
            if chefs:
                # 行政总厨 - 每天监督
                schedules.append(Schedule(
                    employee_id=chefs[0].id,  # 行政总厨
                    date=current_date,
                    shift_start=morning_prep,
                    shift_end=time(20, 0),
                    position='行政总厨-全天'
                ))

                # 副主厨 - 晚班主力
                if len(chefs) > 1:
                    schedules.append(Schedule(
                        employee_id=chefs[1].id,  # 副主厨
                        date=current_date,
                        shift_start=time(14, 0),
                        shift_end=dinner_end,
                        position='副主厨-晚班'
                    ))

                # 烤肉主厨 - 晚餐高峰
                if len(chefs) > 2:
                    schedules.append(Schedule(
                        employee_id=chefs[2].id,  # 烤肉主厨
                        date=current_date,
                        shift_start=dinner_prep,
                        shift_end=dinner_end,
                        position='烤肉主厨-晚班'
                    ))

                # 甜品主厨 - 下午到晚上
                if len(chefs) > 3:
                    schedules.append(Schedule(
                        employee_id=chefs[3].id,  # 甜品主厨
                        date=current_date,
                        shift_start=time(13, 0),
                        shift_end=time(22, 0),
                        position='甜品主厨-下午晚班'
                    ))

                # 配菜厨师 - 轮班
                for i in range(4, min(len(chefs), 6)):
                    chef_index = i
                    if day_offset % 2 == 0:  # 偶数天早班
                        schedules.append(Schedule(
                            employee_id=chefs[chef_index].id,
                            date=current_date,
                            shift_start=morning_prep,
                            shift_end=lunch_end,
                            position='配菜厨师-早班'
                        ))
                    else:  # 奇数天晚班
                        schedules.append(Schedule(
                            employee_id=chefs[chef_index].id,
                            date=current_date,
                            shift_start=dinner_prep,
                            shift_end=dinner_end,
                            position='配菜厨师-晚班'
                        ))

            # 前厅服务团队排班
            if waiters:
                waiter_count = 6 if is_weekend else 4

                # 首席服务员 - 晚班监督
                schedules.append(Schedule(
                    employee_id=waiters[0].id,  # 首席服务员
                    date=current_date,
                    shift_start=dinner_prep,
                    shift_end=time(23, 30),
                    position='首席服务员-晚班'
                ))

                # 侍酒师 - 晚餐时段
                if len(waiters) > 1:
                    schedules.append(Schedule(
                        employee_id=waiters[1].id,  # 侍酒师
                        date=current_date,
                        shift_start=dinner_start,
                        shift_end=time(22, 30),
                        position='侍酒师-晚班'
                    ))

                # 高级服务员 - 分早晚班
                for i in range(2, min(len(waiters), 4)):
                    waiter_index = i
                    if i % 2 == 0:  # 早班
                        schedules.append(Schedule(
                            employee_id=waiters[waiter_index].id,
                            date=current_date,
                            shift_start=time(10, 30),
                            shift_end=time(16, 0),
                            position='高级服务员-午班'
                        ))
                    else:  # 晚班
                        schedules.append(Schedule(
                            employee_id=waiters[waiter_index].id,
                            date=current_date,
                            shift_start=dinner_prep,
                            shift_end=dinner_end,
                            position='高级服务员-晚班'
                        ))

                # 普通服务员 - 轮班
                for i in range(4, min(len(waiters), waiter_count + 2)):
                    waiter_index = i % len(waiters)
                    shift_type = (day_offset + i) % 3

                    if shift_type == 0:  # 早班
                        schedules.append(Schedule(
                            employee_id=waiters[waiter_index].id,
                            date=current_date,
                            shift_start=time(10, 0),
                            shift_end=time(16, 0),
                            position='服务员-午班'
                        ))
                    elif shift_type == 1:  # 晚班
                        schedules.append(Schedule(
                            employee_id=waiters[waiter_index].id,
                            date=current_date,
                            shift_start=time(16, 30),
                            shift_end=time(23, 0),
                            position='服务员-晚班'
                        ))
                    # shift_type == 2 休息

            # 吧台/收银团队排班
            if cashiers:
                # 首席调酒师 - 晚班
                schedules.append(Schedule(
                    employee_id=cashiers[0].id,  # 首席调酒师
                    date=current_date,
                    shift_start=time(17, 0),
                    shift_end=time(23, 30),
                    position='首席调酒师-晚班'
                ))

                # 调酒师 - 周末或繁忙时段
                if len(cashiers) > 1 and is_weekend:
                    schedules.append(Schedule(
                        employee_id=cashiers[1].id,  # 调酒师
                        date=current_date,
                        shift_start=time(19, 0),
                        shift_end=time(23, 0),
                        position='调酒师-晚班'
                    ))

                # 收银主管 - 全天监督
                if len(cashiers) > 2:
                    schedules.append(Schedule(
                        employee_id=cashiers[2].id,  # 收银主管
                        date=current_date,
                        shift_start=time(11, 0),
                        shift_end=time(22, 0),
                        position='收银主管-全天'
                    ))

                # 收银员 - 分班
                for i in range(3, len(cashiers)):
                    cashier_index = i
                    if (day_offset + i) % 2 == 0:  # 早班
                        schedules.append(Schedule(
                            employee_id=cashiers[cashier_index].id,
                            date=current_date,
                            shift_start=time(11, 0),
                            shift_end=time(17, 0),
                            position='收银员-午班'
                        ))
                    else:  # 晚班
                        schedules.append(Schedule(
                            employee_id=cashiers[cashier_index].id,
                            date=current_date,
                            shift_start=time(17, 0),
                            shift_end=time(23, 0),
                            position='收银员-晚班'
                        ))

            # 后勤支持团队排班
            if support_staff:
                for i, staff in enumerate(support_staff):
                    if 'cleaner' in staff.username:  # 清洁员
                        if i % 2 == 0:  # 早班清洁
                            schedules.append(Schedule(
                                employee_id=staff.id,
                                date=current_date,
                                shift_start=time(7, 0),
                                shift_end=time(12, 0),
                                position='清洁员-早班'
                            ))
                        else:  # 晚班清洁
                            schedules.append(Schedule(
                                employee_id=staff.id,
                                date=current_date,
                                shift_start=time(23, 30),
                                shift_end=time(23, 59),
                                position='清洁员-夜班'
                            ))
                    elif 'security' in staff.username:  # 保安
                        schedules.append(Schedule(
                            employee_id=staff.id,
                            date=current_date,
                            shift_start=time(18, 0),
                            shift_end=time(23, 59),
                            position='保安-夜班'
                        ))

        # 添加所有排班到数据库
        for schedule in schedules:
            db.session.add(schedule)

        db.session.commit()
        print(f"成功创建 {len(schedules)} 条专业排班记录！")

        # 统计信息
        position_stats = {}
        for schedule in schedules:
            position = schedule.position
            if position not in position_stats:
                position_stats[position] = 0
            position_stats[position] += 1

        print("\n按职位统计班次:")
        for position, count in sorted(position_stats.items()):
            print(f"- {position}: {count} 个班次")

        print(f"\n排班特色:")
        print("- 管理层全天候监督")
        print("- 厨房团队专业分工")
        print("- 前厅服务分级管理")
        print("- 吧台收银合理配置")
        print("- 后勤支持24小时保障")

if __name__ == '__main__':
    create_professional_schedule()
