#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import subprocess
import shutil
from pathlib import Path

def create_portable_application():
    """创建便携版应用程序"""
    
    print("🚀 创建 AllSportsNow 便携版应用程序")
    print("=" * 60)
    
    # 检查PyInstaller是否已安装
    try:
        import PyInstaller
        print("✅ PyInstaller 已安装")
    except ImportError:
        print("📦 正在安装 PyInstaller...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✅ PyInstaller 安装完成")
    
    # 创建打包脚本
    create_build_script()
    
    # 创建启动脚本
    create_launcher_script()
    
    # 创建配置文件
    create_config_files()
    
    # 执行打包
    build_application()
    
    print("\n🎉 便携版应用程序创建完成！")
    print("=" * 60)
    print("📁 输出目录: dist/AllSportsNow/")
    print("🚀 启动文件: AllSportsNow.exe")
    print("📋 使用说明:")
    print("   1. 将整个 dist/AllSportsNow/ 文件夹复制到任何计算机")
    print("   2. 双击 AllSportsNow.exe 启动应用")
    print("   3. 浏览器会自动打开 http://localhost:5000")
    print("   4. 无需安装Python或其他依赖")

def create_build_script():
    """创建PyInstaller构建脚本"""
    
    build_script = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 收集所有数据文件
datas = [
    ('templates', 'templates'),
    ('static', 'static'),
    ('languages.py', '.'),
]

# 收集隐藏导入
hiddenimports = [
    'flask',
    'werkzeug',
    'jinja2',
    'markupsafe',
    'itsdangerous',
    'click',
    'blinker',
    'importlib_metadata',
    'zipp',
]

a = Analysis(
    ['app.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='AllSportsNow',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='static/images/allsportsnow-logo.ico' if os.path.exists('static/images/allsportsnow-logo.ico') else None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='AllSportsNow',
)
'''
    
    with open('AllSportsNow.spec', 'w', encoding='utf-8') as f:
        f.write(build_script)
    
    print("✅ 创建构建脚本: AllSportsNow.spec")

def create_launcher_script():
    """创建启动脚本"""
    
    launcher_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import time
import webbrowser
import threading
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent.absolute()
sys.path.insert(0, str(current_dir))

def open_browser():
    """延迟打开浏览器"""
    time.sleep(2)  # 等待服务器启动
    webbrowser.open('http://localhost:5000')

def main():
    """主函数"""
    print("🚀 启动 AllSportsNow 体育赛事平台")
    print("=" * 50)
    print("📍 服务器地址: http://localhost:5000")
    print("🌐 浏览器将自动打开...")
    print("❌ 按 Ctrl+C 停止服务器")
    print("=" * 50)
    
    # 在后台线程中打开浏览器
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    # 导入并启动Flask应用
    try:
        from app import app
        app.run(host='127.0.0.1', port=5000, debug=False)
    except KeyboardInterrupt:
        print("\\n👋 AllSportsNow 已停止")
    except Exception as e:
        print(f"❌ 启动错误: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
'''
    
    with open('launcher.py', 'w', encoding='utf-8') as f:
        f.write(launcher_script)
    
    print("✅ 创建启动脚本: launcher.py")

def create_config_files():
    """创建配置文件"""
    
    # 创建README文件
    readme_content = '''# AllSportsNow 便携版

## 🎯 关于
AllSportsNow 是一个现代化的体育赛事信息平台，提供全球体育赛事的最新信息和详细数据。

## 🚀 快速开始
1. 双击 `AllSportsNow.exe` 启动应用
2. 浏览器会自动打开并显示网站
3. 如果浏览器没有自动打开，请手动访问: http://localhost:5000

## ✨ 功能特色
- 🏆 全球体育赛事信息
- 🌍 中英文双语支持
- 📱 响应式设计
- 🔍 智能搜索功能
- 📅 详细赛程安排
- 🏟️ 场馆信息
- 📊 历史数据

## 🎮 支持的体育项目
- ⚽ 足球 (FIFA世界杯、欧冠、英超、西甲、意甲)
- 🏀 篮球 (NBA、欧洲篮球联赛)
- 🎾 网球 (四大满贯)
- 🏊 游泳 (世界锦标赛、奥运会)
- 🏃 田径 (世界锦标赛、钻石联赛)
- ⛳ 高尔夫 (四大满贯)
- 🏎️ 一级方程式

## 💻 系统要求
- Windows 7/8/10/11
- 无需安装Python或其他依赖
- 建议内存: 512MB以上
- 硬盘空间: 100MB以上

## 🛠️ 故障排除
如果遇到问题:
1. 确保端口5000未被占用
2. 检查防火墙设置
3. 以管理员身份运行

## 📞 支持
如有问题或建议，请联系开发团队。

---
© 2025 AllSportsNow. All rights reserved.
'''
    
    with open('README.md', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    # 创建批处理启动文件
    batch_content = '''@echo off
title AllSportsNow - Sports Events Platform
echo.
echo ========================================
echo   AllSportsNow Sports Events Platform
echo ========================================
echo.
echo Starting server...
echo Browser will open automatically...
echo Press Ctrl+C to stop the server
echo.

AllSportsNow.exe
pause
'''
    
    with open('start_allsportsnow.bat', 'w', encoding='utf-8') as f:
        f.write(batch_content)
    
    print("✅ 创建配置文件: README.md, start_allsportsnow.bat")

def build_application():
    """构建应用程序"""
    
    print("\n📦 开始打包应用程序...")
    print("⏳ 这可能需要几分钟时间...")
    
    try:
        # 使用PyInstaller打包
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--clean",
            "--noconfirm", 
            "AllSportsNow.spec"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 应用程序打包成功")
            
            # 复制额外文件到输出目录
            dist_dir = Path("dist/AllSportsNow")
            if dist_dir.exists():
                # 复制README和启动脚本
                shutil.copy2("README.md", dist_dir)
                shutil.copy2("start_allsportsnow.bat", dist_dir)
                
                print("✅ 复制配置文件完成")
                
                # 创建使用说明
                create_usage_instructions(dist_dir)
                
            else:
                print("❌ 输出目录不存在")
        else:
            print("❌ 打包失败:")
            print(result.stderr)
            
    except Exception as e:
        print(f"❌ 打包过程出错: {e}")

def create_usage_instructions(dist_dir):
    """在输出目录创建使用说明"""
    
    instructions = '''🎉 AllSportsNow 便携版使用说明

📁 文件说明:
- AllSportsNow.exe     : 主程序 (双击启动)
- start_allsportsnow.bat : 批处理启动文件
- README.md            : 详细说明文档
- _internal/           : 程序依赖文件 (请勿删除)

🚀 启动方法:
方法1: 双击 AllSportsNow.exe
方法2: 双击 start_allsportsnow.bat

🌐 访问地址:
http://localhost:5000

✨ 功能特色:
- 全球体育赛事信息
- 中英文双语支持
- 响应式设计
- 智能搜索
- 详细赛程和场馆信息

❌ 停止服务:
在命令行窗口按 Ctrl+C

📞 技术支持:
如有问题请查看 README.md 文件
'''
    
    with open(dist_dir / "使用说明.txt", 'w', encoding='utf-8') as f:
        f.write(instructions)
    
    print("✅ 创建使用说明文件")

if __name__ == "__main__":
    create_portable_application()
