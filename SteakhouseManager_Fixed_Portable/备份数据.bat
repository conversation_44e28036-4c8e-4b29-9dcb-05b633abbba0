@echo off
title ���ݱ��ݹ���
echo.
echo ţ�Ų�������ϵͳ - ���ݱ��ݹ���
echo ================================
echo.

set backup_dir=backup_%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set backup_dir=%backup_dir: =0%

if not exist backup mkdir backup
if not exist data\restaurant.db (
    echo ����: ���ݿ��ļ�������
    pause
    exit /b 1
)

copy data\restaurant.db backup\restaurant_%backup_dir%.db
echo.
echo ���ݱ������: backup\restaurant_%backup_dir%.db
echo.
pause
