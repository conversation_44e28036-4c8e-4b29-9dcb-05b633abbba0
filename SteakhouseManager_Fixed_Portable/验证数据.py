#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据验证脚本 - 验证便携版数据完整性
"""

import sqlite3
from pathlib import Path

def verify_data():
    """验证数据完整性"""
    
    db_path = Path("data/restaurant.db")
    if not db_path.exists():
        print("❌ 数据库文件不存在")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查各表数据
        tables_to_check = [
            ('user', '员工'),
            ('table', '桌位'),
            ('menu_item', '菜品'),
            ('category', '分类'),
            ('ingredient', '库存'),
            ('schedule', '排班')
        ]
        
        print("🔍 数据验证结果:")
        print("-" * 40)
        
        for table_name, display_name in tables_to_check:
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"{display_name}: {count} 条记录")
        
        conn.close()
        print("-" * 40)
        print("✅ 数据验证完成")
        return True
        
    except Exception as e:
        print(f"❌ 数据验证失败: {e}")
        return False

if __name__ == '__main__':
    verify_data()
