# 🥩 牛排餐厅管理系统 - 完整数据便携版

## 📋 系统简介
这是包含完整数据的牛排餐厅管理系统便携版，解决了所有数据丢失问题：

### 🏪 完整数据内容
- **31个桌位** - 2人桌、4人桌、6人桌、8人桌、VIP包间、吧台座位、露台座位
- **88道菜品饮品** - 精选牛排、顶级和牛、海鲜、威士忌、香槟、白酒等
- **25名专业员工** - 总经理、行政总厨、首席服务员、侍酒师、调酒师等
- **576个排班记录** - 30天专业排班，周一到周日全覆盖
- **56种库存原料** - 与西餐菜品完全匹配的专业原料
- **客户管理系统** - 生日提醒、忠诚度报告等功能

## 🚀 快速开始

### 1. 启动系统
**方法一**: 双击 `SteakhouseManager.exe`
**方法二**: 双击 `启动系统.bat`

### 2. 访问系统
系统启动后会自动打开浏览器访问：http://127.0.0.1:5000

### 3. 登录验证
**管理员账号：**
- 用户名：admin
- 密码：admin123

**专业员工账号：**
- 总经理：general_manager / gm123456
- 行政总厨：executive_chef / chef123456
- 首席服务员：head_waiter / head123456
- 侍酒师：sommelier / wine123456
- 首席调酒师：head_bartender / bar123456

## 🔧 数据管理

### 📊 数据验证
运行 `python 验证数据.py` 检查数据完整性

### 💾 数据备份
双击 `备份数据.bat` 创建数据备份

### 🔄 数据恢复
双击 `恢复数据.bat` 从备份恢复数据

### 📁 数据位置
- 主数据库：`data/restaurant.db`
- 备份目录：`backup/`

## ✅ 功能验证清单

启动后请验证以下功能：

### 🏪 POS收银系统
- [ ] 查看31个桌位（T01-T20, VIP01-03, BAR01-04, OUT01-04）
- [ ] 查看88道菜品饮品
- [ ] 桌位状态切换功能
- [ ] 菜品搜索和分类筛选

### 👥 员工管理
- [ ] 查看25名专业员工
- [ ] 员工信息完整（姓名、职位、部门）
- [ ] 员工登录功能正常

### 📅 排班管理
- [ ] 查看576个排班记录
- [ ] 周一到周日都有排班
- [ ] 不同职位合理分配

### 📦 库存管理
- [ ] 查看56种原料库存
- [ ] 库存分类清晰（牛肉类、海鲜类等）
- [ ] 库存数量和价格正确

### 👤 客户管理
- [ ] 生日提醒功能正常
- [ ] 忠诚度报告显示正确
- [ ] 客户信息管理

### 📊 财务报表
- [ ] 营收分析功能
- [ ] 数据统计正确

## 🛠️ 故障排除

### 问题：数据丢失
**解决方案：**
1. 检查 `data/restaurant.db` 文件是否存在
2. 运行 `python 验证数据.py` 检查数据
3. 如有备份，使用 `恢复数据.bat` 恢复

### 问题：无法启动
**解决方案：**
1. 确保Windows防火墙允许程序运行
2. 检查5000端口是否被占用
3. 以管理员身份运行

### 问题：浏览器未自动打开
**解决方案：**
手动打开浏览器访问：http://127.0.0.1:5000

## 💻 系统要求
- Windows 7/8/10/11 (64位)
- 至少 512MB 可用内存
- 至少 100MB 可用磁盘空间
- 现代浏览器（Chrome、Edge、Firefox等）

## 📞 技术支持
如遇问题，请检查：
1. 数据文件完整性
2. 系统权限设置
3. 网络端口占用

---
© 2024 牛排餐厅管理系统 - 完整数据便携版
版本：2.0 (修复数据丢失问题)
