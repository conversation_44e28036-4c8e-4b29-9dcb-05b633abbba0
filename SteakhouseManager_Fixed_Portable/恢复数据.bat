@echo off
title ���ݻָ�����
echo.
echo ţ�Ų�������ϵͳ - ���ݻָ�����
echo ================================
echo.

if not exist backup (
    echo ����: ����Ŀ¼������
    pause
    exit /b 1
)

echo ���õı����ļ�:
dir /b backup\*.db

echo.
set /p backup_file=������Ҫ�ָ��ı����ļ���: 

if not exist backup\%backup_file% (
    echo ����: �����ļ�������
    pause
    exit /b 1
)

copy backup\%backup_file% data\restaurant.db
echo.
echo ���ݻָ����
echo ������ϵͳ��ʹ�ûָ�������
echo.
pause
