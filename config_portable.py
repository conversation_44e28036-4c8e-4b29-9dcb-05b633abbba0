import os
from pathlib import Path

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'steakhouse-secret-key-2024'
    
    # 数据库配置
    if hasattr(sys, '_MEIPASS'):
        # 便携版环境 - 数据库放在用户目录
        data_dir = Path.cwd() / "data"
        data_dir.mkdir(exist_ok=True)
        SQLALCHEMY_DATABASE_URI = f"sqlite:///{data_dir / 'restaurant.db'}"
    else:
        # 开发环境
        SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///instance/restaurant.db'
    
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_pre_ping': True,
        'pool_recycle': 300,
    }
