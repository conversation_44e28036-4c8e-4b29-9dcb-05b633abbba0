# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['launcher_fixed.py'],
    pathex=[],
    binaries=[],
    datas=[('templates', 'templates'), ('static', 'static'), ('data', 'data'), ('app.py', '.'), ('models.py', '.'), ('config_portable.py', '.'), ('routes', 'routes')],
    hiddenimports=['flask', 'flask_sqlalchemy', 'flask_login', 'werkzeug', 'jinja2', 'sqlite3'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='SteakhouseManager_Fixed',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
