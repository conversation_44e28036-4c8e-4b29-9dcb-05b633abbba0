#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
添加国际菜品到数据库
"""

from app import app, db
from models import Category, MenuItem

def add_international_dishes():
    with app.app_context():
        # 获取分类
        japanese_category = Category.query.filter_by(name='日料').first()
        korean_category = Category.query.filter_by(name='韩料').first()
        thai_category = Category.query.filter_by(name='泰菜').first()
        indian_category = Category.query.filter_by(name='印度菜').first()
        italian_category = Category.query.filter_by(name='意大利菜').first()
        french_category = Category.query.filter_by(name='法国菜').first()
        western_category = Category.query.filter_by(name='西餐').first()
        snack_category = Category.query.filter_by(name='小食').first()
        breakfast_category = Category.query.filter_by(name='早餐').first()
        latenight_category = Category.query.filter_by(name='夜宵').first()
        
        new_dishes = []
        
        # 日料 (15种)
        if japanese_category:
            new_dishes.extend([
                MenuItem(name='寿司拼盘', description='新鲜寿司拼盘，多种口味', price=128.0, category_id=japanese_category.id, preparation_time=20),
                MenuItem(name='刺身拼盘', description='新鲜刺身拼盘，鲜美可口', price=168.0, category_id=japanese_category.id, preparation_time=15),
                MenuItem(name='天妇罗', description='日式天妇罗，外酥内嫩', price=88.0, category_id=japanese_category.id, preparation_time=15),
                MenuItem(name='照烧鸡腿', description='照烧鸡腿，甜香可口', price=68.0, category_id=japanese_category.id, preparation_time=20),
                MenuItem(name='日式拉面', description='正宗日式拉面，汤浓面滑', price=58.0, category_id=japanese_category.id, preparation_time=18),
                MenuItem(name='鳗鱼饭', description='蒲烧鳗鱼饭，香甜可口', price=98.0, category_id=japanese_category.id, preparation_time=15),
                MenuItem(name='亲子丼', description='日式亲子丼，嫩滑鲜美', price=48.0, category_id=japanese_category.id, preparation_time=12),
                MenuItem(name='牛肉丼', description='日式牛肉丼，香嫩可口', price=58.0, category_id=japanese_category.id, preparation_time=15),
                MenuItem(name='味噌汤', description='日式味噌汤，清香暖胃', price=18.0, category_id=japanese_category.id, preparation_time=8),
                MenuItem(name='章鱼小丸子', description='大阪章鱼小丸子，Q弹可口', price=38.0, category_id=japanese_category.id, preparation_time=10),
                MenuItem(name='日式咖喱', description='日式咖喱饭，温和香甜', price=48.0, category_id=japanese_category.id, preparation_time=15),
                MenuItem(name='茶碗蒸', description='日式茶碗蒸，嫩滑营养', price=28.0, category_id=japanese_category.id, preparation_time=20),
                MenuItem(name='日式烤鱼', description='日式盐烤鱼，清香可口', price=78.0, category_id=japanese_category.id, preparation_time=25),
                MenuItem(name='手卷', description='日式手卷，新鲜美味', price=68.0, category_id=japanese_category.id, preparation_time=10),
                MenuItem(name='日式炸猪排', description='日式炸猪排，外酥内嫩', price=68.0, category_id=japanese_category.id, preparation_time=18),
            ])
        
        # 韩料 (12种)
        if korean_category:
            new_dishes.extend([
                MenuItem(name='韩式烤肉', description='韩式烤肉，香嫩可口', price=128.0, category_id=korean_category.id, preparation_time=25),
                MenuItem(name='石锅拌饭', description='韩式石锅拌饭，营养丰富', price=58.0, category_id=korean_category.id, preparation_time=15),
                MenuItem(name='泡菜炒饭', description='韩式泡菜炒饭，酸辣开胃', price=38.0, category_id=korean_category.id, preparation_time=12),
                MenuItem(name='韩式炸鸡', description='韩式炸鸡，外酥内嫩', price=78.0, category_id=korean_category.id, preparation_time=20),
                MenuItem(name='部队锅', description='韩式部队锅，丰富多样', price=88.0, category_id=korean_category.id, preparation_time=25),
                MenuItem(name='冷面', description='朝鲜冷面，清爽可口', price=42.0, category_id=korean_category.id, preparation_time=10),
                MenuItem(name='韩式海带汤', description='韩式海带汤，清淡营养', price=28.0, category_id=korean_category.id, preparation_time=15),
                MenuItem(name='韩式煎饼', description='韩式煎饼，香脆可口', price=48.0, category_id=korean_category.id, preparation_time=12),
                MenuItem(name='辣炒年糕', description='韩式辣炒年糕，Q弹香辣', price=38.0, category_id=korean_category.id, preparation_time=15),
                MenuItem(name='韩式烤鱼', description='韩式烤鱼，香辣可口', price=98.0, category_id=korean_category.id, preparation_time=30),
                MenuItem(name='韩式泡菜', description='正宗韩式泡菜，酸辣开胃', price=18.0, category_id=korean_category.id, preparation_time=5),
                MenuItem(name='韩式紫菜包饭', description='韩式紫菜包饭，营养美味', price=32.0, category_id=korean_category.id, preparation_time=10),
            ])
        
        # 泰菜 (12种)
        if thai_category:
            new_dishes.extend([
                MenuItem(name='冬阴功汤', description='泰式冬阴功汤，酸辣开胃', price=48.0, category_id=thai_category.id, preparation_time=15),
                MenuItem(name='泰式炒河粉', description='泰式炒河粉，酸甜可口', price=42.0, category_id=thai_category.id, preparation_time=12),
                MenuItem(name='绿咖喱鸡', description='泰式绿咖喱鸡，香辣浓郁', price=68.0, category_id=thai_category.id, preparation_time=20),
                MenuItem(name='红咖喱牛肉', description='泰式红咖喱牛肉，香辣可口', price=78.0, category_id=thai_category.id, preparation_time=25),
                MenuItem(name='芒果糯米饭', description='泰式芒果糯米饭，香甜可口', price=38.0, category_id=thai_category.id, preparation_time=10),
                MenuItem(name='泰式青木瓜沙拉', description='泰式青木瓜沙拉，酸辣清爽', price=32.0, category_id=thai_category.id, preparation_time=8),
                MenuItem(name='泰式烤鱼', description='泰式烤鱼，香辣可口', price=88.0, category_id=thai_category.id, preparation_time=30),
                MenuItem(name='椰汁鸡汤', description='泰式椰汁鸡汤，清甜可口', price=58.0, category_id=thai_category.id, preparation_time=25),
                MenuItem(name='泰式春卷', description='泰式春卷，清爽可口', price=28.0, category_id=thai_category.id, preparation_time=10),
                MenuItem(name='泰式炒空心菜', description='泰式炒空心菜，清香爽脆', price=22.0, category_id=thai_category.id, preparation_time=8),
                MenuItem(name='泰式菠萝饭', description='泰式菠萝饭，酸甜可口', price=48.0, category_id=thai_category.id, preparation_time=20),
                MenuItem(name='泰式奶茶', description='正宗泰式奶茶，香甜浓郁', price=25.0, category_id=thai_category.id, preparation_time=5),
            ])
        
        # 印度菜 (10种)
        if indian_category:
            new_dishes.extend([
                MenuItem(name='印度咖喱鸡', description='印度咖喱鸡，香辣浓郁', price=68.0, category_id=indian_category.id, preparation_time=25),
                MenuItem(name='印度飞饼', description='印度飞饼，层次丰富', price=28.0, category_id=indian_category.id, preparation_time=15),
                MenuItem(name='印度烤鸡', description='印度烤鸡，香料浓郁', price=78.0, category_id=indian_category.id, preparation_time=30),
                MenuItem(name='印度咖喱羊肉', description='印度咖喱羊肉，香辣可口', price=88.0, category_id=indian_category.id, preparation_time=35),
                MenuItem(name='印度奶茶', description='正宗印度奶茶，香甜浓郁', price=22.0, category_id=indian_category.id, preparation_time=8),
                MenuItem(name='印度烤饼', description='印度烤饼，香脆可口', price=18.0, category_id=indian_category.id, preparation_time=10),
                MenuItem(name='印度豆汤', description='印度豆汤，营养丰富', price=32.0, category_id=indian_category.id, preparation_time=20),
                MenuItem(name='印度炒饭', description='印度香料炒饭，香味浓郁', price=38.0, category_id=indian_category.id, preparation_time=15),
                MenuItem(name='印度酸奶', description='印度酸奶，清爽解腻', price=15.0, category_id=indian_category.id, preparation_time=3),
                MenuItem(name='印度甜品', description='印度传统甜品，香甜可口', price=28.0, category_id=indian_category.id, preparation_time=5),
            ])
        
        # 意大利菜 (12种)
        if italian_category:
            new_dishes.extend([
                MenuItem(name='意大利面条', description='经典意大利面条，多种口味', price=58.0, category_id=italian_category.id, preparation_time=15),
                MenuItem(name='玛格丽特披萨', description='经典玛格丽特披萨，简约美味', price=68.0, category_id=italian_category.id, preparation_time=20),
                MenuItem(name='海鲜披萨', description='海鲜披萨，鲜美可口', price=88.0, category_id=italian_category.id, preparation_time=25),
                MenuItem(name='意式烩饭', description='意式烩饭，奶香浓郁', price=78.0, category_id=italian_category.id, preparation_time=25),
                MenuItem(name='意式肉酱面', description='意式肉酱面，香浓可口', price=58.0, category_id=italian_category.id, preparation_time=18),
                MenuItem(name='意式沙拉', description='意式沙拉，清爽健康', price=38.0, category_id=italian_category.id, preparation_time=8),
                MenuItem(name='意式汤', description='意式蔬菜汤，营养丰富', price=32.0, category_id=italian_category.id, preparation_time=15),
                MenuItem(name='提拉米苏', description='经典提拉米苏，香甜浓郁', price=48.0, category_id=italian_category.id, preparation_time=5),
                MenuItem(name='意式咖啡', description='正宗意式咖啡，香浓醇厚', price=28.0, category_id=italian_category.id, preparation_time=5),
                MenuItem(name='意式面包', description='意式面包，香脆可口', price=18.0, category_id=italian_category.id, preparation_time=5),
                MenuItem(name='意式奶酪', description='进口意式奶酪，醇香浓郁', price=58.0, category_id=italian_category.id, preparation_time=3),
                MenuItem(name='意式火腿', description='意式火腿，口感丰富', price=68.0, category_id=italian_category.id, preparation_time=5),
            ])
        
        # 添加到数据库
        for dish in new_dishes:
            db.session.add(dish)
        
        try:
            db.session.commit()
            print(f"成功添加 {len(new_dishes)} 道国际菜品！")
        except Exception as e:
            db.session.rollback()
            print(f"添加菜品失败: {e}")

if __name__ == '__main__':
    add_international_dishes()
