from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from datetime import datetime, date
from werkzeug.security import generate_password_hash, check_password_hash

db = SQLAlchemy()

class User(UserMixin, db.Model):
    """用户模型 - 包括员工和管理员"""
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)
    role = db.Column(db.String(20), nullable=False, default='employee')  # admin, manager, employee, waiter, chef
    name = db.Column(db.String(100), nullable=False)
    phone = db.Column(db.String(20))
    hire_date = db.Column(db.Date, default=date.today)
    is_active = db.Column(db.<PERSON>, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 关系
    orders = db.relationship('Order', backref='employee', lazy=True)
    attendances = db.relationship('Attendance', backref='employee', lazy=True)
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

class Customer(db.Model):
    """客户模型"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    phone = db.Column(db.String(20), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True)
    birthday = db.Column(db.Date)
    points = db.Column(db.Integer, default=0)
    total_spent = db.Column(db.Float, default=0.0)
    preferences = db.Column(db.Text)  # JSON格式存储偏好
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 关系
    orders = db.relationship('Order', backref='customer', lazy=True)
    reservations = db.relationship('Reservation', backref='customer', lazy=True)

class Category(db.Model):
    """菜品分类"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False)
    description = db.Column(db.Text)
    
    # 关系
    menu_items = db.relationship('MenuItem', backref='category', lazy=True)

class MenuItem(db.Model):
    """菜品模型"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    price = db.Column(db.Float, nullable=False)
    category_id = db.Column(db.Integer, db.ForeignKey('category.id'), nullable=False)
    image_url = db.Column(db.String(200))
    is_available = db.Column(db.Boolean, default=True)
    preparation_time = db.Column(db.Integer, default=15)  # 分钟
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class Ingredient(db.Model):
    """原料模型"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    unit = db.Column(db.String(20), nullable=False)  # kg, g, L, ml, 个
    current_stock = db.Column(db.Float, default=0.0)
    min_stock = db.Column(db.Float, default=10.0)
    unit_cost = db.Column(db.Float, default=0.0)
    supplier = db.Column(db.String(100))
    last_updated = db.Column(db.DateTime, default=datetime.utcnow)

class Table(db.Model):
    """桌位模型"""
    id = db.Column(db.Integer, primary_key=True)
    number = db.Column(db.String(10), unique=True, nullable=False)
    capacity = db.Column(db.Integer, nullable=False)
    status = db.Column(db.String(20), default='available')  # available, occupied, reserved, maintenance
    location = db.Column(db.String(50))  # 大厅、包间、露台等
    
    # 关系
    reservations = db.relationship('Reservation', backref='table', lazy=True)
    orders = db.relationship('Order', backref='table', lazy=True)

class Reservation(db.Model):
    """预订模型"""
    id = db.Column(db.Integer, primary_key=True)
    customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'), nullable=False)
    table_id = db.Column(db.Integer, db.ForeignKey('table.id'), nullable=False)
    reservation_date = db.Column(db.Date, nullable=False)
    reservation_time = db.Column(db.Time, nullable=False)
    party_size = db.Column(db.Integer, nullable=False)
    status = db.Column(db.String(20), default='confirmed')  # confirmed, cancelled, completed, no_show
    special_requests = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class Order(db.Model):
    """订单模型"""
    id = db.Column(db.Integer, primary_key=True)
    order_number = db.Column(db.String(20), unique=True, nullable=False)
    customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'))
    table_id = db.Column(db.Integer, db.ForeignKey('table.id'))
    employee_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    status = db.Column(db.String(20), default='pending')  # pending, preparing, ready, served, paid, cancelled
    total_amount = db.Column(db.Float, default=0.0)
    payment_method = db.Column(db.String(20))  # cash, card, mobile
    special_instructions = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    completed_at = db.Column(db.DateTime)
    
    # 关系
    order_items = db.relationship('OrderItem', backref='order', lazy=True, cascade='all, delete-orphan')

class OrderItem(db.Model):
    """订单项目模型"""
    id = db.Column(db.Integer, primary_key=True)
    order_id = db.Column(db.Integer, db.ForeignKey('order.id'), nullable=False)
    menu_item_id = db.Column(db.Integer, db.ForeignKey('menu_item.id'), nullable=False)
    quantity = db.Column(db.Integer, nullable=False)
    unit_price = db.Column(db.Float, nullable=False)
    special_instructions = db.Column(db.Text)
    
    # 关系
    menu_item = db.relationship('MenuItem', backref='order_items')

class Attendance(db.Model):
    """考勤模型"""
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    date = db.Column(db.Date, nullable=False)
    clock_in = db.Column(db.DateTime)
    clock_out = db.Column(db.DateTime)
    break_start = db.Column(db.DateTime)
    break_end = db.Column(db.DateTime)
    total_hours = db.Column(db.Float, default=0.0)
    overtime_hours = db.Column(db.Float, default=0.0)
    status = db.Column(db.String(20), default='present')  # present, absent, late, early_leave

class Schedule(db.Model):
    """排班模型"""
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    date = db.Column(db.Date, nullable=False)
    shift_start = db.Column(db.Time, nullable=False)
    shift_end = db.Column(db.Time, nullable=False)
    position = db.Column(db.String(50))  # 岗位：服务员、厨师、收银员等
    
    # 关系
    employee = db.relationship('User', backref='schedules')
