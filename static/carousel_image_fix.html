<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 轮播图图片修复工具</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .fix-banner {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 3rem;
            border-radius: 1rem;
            text-align: center;
            margin-bottom: 3rem;
            box-shadow: 0 15px 30px rgba(0,0,0,0.3);
        }
        .problem-section {
            background: #f8d7da;
            border-left: 4px solid #dc3545;
            padding: 1.5rem;
            margin: 1rem 0;
            border-radius: 0.5rem;
        }
        .solution-section {
            background: #d4edda;
            border-left: 4px solid #28a745;
            padding: 1.5rem;
            margin: 1rem 0;
            border-radius: 0.5rem;
        }
        .test-carousel {
            margin: 2rem 0;
            border: 3px solid #28a745;
            border-radius: 1rem;
            overflow: hidden;
        }
        .language-switch {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
        
        /* 修复后的轮播图CSS */
        .fixed-carousel .carousel-item {
            position: relative;
            float: left;
            width: 100%;
            margin-right: -100%;
            -webkit-backface-visibility: hidden;
            backface-visibility: hidden;
            transition: transform 0.6s ease-in-out;
        }
        
        .fixed-carousel .carousel-item.active {
            display: block !important;
            opacity: 1 !important;
            visibility: visible !important;
        }
        
        .fixed-carousel .carousel-item img {
            height: 400px !important;
            object-fit: cover !important;
            width: 100% !important;
            display: block !important;
            opacity: 1 !important;
            visibility: visible !important;
        }
    </style>
</head>
<body>
    <!-- Language Switch -->
    <div class="language-switch">
        <div class="btn-group">
            <a href="/set_language/en" class="btn btn-outline-primary btn-sm">English</a>
            <a href="/set_language/zh" class="btn btn-outline-success btn-sm">中文</a>
        </div>
    </div>

    <div class="container py-5">
        <!-- Fix Banner -->
        <div class="fix-banner">
            <h1 class="display-2">🔧 轮播图图片修复工具</h1>
            <h2 class="mb-4">解决"图片短暂显示后消失"问题</h2>
            <p class="lead">CSS冲突导致的图片显示问题已修复</p>
        </div>

        <!-- Problem Analysis -->
        <div class="card mb-4">
            <div class="card-header bg-danger text-white">
                <h4><i class="fas fa-bug"></i> 问题分析</h4>
            </div>
            <div class="card-body">
                <div class="problem-section">
                    <h5><i class="fas fa-exclamation-triangle"></i> 发现的问题</h5>
                    <p><strong>症状:</strong> 使用Ctrl+F5时，图片短暂出现然后消失</p>
                    <p><strong>根本原因:</strong> CSS规则冲突</p>
                    <ul>
                        <li><code>.carousel-item { display: none; }</code> - Bootstrap默认隐藏非活动项</li>
                        <li><code>.carousel-item { display: block !important; }</code> - 我们的修复CSS强制显示</li>
                        <li><code>height: auto</code> 与 <code>height: 400px</code> 冲突</li>
                        <li>JavaScript多次设置样式导致闪烁</li>
                    </ul>
                </div>

                <div class="solution-section">
                    <h5><i class="fas fa-wrench"></i> 解决方案</h5>
                    <p><strong>已修复的问题:</strong></p>
                    <ul>
                        <li>移除了冲突的 <code>display: none</code> 规则</li>
                        <li>移除了冲突的 <code>height: auto</code> 规则</li>
                        <li>优化了CSS选择器优先级</li>
                        <li>确保只有活动的轮播项显示</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Test Carousel -->
        <div class="card mb-4">
            <div class="card-header bg-success text-white">
                <h4><i class="fas fa-test-tube"></i> 修复后的轮播图测试</h4>
            </div>
            <div class="card-body">
                <div class="test-carousel">
                    <div id="testCarousel" class="carousel slide fixed-carousel" data-bs-ride="carousel">
                        <div class="carousel-indicators">
                            <button type="button" data-bs-target="#testCarousel" data-bs-slide-to="0" class="active" aria-current="true"></button>
                            <button type="button" data-bs-target="#testCarousel" data-bs-slide-to="1"></button>
                            <button type="button" data-bs-target="#testCarousel" data-bs-slide-to="2"></button>
                        </div>
                        <div class="carousel-inner">
                            <div class="carousel-item active">
                                <img src="/static/images/homepage/featured/fifa_world_cup_featured.jpg" class="d-block w-100" alt="FIFA World Cup 2026">
                                <div class="carousel-caption d-none d-md-block">
                                    <h5>FIFA World Cup 2026</h5>
                                    <p>测试图片 - 应该稳定显示，不会消失</p>
                                </div>
                            </div>
                            <div class="carousel-item">
                                <img src="/static/images/homepage/featured/champions_league_featured.jpg" class="d-block w-100" alt="Champions League">
                                <div class="carousel-caption d-none d-md-block">
                                    <h5>UEFA Champions League</h5>
                                    <p>测试图片 - 应该稳定显示，不会消失</p>
                                </div>
                            </div>
                            <div class="carousel-item">
                                <img src="/static/images/homepage/featured/nba_finals_featured.jpg" class="d-block w-100" alt="NBA Finals">
                                <div class="carousel-caption d-none d-md-block">
                                    <h5>NBA Finals 2025</h5>
                                    <p>测试图片 - 应该稳定显示，不会消失</p>
                                </div>
                            </div>
                        </div>
                        <button class="carousel-control-prev" type="button" data-bs-target="#testCarousel" data-bs-slide="prev">
                            <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                            <span class="visually-hidden">Previous</span>
                        </button>
                        <button class="carousel-control-next" type="button" data-bs-target="#testCarousel" data-bs-slide="next">
                            <span class="carousel-control-next-icon" aria-hidden="true"></span>
                            <span class="visually-hidden">Next</span>
                        </button>
                    </div>
                </div>
                
                <div class="alert alert-info mt-3">
                    <h6><i class="fas fa-info-circle"></i> 测试说明:</h6>
                    <ul class="small mb-0">
                        <li>如果图片稳定显示 → 修复成功</li>
                        <li>如果图片仍然闪烁 → 需要进一步调试</li>
                        <li>尝试按Ctrl+F5刷新页面测试</li>
                        <li>轮播图应该能正常切换</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Solution Steps -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h4><i class="fas fa-tools"></i> 修复步骤详解</h4>
            </div>
            <div class="card-body">
                <div class="solution-section">
                    <h6>步骤1: 修复CSS冲突</h6>
                    <p>移除了以下冲突规则:</p>
                    <pre><code>/* 移除前 - 有冲突 */
.carousel-item { display: none; }
.carousel-item { display: block !important; }

/* 修复后 - 无冲突 */
.carousel-item.active { display: block !important; }</code></pre>
                </div>

                <div class="solution-section">
                    <h6>步骤2: 修复高度冲突</h6>
                    <p>移除了height属性冲突:</p>
                    <pre><code>/* 移除前 - 有冲突 */
.carousel-item img { height: auto !important; }
.carousel-item img { height: 400px !important; }

/* 修复后 - 无冲突 */
.carousel-item img { height: 400px !important; object-fit: cover !important; }</code></pre>
                </div>

                <div class="solution-section">
                    <h6>步骤3: 优化JavaScript</h6>
                    <p>JavaScript代码已优化，减少重复设置样式</p>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="text-center mt-5">
            <h3>🎯 测试修复效果</h3>
            <div class="d-flex justify-content-center flex-wrap">
                <a href="/" target="_blank" class="btn btn-primary btn-lg m-2">
                    <i class="fas fa-home"></i> 查看主页轮播图
                </a>
                <button onclick="location.reload()" class="btn btn-warning btn-lg m-2">
                    <i class="fas fa-sync-alt"></i> 刷新测试页面
                </button>
                <button onclick="hardRefresh()" class="btn btn-danger btn-lg m-2">
                    <i class="fas fa-redo"></i> 硬刷新 (Ctrl+F5)
                </button>
                <button onclick="testCarouselFunction()" class="btn btn-success btn-lg m-2">
                    <i class="fas fa-play"></i> 测试轮播功能
                </button>
            </div>
        </div>

        <!-- Status Display -->
        <div class="alert alert-info mt-4" id="statusDisplay">
            <h6><i class="fas fa-info-circle"></i> 状态监控:</h6>
            <div id="imageStatus">正在检测图片状态...</div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function hardRefresh() {
            location.reload(true);
        }

        function testCarouselFunction() {
            const carousel = new bootstrap.Carousel(document.getElementById('testCarousel'));
            let slideIndex = 0;
            
            const testInterval = setInterval(() => {
                carousel.next();
                slideIndex++;
                
                if (slideIndex >= 3) {
                    clearInterval(testInterval);
                    document.getElementById('statusDisplay').innerHTML = 
                        '<h6><i class="fas fa-check-circle text-success"></i> 轮播测试完成!</h6>' +
                        '<div>如果所有图片都正常显示，说明修复成功。</div>';
                }
            }, 2000);
        }

        // 监控图片加载状态
        document.addEventListener('DOMContentLoaded', function() {
            const images = document.querySelectorAll('#testCarousel img');
            let loadedCount = 0;
            let errorCount = 0;
            
            images.forEach((img, index) => {
                img.addEventListener('load', function() {
                    loadedCount++;
                    updateStatus();
                    console.log(`Image ${index + 1} loaded successfully:`, this.src);
                });
                
                img.addEventListener('error', function() {
                    errorCount++;
                    updateStatus();
                    console.log(`Image ${index + 1} failed to load:`, this.src);
                });
            });
            
            function updateStatus() {
                const statusDiv = document.getElementById('imageStatus');
                statusDiv.innerHTML = `
                    <div>总图片数: ${images.length}</div>
                    <div>已加载: <span class="text-success">${loadedCount}</span></div>
                    <div>加载失败: <span class="text-danger">${errorCount}</span></div>
                    <div>状态: ${loadedCount === images.length ? '<span class="text-success">✅ 全部加载成功</span>' : 
                                errorCount > 0 ? '<span class="text-danger">❌ 有图片加载失败</span>' : 
                                '<span class="text-warning">⏳ 正在加载...</span>'}</div>
                `;
            }
            
            // 初始状态更新
            setTimeout(updateStatus, 100);
        });

        // 页面加载完成后的检查
        window.addEventListener('load', function() {
            console.log('🔧 轮播图修复工具已加载');
            console.log('📍 检查图片显示状态...');
            
            setTimeout(() => {
                const visibleImages = document.querySelectorAll('#testCarousel .carousel-item.active img');
                if (visibleImages.length > 0) {
                    console.log('✅ 轮播图图片正常显示');
                } else {
                    console.log('❌ 轮播图图片未显示');
                }
            }, 1000);
        });
    </script>
</body>
</html>
