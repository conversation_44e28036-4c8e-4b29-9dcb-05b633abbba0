<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试测试页面</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .test-section {
            margin-bottom: 2rem;
            padding: 1rem;
            border: 1px solid #ddd;
            border-radius: 0.5rem;
        }
        .success { background-color: #d4edda; }
        .error { background-color: #f8d7da; }
        .warning { background-color: #fff3cd; }
    </style>
</head>
<body>
    <div class="container py-5">
        <h1 class="text-center mb-5">🔍 网站调试测试</h1>
        
        <!-- 基本连接测试 -->
        <div class="test-section success">
            <h3>✅ 基本连接测试</h3>
            <p>如果你能看到这个页面，说明Flask应用运行正常，静态文件服务正常。</p>
            <p><strong>访问地址：</strong> http://127.0.0.1:5000/static/debug_test.html</p>
        </div>

        <!-- 主页链接测试 -->
        <div class="test-section">
            <h3>🏠 主页链接测试</h3>
            <p>点击下面的链接测试主页是否正常：</p>
            <a href="/" class="btn btn-primary" target="_blank">测试主页</a>
            <p class="mt-2"><small>如果主页无法加载，可能是模板或数据问题</small></p>
        </div>

        <!-- 赛事列表测试 -->
        <div class="test-section">
            <h3>📅 赛事列表测试</h3>
            <p>点击下面的链接测试赛事列表页面：</p>
            <a href="/events" class="btn btn-success" target="_blank">测试赛事列表</a>
            <p class="mt-2"><small>如果赛事列表无法加载，可能是事件数据问题</small></p>
        </div>

        <!-- 具体赛事测试 -->
        <div class="test-section">
            <h3>🏆 具体赛事测试</h3>
            <p>点击下面的链接测试具体赛事页面：</p>
            <div class="d-flex gap-2 flex-wrap">
                <a href="/events/1" class="btn btn-outline-primary btn-sm" target="_blank">FIFA世界杯2026</a>
                <a href="/events/2" class="btn btn-outline-primary btn-sm" target="_blank">欧冠决赛</a>
                <a href="/events/3" class="btn btn-outline-primary btn-sm" target="_blank">英超联赛</a>
                <a href="/events/4" class="btn btn-outline-primary btn-sm" target="_blank">西甲联赛</a>
                <a href="/events/5" class="btn btn-outline-primary btn-sm" target="_blank">意甲联赛</a>
            </div>
            <p class="mt-2"><small>如果某个赛事页面无法加载，可能是该赛事的数据有问题</small></p>
        </div>

        <!-- 图片测试 -->
        <div class="test-section">
            <h3>🖼️ 图片测试</h3>
            <p>测试几张关键图片是否能正常显示：</p>
            <div class="row g-3">
                <div class="col-md-4">
                    <img src="/static/images/events/fifa_world_cup_2026.jpg" class="img-fluid" alt="FIFA世界杯2026" 
                         style="max-height: 150px; width: 100%; object-fit: cover;"
                         onload="this.parentElement.classList.add('border-success')" 
                         onerror="this.parentElement.classList.add('border-danger')">
                    <p class="small mt-1">FIFA世界杯2026</p>
                </div>
                <div class="col-md-4">
                    <img src="/static/images/events/champions_league.jpg" class="img-fluid" alt="欧冠" 
                         style="max-height: 150px; width: 100%; object-fit: cover;"
                         onload="this.parentElement.classList.add('border-success')" 
                         onerror="this.parentElement.classList.add('border-danger')">
                    <p class="small mt-1">欧冠决赛</p>
                </div>
                <div class="col-md-4">
                    <img src="/static/images/events/nba_finals.jpg" class="img-fluid" alt="NBA" 
                         style="max-height: 150px; width: 100%; object-fit: cover;"
                         onload="this.parentElement.classList.add('border-success')" 
                         onerror="this.parentElement.classList.add('border-danger')">
                    <p class="small mt-1">NBA总决赛</p>
                </div>
            </div>
            <p class="mt-2"><small>绿色边框=图片加载成功，红色边框=图片加载失败</small></p>
        </div>

        <!-- JavaScript测试 -->
        <div class="test-section">
            <h3>⚙️ JavaScript测试</h3>
            <button onclick="testJS()" class="btn btn-warning">测试JavaScript功能</button>
            <div id="js-result" class="mt-2"></div>
        </div>

        <!-- 网络连接测试 -->
        <div class="test-section">
            <h3>🌐 网络连接测试</h3>
            <p>测试外部资源加载：</p>
            <div id="network-test">
                <p>正在测试Bootstrap CSS...</p>
                <p>正在测试Font Awesome图标...</p>
            </div>
        </div>

        <!-- 常见问题解决方案 -->
        <div class="test-section warning">
            <h3>⚠️ 常见问题解决方案</h3>
            <ul>
                <li><strong>页面无法加载：</strong> 检查Flask应用是否在运行 (http://127.0.0.1:5000)</li>
                <li><strong>图片不显示：</strong> 检查static/images/events/文件夹中的图片文件</li>
                <li><strong>亮点不显示：</strong> 检查app.py中的highlights分配</li>
                <li><strong>历史数据不显示：</strong> 检查app.py中的historical_data分配</li>
                <li><strong>样式错误：</strong> 检查Bootstrap CSS是否正确加载</li>
            </ul>
        </div>
    </div>

    <script>
        function testJS() {
            const result = document.getElementById('js-result');
            result.innerHTML = '<div class="alert alert-success">✅ JavaScript功能正常！</div>';
        }

        // 测试网络连接
        window.onload = function() {
            const networkTest = document.getElementById('network-test');
            
            // 检查Bootstrap
            if (window.getComputedStyle(document.body).fontFamily.includes('system-ui')) {
                networkTest.innerHTML += '<p class="text-success">✅ Bootstrap CSS加载成功</p>';
            } else {
                networkTest.innerHTML += '<p class="text-danger">❌ Bootstrap CSS加载失败</p>';
            }
            
            // 检查Font Awesome
            const testIcon = document.createElement('i');
            testIcon.className = 'fas fa-home';
            document.body.appendChild(testIcon);
            
            setTimeout(() => {
                const iconStyle = window.getComputedStyle(testIcon);
                if (iconStyle.fontFamily.includes('Font Awesome')) {
                    networkTest.innerHTML += '<p class="text-success">✅ Font Awesome图标加载成功</p>';
                } else {
                    networkTest.innerHTML += '<p class="text-danger">❌ Font Awesome图标加载失败</p>';
                }
                document.body.removeChild(testIcon);
            }, 1000);
        };
    </script>
</body>
</html>
