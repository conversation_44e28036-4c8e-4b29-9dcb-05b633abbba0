<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 排版问题诊断修复</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .layout-card {
            border: 3px solid #dc3545;
            margin-bottom: 2rem;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        .layout-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.2);
        }
        .layout-banner {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 3rem;
            border-radius: 1rem;
            text-align: center;
            margin-bottom: 3rem;
            box-shadow: 0 15px 30px rgba(0,0,0,0.3);
        }
        .test-result {
            padding: 1rem;
            margin: 0.5rem 0;
            border-radius: 0.5rem;
            font-family: monospace;
        }
        .test-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .test-error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .solution-box {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin: 1rem 0;
        }
        .emergency-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <!-- Layout Banner -->
        <div class="layout-banner">
            <h1 class="display-2">🔧 排版问题诊断修复</h1>
            <h2 class="mb-4">解决网页竖向排列问题</h2>
            <p class="lead">图片显示正常，但CSS样式加载有问题</p>
        </div>

        <!-- Problem Analysis -->
        <div class="card layout-card">
            <div class="card-header bg-danger text-white">
                <h4><i class="fas fa-exclamation-triangle"></i> 问题分析</h4>
            </div>
            <div class="card-body">
                <div class="solution-box">
                    <h5><i class="fas fa-search"></i> 排版问题的常见原因:</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <h6>🔴 CSS加载问题:</h6>
                            <ul class="small">
                                <li>Bootstrap CSS文件加载失败</li>
                                <li>自定义CSS文件404错误</li>
                                <li>CDN链接访问问题</li>
                                <li>缓存清理导致样式丢失</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>🔴 网络问题:</h6>
                            <ul class="small">
                                <li>外部CDN连接失败</li>
                                <li>网络延迟导致CSS加载超时</li>
                                <li>防火墙阻止外部资源</li>
                                <li>DNS解析问题</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- CSS Diagnosis -->
        <div class="card layout-card">
            <div class="card-header bg-warning text-dark">
                <h4><i class="fas fa-code"></i> CSS加载诊断</h4>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5>CSS资源检测</h5>
                    <button class="btn btn-warning btn-lg" onclick="diagnoseCSSLoading()">
                        <i class="fas fa-play"></i> 检测CSS加载
                    </button>
                </div>
                
                <div id="cssResults">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> 点击按钮检测CSS文件加载状态
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Fixes -->
        <div class="card layout-card">
            <div class="card-header bg-success text-white">
                <h4><i class="fas fa-tools"></i> 快速修复方案</h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="emergency-box">
                            <h6><i class="fas fa-rocket"></i> 方案1: 强制重新加载CSS</h6>
                            <ol class="small">
                                <li>按 <kbd>F12</kbd> 打开开发者工具</li>
                                <li>切换到 <strong>Network</strong> 标签</li>
                                <li>勾选 <strong>"Disable cache"</strong></li>
                                <li>按 <kbd>Ctrl+Shift+R</kbd> 强制刷新</li>
                                <li>等待所有CSS文件重新加载</li>
                            </ol>
                            <button class="btn btn-success btn-sm" onclick="forceReloadCSS()">执行方案1</button>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="emergency-box">
                            <h6><i class="fas fa-sync"></i> 方案2: 清除所有缓存</h6>
                            <ol class="small">
                                <li>按 <kbd>Ctrl+Shift+Delete</kbd></li>
                                <li>选择 <strong>"所有时间"</strong></li>
                                <li>勾选所有缓存选项</li>
                                <li>点击 <strong>"清除数据"</strong></li>
                                <li>重新访问网站</li>
                            </ol>
                            <button class="btn btn-warning btn-sm" onclick="clearAllCache()">执行方案2</button>
                        </div>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-md-6">
                        <div class="emergency-box">
                            <h6><i class="fas fa-shield-alt"></i> 方案3: 使用隐私模式</h6>
                            <ol class="small">
                                <li>按 <kbd>Ctrl+Shift+N</kbd> (Chrome)</li>
                                <li>或 <kbd>Ctrl+Shift+P</kbd> (Firefox)</li>
                                <li>在隐私窗口中访问网站</li>
                                <li>检查排版是否正常</li>
                            </ol>
                            <button class="btn btn-info btn-sm" onclick="openIncognito()">执行方案3</button>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="emergency-box">
                            <h6><i class="fas fa-redo"></i> 方案4: 重启浏览器</h6>
                            <ol class="small">
                                <li>完全关闭浏览器</li>
                                <li>等待5秒钟</li>
                                <li>重新打开浏览器</li>
                                <li>访问网站</li>
                            </ol>
                            <button class="btn btn-secondary btn-sm" onclick="restartBrowser()">执行方案4</button>
                        </div>
                    </div>
                </div>

                <div id="quickFixResults" class="mt-3"></div>
            </div>
        </div>

        <!-- Manual Check -->
        <div class="card layout-card">
            <div class="card-header bg-info text-white">
                <h4><i class="fas fa-eye"></i> 手动检查步骤</h4>
            </div>
            <div class="card-body">
                <div class="solution-box">
                    <h5><i class="fas fa-search"></i> 如何检查CSS加载问题:</h5>
                    
                    <h6>步骤1: 检查开发者工具</h6>
                    <ol>
                        <li>在主页按 <kbd>F12</kbd> 打开开发者工具</li>
                        <li>切换到 <strong>Console</strong> 标签</li>
                        <li>查看是否有红色错误信息，特别是CSS相关的404错误</li>
                        <li>切换到 <strong>Network</strong> 标签</li>
                        <li>刷新页面，查看CSS文件是否成功加载（状态码200）</li>
                    </ol>
                    
                    <h6>步骤2: 检查页面源代码</h6>
                    <ol>
                        <li>右键点击页面 → <strong>"查看页面源代码"</strong></li>
                        <li>查找 <code>&lt;link rel="stylesheet"</code> 标签</li>
                        <li>确认Bootstrap CSS链接是否正确</li>
                        <li>点击CSS链接测试是否能访问</li>
                    </ol>
                </div>
            </div>
        </div>

        <!-- Emergency CSS -->
        <div class="card layout-card">
            <div class="card-header bg-primary text-white">
                <h4><i class="fas fa-first-aid"></i> 紧急CSS修复</h4>
            </div>
            <div class="card-body">
                <div class="emergency-box">
                    <h5><i class="fas fa-magic"></i> 如果上述方法都无效，尝试紧急CSS修复:</h5>
                    
                    <h6>临时解决方案:</h6>
                    <p>如果Bootstrap CSS无法加载，我们可以使用本地CSS文件或者不同的CDN源。</p>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h6>备用CDN链接:</h6>
                            <ul class="small">
                                <li>jsDelivr CDN</li>
                                <li>unpkg CDN</li>
                                <li>cdnjs CDN</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>本地CSS选项:</h6>
                            <ul class="small">
                                <li>下载Bootstrap到本地</li>
                                <li>使用简化的CSS</li>
                                <li>创建基础布局CSS</li>
                            </ul>
                        </div>
                    </div>
                    
                    <button class="btn btn-primary" onclick="createEmergencyCSS()">
                        <i class="fas fa-download"></i> 创建紧急CSS修复
                    </button>
                </div>
            </div>
        </div>

        <!-- Test Links -->
        <div class="text-center mt-5">
            <h3>🎯 测试链接</h3>
            <div class="d-flex justify-content-center flex-wrap">
                <a href="/" target="_blank" class="btn btn-danger btn-lg m-2">
                    <i class="fas fa-home"></i> 主页 (检查排版)
                </a>
                <a href="/events" target="_blank" class="btn btn-warning btn-lg m-2">
                    <i class="fas fa-calendar"></i> 赛事列表
                </a>
                <a href="/static/image_fix_complete.html" target="_blank" class="btn btn-success btn-lg m-2">
                    <i class="fas fa-images"></i> 图片修复页面
                </a>
            </div>
        </div>

        <!-- Expected Results -->
        <div class="alert alert-success mt-4">
            <h5><i class="fas fa-check-circle"></i> 修复成功的标志:</h5>
            <div class="row">
                <div class="col-md-6">
                    <h6>✅ 排版正常:</h6>
                    <ul class="small mb-0">
                        <li>导航栏水平排列</li>
                        <li>轮播图正确显示</li>
                        <li>分类卡片网格布局</li>
                        <li>响应式设计正常</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>✅ 样式正常:</h6>
                    <ul class="small mb-0">
                        <li>按钮样式正确</li>
                        <li>卡片阴影效果</li>
                        <li>颜色主题正确</li>
                        <li>字体和间距正常</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // CSS加载诊断
        async function diagnoseCSSLoading() {
            const resultsDiv = document.getElementById('cssResults');
            resultsDiv.innerHTML = '<div class="alert alert-info"><i class="fas fa-spinner fa-spin"></i> 正在检测CSS加载...</div>';
            
            const cssTests = [
                { name: 'Bootstrap CSS (CDN)', url: 'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' },
                { name: 'Font Awesome CSS', url: 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' },
                { name: '主页', url: '/' },
                { name: '静态文件服务', url: '/static/favicon.ico' }
            ];
            
            let results = '';
            let successCount = 0;
            
            for (const test of cssTests) {
                try {
                    const response = await fetch(test.url, { method: 'HEAD' });
                    const success = response.ok;
                    const resultClass = success ? 'test-success' : 'test-error';
                    const resultIcon = success ? '✅' : '❌';
                    const statusText = `${response.status} ${response.statusText}`;
                    
                    if (success) successCount++;
                    
                    results += `
                        <div class="test-result ${resultClass}">
                            ${resultIcon} ${test.name} - ${statusText}
                        </div>
                    `;
                } catch (error) {
                    results += `
                        <div class="test-result test-error">
                            ❌ ${test.name} - Network Error: ${error.message}
                        </div>
                    `;
                }
            }
            
            // 检查当前页面的CSS
            const stylesheets = document.styleSheets;
            results += `
                <div class="test-result test-success">
                    ✅ 当前页面加载的CSS文件数量: ${stylesheets.length}
                </div>
            `;
            
            const summary = `
                <div class="alert ${successCount >= 3 ? 'alert-success' : 'alert-danger'}">
                    <h6>CSS加载检测完成</h6>
                    <p><strong>成功:</strong> ${successCount}/${cssTests.length}</p>
                    <p><strong>状态:</strong> ${successCount >= 3 ? '✅ CSS加载正常' : '❌ CSS加载有问题'}</p>
                </div>
            `;
            
            resultsDiv.innerHTML = summary + results;
        }

        // 快速修复方法
        function forceReloadCSS() {
            const resultsDiv = document.getElementById('quickFixResults');
            resultsDiv.innerHTML = `
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> 
                    <strong>执行方案1:</strong><br>
                    1. 按 F12 打开开发者工具<br>
                    2. 切换到 Network 标签<br>
                    3. 勾选 "Disable cache"<br>
                    4. 按 Ctrl+Shift+R 强制刷新<br>
                    5. 等待所有资源重新加载
                </div>
            `;
        }

        function clearAllCache() {
            const resultsDiv = document.getElementById('quickFixResults');
            resultsDiv.innerHTML = `
                <div class="alert alert-warning">
                    <i class="fas fa-sync"></i> 
                    <strong>执行方案2:</strong><br>
                    1. 按 Ctrl+Shift+Delete 打开清除数据<br>
                    2. 选择 "所有时间"<br>
                    3. 勾选所有缓存选项<br>
                    4. 点击 "清除数据"<br>
                    5. 重新访问网站
                </div>
            `;
        }

        function openIncognito() {
            const resultsDiv = document.getElementById('quickFixResults');
            resultsDiv.innerHTML = `
                <div class="alert alert-info">
                    <i class="fas fa-shield-alt"></i> 
                    <strong>执行方案3:</strong><br>
                    1. 按 Ctrl+Shift+N (Chrome) 或 Ctrl+Shift+P (Firefox)<br>
                    2. 在隐私窗口中访问: <a href="/" target="_blank">http://127.0.0.1:5000/</a><br>
                    3. 检查排版是否正常
                </div>
            `;
        }

        function restartBrowser() {
            const resultsDiv = document.getElementById('quickFixResults');
            resultsDiv.innerHTML = `
                <div class="alert alert-secondary">
                    <i class="fas fa-redo"></i> 
                    <strong>执行方案4:</strong><br>
                    1. 完全关闭浏览器<br>
                    2. 等待5秒钟<br>
                    3. 重新打开浏览器<br>
                    4. 访问网站
                </div>
            `;
        }

        function createEmergencyCSS() {
            const resultsDiv = document.getElementById('quickFixResults');
            resultsDiv.innerHTML = `
                <div class="alert alert-primary">
                    <i class="fas fa-download"></i> 
                    <strong>紧急CSS修复:</strong><br>
                    如果Bootstrap CDN无法访问，我们可以创建本地CSS文件。<br>
                    这需要在服务器端操作，请告诉我是否需要创建本地CSS备份。
                </div>
            `;
        }

        // 页面加载时自动检查
        window.addEventListener('load', function() {
            console.log('🔧 排版问题诊断页面已加载');
            console.log('💡 如果这个页面排版正常，说明CSS可以加载');
            console.log('🎯 问题可能在于主页的特定CSS或缓存问题');
            
            // 检查Bootstrap是否加载
            if (typeof bootstrap !== 'undefined') {
                console.log('✅ Bootstrap JavaScript已加载');
            } else {
                console.warn('⚠️ Bootstrap JavaScript未加载');
            }
        });
    </script>
</body>
</html>
