<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📸 图片替换详细教程</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .tutorial-card {
            border: 3px solid #007bff;
            margin-bottom: 2rem;
            transition: transform 0.3s, box-shadow 0.3s;
            background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
        }
        .tutorial-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.2);
        }
        .tutorial-banner {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 4rem;
            border-radius: 1rem;
            text-align: center;
            margin-bottom: 3rem;
            box-shadow: 0 15px 30px rgba(0,0,0,0.3);
        }
        .step-badge {
            background: #28a745;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 2rem;
            font-size: 1rem;
            font-weight: bold;
            margin: 0.5rem;
            display: inline-block;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        .tutorial-section {
            background: #f8f9fa;
            border-radius: 1rem;
            padding: 2rem;
            margin: 2rem 0;
            border-left: 5px solid #007bff;
        }
        .step-number {
            background: #007bff;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 1rem;
            flex-shrink: 0;
        }
        .file-path {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 0.5rem;
            font-family: monospace;
            border: 1px solid #dee2e6;
            margin: 1rem 0;
        }
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 0.5rem;
            font-family: monospace;
            margin: 1rem 0;
        }
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 1rem 0;
        }
        .success-box {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 1rem 0;
        }
        .image-example {
            border: 2px dashed #dee2e6;
            padding: 1rem;
            text-align: center;
            border-radius: 0.5rem;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <!-- Tutorial Banner -->
        <div class="tutorial-banner">
            <h1 class="display-2">📸 图片替换详细教程</h1>
            <h2 class="mb-4">一步一步教您如何替换主页图片</h2>
            <p class="lead">从准备图片到完成替换的完整指南</p>
            <div class="mt-4">
                <span class="step-badge">准备图片</span>
                <span class="step-badge">找到文件</span>
                <span class="step-badge">替换文件</span>
                <span class="step-badge">验证效果</span>
            </div>
        </div>

        <!-- Step 1: Prepare Images -->
        <div class="card tutorial-card">
            <div class="card-header bg-primary text-white">
                <h4><i class="fas fa-image"></i> 步骤1: 准备您的图片</h4>
            </div>
            <div class="card-body">
                <div class="d-flex align-items-start mb-3">
                    <div class="step-number">1</div>
                    <div>
                        <h5>选择或准备图片文件</h5>
                        <p>根据您要替换的图片类型，准备相应尺寸的图片：</p>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="image-example">
                            <h6><i class="fas fa-desktop"></i> 精选赛事轮播图</h6>
                            <p><strong>推荐尺寸:</strong> 1200 x 400 像素</p>
                            <p><strong>比例:</strong> 3:1 (横幅)</p>
                            <p><strong>用途:</strong> 主页顶部大图</p>
                            <div class="warning-box">
                                <i class="fas fa-exclamation-triangle"></i>
                                <strong>注意:</strong> 确保图片有足够对比度显示白色文字
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="image-example">
                            <h6><i class="fas fa-th-large"></i> 分类浏览卡片</h6>
                            <p><strong>推荐尺寸:</strong> 400 x 250 像素</p>
                            <p><strong>比例:</strong> 8:5 (卡片)</p>
                            <p><strong>用途:</strong> 分类卡片顶部</p>
                            <div class="warning-box">
                                <i class="fas fa-exclamation-triangle"></i>
                                <strong>注意:</strong> 保持风格一致，避免过于复杂的背景
                            </div>
                        </div>
                    </div>
                </div>

                <h6 class="mt-4">图片要求:</h6>
                <ul>
                    <li><strong>格式:</strong> JPG, JPEG, 或 PNG</li>
                    <li><strong>文件大小:</strong> 建议小于 500KB (加载速度)</li>
                    <li><strong>质量:</strong> 高清晰度，避免模糊</li>
                    <li><strong>内容:</strong> 与体育赛事相关的图片</li>
                </ul>
            </div>
        </div>

        <!-- Step 2: Locate Files -->
        <div class="card tutorial-card">
            <div class="card-header bg-success text-white">
                <h4><i class="fas fa-folder-open"></i> 步骤2: 找到要替换的文件</h4>
            </div>
            <div class="card-body">
                <div class="d-flex align-items-start mb-3">
                    <div class="step-number">2</div>
                    <div>
                        <h5>定位图片文件位置</h5>
                        <p>根据您要替换的图片类型，找到对应的文件夹：</p>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-star"></i> 精选赛事图片位置:</h6>
                        <div class="file-path">
                            <strong>文件夹路径:</strong><br>
                            static/images/homepage/featured/
                        </div>
                        <p><strong>包含的文件:</strong></p>
                        <ul class="small">
                            <li>fifa_world_cup_featured.jpg</li>
                            <li>champions_league_featured.jpg</li>
                            <li>nba_finals_featured.jpg</li>
                            <li>wimbledon_featured.jpg</li>
                            <li>olympic_swimming_featured.jpg</li>
                            <li>masters_golf_featured.jpg</li>
                            <li>monaco_gp_featured.jpg</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-th-large"></i> 分类图片位置:</h6>
                        <div class="file-path">
                            <strong>文件夹路径:</strong><br>
                            static/images/homepage/categories/
                        </div>
                        <p><strong>包含的文件:</strong></p>
                        <ul class="small">
                            <li>football_category.jpg</li>
                            <li>basketball_category.jpg</li>
                            <li>tennis_category.jpg</li>
                            <li>swimming_category.jpg</li>
                            <li>athletics_category.jpg</li>
                            <li>golf_category.jpg</li>
                            <li>formula_1_category.jpg</li>
                        </ul>
                    </div>
                </div>

                <div class="success-box">
                    <i class="fas fa-lightbulb"></i>
                    <strong>提示:</strong> 您可以使用文件管理器 (Windows资源管理器、Mac Finder) 或代码编辑器来访问这些文件夹
                </div>
            </div>
        </div>

        <!-- Step 3: Replace Files -->
        <div class="card tutorial-card">
            <div class="card-header bg-warning text-dark">
                <h4><i class="fas fa-exchange-alt"></i> 步骤3: 替换图片文件</h4>
            </div>
            <div class="card-body">
                <div class="d-flex align-items-start mb-3">
                    <div class="step-number">3</div>
                    <div>
                        <h5>执行文件替换操作</h5>
                        <p>这是最关键的步骤，请仔细按照以下方法操作：</p>
                    </div>
                </div>

                <div class="tutorial-section">
                    <h6><i class="fas fa-desktop"></i> 方法1: 使用文件管理器 (推荐)</h6>
                    <ol>
                        <li><strong>打开文件管理器</strong>
                            <ul>
                                <li>Windows: 打开"文件资源管理器"</li>
                                <li>Mac: 打开"访达 (Finder)"</li>
                                <li>Linux: 打开文件管理器</li>
                            </ul>
                        </li>
                        <li><strong>导航到项目文件夹</strong>
                            <div class="file-path">
                                找到您的项目根目录，然后进入:<br>
                                static → images → homepage → featured (或 categories)
                            </div>
                        </li>
                        <li><strong>选择要替换的文件</strong>
                            <ul>
                                <li>找到您要替换的图片文件</li>
                                <li>例如: fifa_world_cup_featured.jpg</li>
                            </ul>
                        </li>
                        <li><strong>替换文件</strong>
                            <ul>
                                <li>将您的新图片重命名为相同的文件名</li>
                                <li>复制新图片到该文件夹</li>
                                <li>选择"替换"或"覆盖"现有文件</li>
                            </ul>
                        </li>
                    </ol>
                </div>

                <div class="tutorial-section">
                    <h6><i class="fas fa-code"></i> 方法2: 使用代码编辑器</h6>
                    <ol>
                        <li><strong>打开代码编辑器</strong> (VS Code, Sublime Text, 等)</li>
                        <li><strong>打开项目文件夹</strong></li>
                        <li><strong>在侧边栏中导航</strong> 到 static/images/homepage/</li>
                        <li><strong>右键点击要替换的文件</strong></li>
                        <li><strong>选择"删除"</strong> 旧文件</li>
                        <li><strong>将新图片拖拽</strong> 到相同位置</li>
                        <li><strong>重命名</strong> 为原来的文件名</li>
                    </ol>
                </div>

                <div class="warning-box">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>重要提醒:</strong>
                    <ul class="mb-0">
                        <li>必须保持相同的文件名 (包括扩展名)</li>
                        <li>确保文件路径正确</li>
                        <li>建议先备份原文件</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Step 4: Verify Results -->
        <div class="card tutorial-card">
            <div class="card-header bg-info text-white">
                <h4><i class="fas fa-check-circle"></i> 步骤4: 验证替换效果</h4>
            </div>
            <div class="card-body">
                <div class="d-flex align-items-start mb-3">
                    <div class="step-number">4</div>
                    <div>
                        <h5>检查图片是否正确显示</h5>
                        <p>替换完成后，需要验证图片是否正确显示：</p>
                    </div>
                </div>

                <div class="tutorial-section">
                    <h6><i class="fas fa-sync"></i> 刷新浏览器</h6>
                    <ol>
                        <li><strong>打开浏览器</strong> 访问主页: <code>http://127.0.0.1:5000/</code></li>
                        <li><strong>强制刷新页面</strong>
                            <ul>
                                <li>Windows: 按 <kbd>Ctrl + F5</kbd></li>
                                <li>Mac: 按 <kbd>Cmd + Shift + R</kbd></li>
                                <li>或者按 <kbd>F5</kbd> 多次</li>
                            </ul>
                        </li>
                        <li><strong>清除浏览器缓存</strong> (如果图片仍未更新)
                            <ul>
                                <li>Chrome: 设置 → 隐私和安全 → 清除浏览数据</li>
                                <li>Firefox: 设置 → 隐私与安全 → 清除数据</li>
                            </ul>
                        </li>
                    </ol>
                </div>

                <div class="tutorial-section">
                    <h6><i class="fas fa-eye"></i> 检查显示效果</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <h6>精选赛事轮播图检查:</h6>
                            <ul>
                                <li>查看主页顶部轮播图</li>
                                <li>确认新图片正确显示</li>
                                <li>测试轮播图切换功能</li>
                                <li>检查图片清晰度</li>
                                <li>确认文字可读性</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>分类卡片图检查:</h6>
                            <ul>
                                <li>滚动到分类浏览部分</li>
                                <li>确认卡片图片正确显示</li>
                                <li>测试悬停效果</li>
                                <li>检查图片比例</li>
                                <li>确认整体美观性</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="success-box">
                    <i class="fas fa-check-circle"></i>
                    <strong>成功标志:</strong> 新图片正确显示，页面加载正常，没有错误提示
                </div>
            </div>
        </div>

        <!-- Troubleshooting -->
        <div class="card tutorial-card">
            <div class="card-header bg-danger text-white">
                <h4><i class="fas fa-tools"></i> 常见问题解决</h4>
            </div>
            <div class="card-body">
                <div class="tutorial-section">
                    <h6><i class="fas fa-question-circle"></i> 图片不显示</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <h6>可能原因:</h6>
                            <ul>
                                <li>文件名不正确</li>
                                <li>文件路径错误</li>
                                <li>文件格式不支持</li>
                                <li>文件权限问题</li>
                                <li>浏览器缓存</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>解决方法:</h6>
                            <ul>
                                <li>检查文件名是否完全一致</li>
                                <li>确认文件在正确的文件夹中</li>
                                <li>使用 JPG 或 PNG 格式</li>
                                <li>检查文件读取权限</li>
                                <li>清除浏览器缓存</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="tutorial-section">
                    <h6><i class="fas fa-image"></i> 图片显示异常</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <h6>常见问题:</h6>
                            <ul>
                                <li>图片变形或拉伸</li>
                                <li>图片模糊不清</li>
                                <li>图片过大或过小</li>
                                <li>颜色显示异常</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>解决方法:</h6>
                            <ul>
                                <li>使用推荐的图片尺寸</li>
                                <li>提高图片分辨率</li>
                                <li>调整图片大小</li>
                                <li>检查图片色彩模式</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="code-block">
                    <strong>快速检查命令 (开发者):</strong><br>
                    # 检查文件是否存在<br>
                    ls static/images/homepage/featured/<br>
                    ls static/images/homepage/categories/<br><br>
                    # 检查文件权限<br>
                    ls -la static/images/homepage/featured/
                </div>
            </div>
        </div>

        <!-- Example Walkthrough -->
        <div class="card tutorial-card">
            <div class="card-header bg-secondary text-white">
                <h4><i class="fas fa-play-circle"></i> 实例演示: 替换FIFA世界杯图片</h4>
            </div>
            <div class="card-body">
                <div class="tutorial-section">
                    <h6><i class="fas fa-step-forward"></i> 完整操作示例</h6>
                    <p>假设您要替换FIFA世界杯的轮播图片：</p>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h6>步骤详解:</h6>
                            <ol>
                                <li><strong>准备新图片</strong>
                                    <ul>
                                        <li>尺寸: 1200 x 400 像素</li>
                                        <li>内容: FIFA世界杯相关图片</li>
                                        <li>格式: JPG</li>
                                    </ul>
                                </li>
                                <li><strong>重命名图片</strong>
                                    <ul>
                                        <li>将新图片命名为: <code>fifa_world_cup_featured.jpg</code></li>
                                    </ul>
                                </li>
                                <li><strong>替换文件</strong>
                                    <ul>
                                        <li>导航到: <code>static/images/homepage/featured/</code></li>
                                        <li>替换现有的 <code>fifa_world_cup_featured.jpg</code></li>
                                    </ul>
                                </li>
                                <li><strong>验证效果</strong>
                                    <ul>
                                        <li>访问主页</li>
                                        <li>查看轮播图第一张</li>
                                        <li>确认新图片显示</li>
                                    </ul>
                                </li>
                            </ol>
                        </div>
                        <div class="col-md-6">
                            <div class="image-example">
                                <h6>当前FIFA世界杯图片</h6>
                                <img src="/static/images/homepage/featured/fifa_world_cup_featured.jpg" 
                                     class="img-fluid" alt="FIFA世界杯" style="max-height: 150px;">
                                <p class="small mt-2">这是当前的占位图片，您可以替换为自己的图片</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Reference -->
        <div class="tutorial-section">
            <h3 class="text-center mb-4">📋 快速参考</h3>
            <div class="row">
                <div class="col-md-4">
                    <div class="image-example">
                        <h6><i class="fas fa-folder"></i> 文件路径</h6>
                        <div class="file-path small">
                            精选赛事:<br>
                            static/images/homepage/featured/<br><br>
                            分类卡片:<br>
                            static/images/homepage/categories/
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="image-example">
                        <h6><i class="fas fa-ruler"></i> 图片尺寸</h6>
                        <ul class="small text-start">
                            <li>精选赛事: 1200 x 400 px</li>
                            <li>分类卡片: 400 x 250 px</li>
                            <li>格式: JPG/PNG</li>
                            <li>大小: < 500KB</li>
                        </ul>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="image-example">
                        <h6><i class="fas fa-keyboard"></i> 快捷键</h6>
                        <ul class="small text-start">
                            <li>强制刷新: Ctrl+F5</li>
                            <li>Mac刷新: Cmd+Shift+R</li>
                            <li>普通刷新: F5</li>
                            <li>开发者工具: F12</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Final Tips -->
        <div class="tutorial-banner mt-5">
            <h2>🎯 替换完成！</h2>
            <p class="lead">按照以上步骤，您就可以成功替换主页的任何图片</p>
            <div class="row mt-4">
                <div class="col-md-3 text-center">
                    <i class="fas fa-image fa-4x mb-2"></i>
                    <h6>准备图片</h6>
                    <small>正确尺寸和格式</small>
                </div>
                <div class="col-md-3 text-center">
                    <i class="fas fa-search fa-4x mb-2"></i>
                    <h6>找到文件</h6>
                    <small>定位正确路径</small>
                </div>
                <div class="col-md-3 text-center">
                    <i class="fas fa-exchange-alt fa-4x mb-2"></i>
                    <h6>替换文件</h6>
                    <small>保持相同文件名</small>
                </div>
                <div class="col-md-3 text-center">
                    <i class="fas fa-check-circle fa-4x mb-2"></i>
                    <h6>验证效果</h6>
                    <small>刷新查看结果</small>
                </div>
            </div>
            <div class="mt-4">
                <a href="/" target="_blank" class="btn btn-light btn-lg me-3">
                    <i class="fas fa-home"></i> 查看主页效果
                </a>
                <a href="/static/homepage_images_guide.html" target="_blank" class="btn btn-outline-light btn-lg">
                    <i class="fas fa-book"></i> 图片指南
                </a>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
