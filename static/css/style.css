:root {
    --primary: #8B0000;
    --secondary: #D4AF37;
    --light: #F5F5DC;
    --dark: #3C2F2F;
}

body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.sidebar {
    background: linear-gradient(135deg, var(--dark), var(--primary));
    color: white;
    height: 100vh;
    position: fixed;
    padding-top: 20px;
    box-shadow: 3px 0 10px rgba(0,0,0,0.1);
}

.main-content {
    margin-left: 250px;
    padding: 20px;
}

.nav-link {
    color: rgba(255,255,255,0.8);
    padding: 12px 20px;
    margin: 5px 0;
    border-radius: 5px;
    transition: all 0.3s;
}

.nav-link:hover, .nav-link.active {
    background-color: rgba(255,255,255,0.1);
    color: white;
}

.nav-link i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

.dashboard-card {
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transition: transform 0.3s;
    height: 100%;
    border: none;
}

.dashboard-card:hover {
    transform: translateY(-5px);
}

.card-header {
    border-top-left-radius: 10px !important;
    border-top-right-radius: 10px !important;
    font-weight: 600;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
}

.btn-primary {
    background-color: var(--primary);
    border-color: var(--primary);
}

.btn-primary:hover {
    background-color: #6d0000;
    border-color: #6d0000;
}

.btn-outline-primary {
    color: var(--primary);
    border-color: var(--primary);
}

.btn-outline-primary:hover {
    background-color: var(--primary);
    color: white;
}

.table th {
    background-color: var(--primary);
    color: white;
}

.alert-warning {
    background-color: rgba(255, 193, 7, 0.2);
}

.steak-bg {
    background-image: url('https://images.unsplash.com/photo-1600891964092-4316c288032e?ixlib=rb-4.0.3');
    background-size: cover;
    background-position: center;
    height: 300px;
    border-radius: 10px;
    position: relative;
    overflow: hidden;
}

.steak-overlay {
    background: rgba(60, 47, 47, 0.7);
    color: white;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 20px;
    text-align: center;
}

.logo {
    font-family: 'Georgia', serif;
    font-size: 1.8rem;
    font-weight: bold;
    color: var(--secondary);
    text-align: center;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(255,255,255,0.2);
}

.badge {
    padding: 0.5em 0.75em;
    font-weight: 600;
}

.table-hover tbody tr:hover {
    background-color: rgba(139, 0, 0, 0.05);
}