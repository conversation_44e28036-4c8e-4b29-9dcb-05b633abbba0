#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化西餐厅员工结构和职位划分
"""

from app import app, db
from models import User
from datetime import date

def optimize_restaurant_staff():
    with app.app_context():
        # 清除现有员工（保留admin）
        User.query.filter(User.username != 'admin').delete()
        
        # 定义西餐厅专业员工结构
        staff_members = [
            # 管理层 (Management)
            {
                'username': 'general_manager',
                'password': 'gm123456',
                'email': '<EMAIL>',
                'name': '约翰·史密斯',
                'role': 'manager',
                'phone': '13800001001',
                'hire_date': date(2020, 1, 15),
                'position': '总经理',
                'department': '管理层'
            },
            {
                'username': 'assistant_manager',
                'password': 'am123456',
                'email': '<EMAIL>',
                'name': '艾米丽·约翰逊',
                'role': 'manager',
                'phone': '13800001002',
                'hire_date': date(2021, 3, 10),
                'position': '副总经理',
                'department': '管理层'
            },
            {
                'username': 'floor_manager',
                'password': 'fm123456',
                'email': '<EMAIL>',
                'name': '大卫·威廉姆斯',
                'role': 'manager',
                'phone': '13800001003',
                'hire_date': date(2021, 6, 1),
                'position': '楼面经理',
                'department': '前厅'
            },
            
            # 厨房团队 (Kitchen Team)
            {
                'username': 'executive_chef',
                'password': 'chef123456',
                'email': '<EMAIL>',
                'name': '马可·罗西',
                'role': 'chef',
                'phone': '13800002001',
                'hire_date': date(2020, 2, 1),
                'position': '行政总厨',
                'department': '厨房'
            },
            {
                'username': 'sous_chef',
                'password': 'sous123456',
                'email': '<EMAIL>',
                'name': '安东尼奥·加西亚',
                'role': 'chef',
                'phone': '13800002002',
                'hire_date': date(2021, 1, 15),
                'position': '副主厨',
                'department': '厨房'
            },
            {
                'username': 'grill_chef',
                'password': 'grill123456',
                'email': '<EMAIL>',
                'name': '詹姆斯·布朗',
                'role': 'chef',
                'phone': '13800002003',
                'hire_date': date(2021, 4, 20),
                'position': '烤肉主厨',
                'department': '厨房'
            },
            {
                'username': 'pastry_chef',
                'password': 'pastry123456',
                'email': '<EMAIL>',
                'name': '索菲亚·马丁',
                'role': 'chef',
                'phone': '13800002004',
                'hire_date': date(2021, 8, 10),
                'position': '甜品主厨',
                'department': '厨房'
            },
            {
                'username': 'prep_cook1',
                'password': 'prep123456',
                'email': '<EMAIL>',
                'name': '李明',
                'role': 'chef',
                'phone': '13800002005',
                'hire_date': date(2022, 1, 5),
                'position': '配菜厨师',
                'department': '厨房'
            },
            {
                'username': 'prep_cook2',
                'password': 'prep123456',
                'email': '<EMAIL>',
                'name': '王强',
                'role': 'chef',
                'phone': '13800002006',
                'hire_date': date(2022, 3, 15),
                'position': '配菜厨师',
                'department': '厨房'
            },
            
            # 前厅服务团队 (Front of House)
            {
                'username': 'head_waiter',
                'password': 'head123456',
                'email': '<EMAIL>',
                'name': '迈克尔·戴维斯',
                'role': 'waiter',
                'phone': '13800003001',
                'hire_date': date(2020, 5, 1),
                'position': '首席服务员',
                'department': '前厅'
            },
            {
                'username': 'sommelier',
                'password': 'wine123456',
                'email': '<EMAIL>',
                'name': '伊莎贝拉·罗德里格斯',
                'role': 'waiter',
                'phone': '13800003002',
                'hire_date': date(2021, 2, 10),
                'position': '侍酒师',
                'department': '前厅'
            },
            {
                'username': 'senior_waiter1',
                'password': 'senior123456',
                'email': '<EMAIL>',
                'name': '克里斯托弗·李',
                'role': 'waiter',
                'phone': '13800003003',
                'hire_date': date(2021, 5, 20),
                'position': '高级服务员',
                'department': '前厅'
            },
            {
                'username': 'senior_waiter2',
                'password': 'senior123456',
                'email': '<EMAIL>',
                'name': '娜塔莉·泰勒',
                'role': 'waiter',
                'phone': '13800003004',
                'hire_date': date(2021, 7, 8),
                'position': '高级服务员',
                'department': '前厅'
            },
            {
                'username': 'waiter1',
                'password': 'waiter123456',
                'email': '<EMAIL>',
                'name': '张小雅',
                'role': 'waiter',
                'phone': '13800003005',
                'hire_date': date(2022, 2, 1),
                'position': '服务员',
                'department': '前厅'
            },
            {
                'username': 'waiter2',
                'password': 'waiter123456',
                'email': '<EMAIL>',
                'name': '刘小美',
                'role': 'waiter',
                'phone': '13800003006',
                'hire_date': date(2022, 4, 15),
                'position': '服务员',
                'department': '前厅'
            },
            {
                'username': 'waiter3',
                'password': 'waiter123456',
                'email': '<EMAIL>',
                'name': '陈小丽',
                'role': 'waiter',
                'phone': '13800003007',
                'hire_date': date(2022, 6, 10),
                'position': '服务员',
                'department': '前厅'
            },
            {
                'username': 'waiter4',
                'password': 'waiter123456',
                'email': '<EMAIL>',
                'name': '赵小敏',
                'role': 'waiter',
                'phone': '13800003008',
                'hire_date': date(2022, 8, 5),
                'position': '服务员',
                'department': '前厅'
            },
            
            # 吧台团队 (Bar Team)
            {
                'username': 'head_bartender',
                'password': 'bar123456',
                'email': '<EMAIL>',
                'name': '亚历克斯·琼斯',
                'role': 'cashier',
                'phone': '13800004001',
                'hire_date': date(2020, 8, 1),
                'position': '首席调酒师',
                'department': '吧台'
            },
            {
                'username': 'bartender',
                'password': 'bartender123456',
                'email': '<EMAIL>',
                'name': '瑞秋·格林',
                'role': 'cashier',
                'phone': '13800004002',
                'hire_date': date(2021, 10, 15),
                'position': '调酒师',
                'department': '吧台'
            },
            
            # 收银团队 (Cashier Team)
            {
                'username': 'head_cashier',
                'password': 'cashier123456',
                'email': '<EMAIL>',
                'name': '莎拉·威尔逊',
                'role': 'cashier',
                'phone': '13800005001',
                'hire_date': date(2020, 10, 1),
                'position': '收银主管',
                'department': '收银'
            },
            {
                'username': 'cashier1',
                'password': 'cashier123456',
                'email': '<EMAIL>',
                'name': '徐小芳',
                'role': 'cashier',
                'phone': '13800005002',
                'hire_date': date(2021, 12, 1),
                'position': '收银员',
                'department': '收银'
            },
            {
                'username': 'cashier2',
                'password': 'cashier123456',
                'email': '<EMAIL>',
                'name': '马小华',
                'role': 'cashier',
                'phone': '13800005003',
                'hire_date': date(2022, 5, 20),
                'position': '收银员',
                'department': '收银'
            },
            
            # 后勤支持团队 (Support Team)
            {
                'username': 'cleaner1',
                'password': 'clean123456',
                'email': '<EMAIL>',
                'name': '孙大姐',
                'role': 'employee',
                'phone': '13800006001',
                'hire_date': date(2021, 1, 10),
                'position': '清洁员',
                'department': '后勤'
            },
            {
                'username': 'cleaner2',
                'password': 'clean123456',
                'email': '<EMAIL>',
                'name': '周阿姨',
                'role': 'employee',
                'phone': '13800006002',
                'hire_date': date(2021, 6, 15),
                'position': '清洁员',
                'department': '后勤'
            },
            {
                'username': 'security',
                'password': 'security123456',
                'email': '<EMAIL>',
                'name': '李保安',
                'role': 'employee',
                'phone': '13800006003',
                'hire_date': date(2020, 12, 1),
                'position': '保安',
                'department': '后勤'
            }
        ]
        
        # 添加员工
        added_count = 0
        for staff_data in staff_members:
            user = User(
                username=staff_data['username'],
                email=staff_data['email'],
                name=staff_data['name'],
                role=staff_data['role'],
                phone=staff_data['phone'],
                hire_date=staff_data['hire_date']
            )
            user.set_password(staff_data['password'])
            db.session.add(user)
            added_count += 1
        
        db.session.commit()
        
        print(f"成功添加 {added_count} 名专业员工！")
        
        # 统计各部门员工数量
        department_stats = {}
        role_stats = {}
        
        for staff_data in staff_members:
            dept = staff_data['department']
            role = staff_data['role']
            
            if dept not in department_stats:
                department_stats[dept] = 0
            department_stats[dept] += 1
            
            if role not in role_stats:
                role_stats[role] = 0
            role_stats[role] += 1
        
        print("\n部门员工统计:")
        for dept, count in department_stats.items():
            print(f"- {dept}: {count} 人")
        
        print("\n职位员工统计:")
        role_names = {
            'manager': '管理层',
            'chef': '厨师',
            'waiter': '服务员',
            'cashier': '收银/吧台',
            'employee': '后勤支持'
        }
        for role, count in role_stats.items():
            print(f"- {role_names.get(role, role)}: {count} 人")
        
        print(f"\n总员工数: {sum(department_stats.values())} 人")
        print("员工结构: 符合高端西餐厅专业化运营标准")
        
        print("\n员工登录信息:")
        print("管理层:")
        print("- 总经理: general_manager / gm123456")
        print("- 副总经理: assistant_manager / am123456")
        print("- 楼面经理: floor_manager / fm123456")
        print("\n厨房:")
        print("- 行政总厨: executive_chef / chef123456")
        print("- 副主厨: sous_chef / sous123456")
        print("- 烤肉主厨: grill_chef / grill123456")
        print("\n前厅:")
        print("- 首席服务员: head_waiter / head123456")
        print("- 侍酒师: sommelier / wine123456")
        print("- 高级服务员: senior_waiter1 / senior123456")

if __name__ == '__main__':
    optimize_restaurant_staff()
