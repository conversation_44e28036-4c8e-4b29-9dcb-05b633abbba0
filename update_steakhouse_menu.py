#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新牛排餐厅菜单 - 西餐标准定价
"""

from app import app, db
from models import MenuItem, Category

def update_steakhouse_menu():
    with app.app_context():
        # 清除现有菜品
        MenuItem.query.delete()
        
        # 获取分类
        categories = {cat.name: cat.id for cat in Category.query.all()}
        
        # 定义西餐牛排餐厅菜单
        menu_items = [
            # 精选牛排 (Premium Steaks) - 主打产品
            {'name': '菲力牛排 200g', 'category': '精选牛排', 'price': 298, 'description': '最嫩的牛肉部位，口感细腻，脂肪含量低，适合追求健康的食客', 'prep_time': 15},
            {'name': '西冷牛排 250g', 'category': '精选牛排', 'price': 268, 'description': '经典牛排选择，肉质紧实有嚼劲，牛肉香味浓郁', 'prep_time': 12},
            {'name': '肋眼牛排 280g', 'category': '精选牛排', 'price': 288, 'description': '大理石纹理丰富，脂肪分布均匀，口感丰腴多汁', 'prep_time': 15},
            {'name': 'T骨牛排 400g', 'category': '精选牛排', 'price': 358, 'description': '一次品尝菲力和西冷两种口感，适合大胃王', 'prep_time': 18},
            {'name': '纽约客牛排 300g', 'category': '精选牛排', 'price': 318, 'description': '美式经典，肉质厚实，牛肉味浓郁，口感层次丰富', 'prep_time': 15},
            
            # 顶级牛排 (Premium Wagyu) - 高端产品
            {'name': '日本A5和牛菲力 150g', 'category': '顶级牛排', 'price': 888, 'description': '世界顶级和牛，入口即化，脂肪香甜，极致奢华体验', 'prep_time': 20},
            {'name': '日本A5和牛西冷 200g', 'category': '顶级牛排', 'price': 998, 'description': '和牛之王，大理石纹理完美，口感无与伦比', 'prep_time': 20},
            {'name': '澳洲M9和牛肋眼 250g', 'category': '顶级牛排', 'price': 688, 'description': '澳洲顶级和牛，脂肪分布均匀，口感丰富醇厚', 'prep_time': 18},
            {'name': '美国Prime牛排 350g', 'category': '顶级牛排', 'price': 468, 'description': '美国最高等级牛肉，肉质鲜美，汁水丰富', 'prep_time': 16},
            {'name': '战斧牛排 800g', 'category': '顶级牛排', 'price': 1288, 'description': '视觉震撼的超大牛排，适合2-3人分享，仪式感满满', 'prep_time': 25},
            
            # 海鲜类 (Seafood) - 高端配菜
            {'name': '波士顿龙虾 500g', 'category': '海鲜类', 'price': 388, 'description': '新鲜波士顿龙虾，肉质鲜甜Q弹，配黄油蒜蓉', 'prep_time': 20},
            {'name': '阿拉斯加帝王蟹腿', 'category': '海鲜类', 'price': 298, 'description': '肥美蟹腿，肉质饱满，天然海洋鲜味', 'prep_time': 15},
            {'name': '挪威三文鱼排', 'category': '海鲜类', 'price': 168, 'description': '挪威进口三文鱼，肉质紧实，富含Omega-3', 'prep_time': 12},
            {'name': '法式煎扇贝', 'category': '海鲜类', 'price': 188, 'description': '新鲜扇贝配黄油香草，口感嫩滑甘甜', 'prep_time': 10},
            {'name': '香煎银鳕鱼', 'category': '海鲜类', 'price': 228, 'description': '阿拉斯加银鳕鱼，肉质细腻，入口即化', 'prep_time': 12},
            
            # 开胃菜 (Appetizers) - 精致前菜
            {'name': '法式鹅肝', 'category': '开胃菜', 'price': 288, 'description': '法国进口鹅肝，配无花果酱和吐司，奢华享受', 'prep_time': 8},
            {'name': '俄罗斯鱼子酱', 'category': '开胃菜', 'price': 388, 'description': '顶级鱼子酱配吐司和酸奶油，极致奢华', 'prep_time': 5},
            {'name': '意式风干火腿拼盘', 'category': '开胃菜', 'price': 128, 'description': '帕尔马火腿配蜜瓜和芝士，经典意式开胃菜', 'prep_time': 5},
            {'name': '蒜蓉面包', 'category': '开胃菜', 'price': 38, 'description': '香脆法式面包配蒜蓉黄油，经典配菜', 'prep_time': 8},
            {'name': '芝士拼盘', 'category': '开胃菜', 'price': 98, 'description': '精选欧洲芝士配坚果和蜂蜜，品酒佳配', 'prep_time': 5},
            
            # 汤品 (Soups) - 精致汤品
            {'name': '法式洋葱汤', 'category': '汤品', 'price': 68, 'description': '经典法式洋葱汤配芝士焗烤，香浓暖胃', 'prep_time': 15},
            {'name': '龙虾浓汤', 'category': '汤品', 'price': 88, 'description': '新鲜龙虾熬制的浓汤，鲜美醇厚', 'prep_time': 20},
            {'name': '蘑菇奶油汤', 'category': '汤品', 'price': 58, 'description': '新鲜蘑菇配奶油，口感丝滑香浓', 'prep_time': 12},
            {'name': '牛尾清汤', 'category': '汤品', 'price': 78, 'description': '慢炖牛尾汤，汤色清澈，营养丰富', 'prep_time': 180},
            
            # 沙拉 (Salads) - 健康选择
            {'name': '凯撒沙拉', 'category': '沙拉', 'price': 78, 'description': '经典凯撒沙拉配帕尔马芝士和面包丁', 'prep_time': 8},
            {'name': '芝麻菜沙拉', 'category': '沙拉', 'price': 68, 'description': '新鲜芝麻菜配樱桃番茄和意式醋汁', 'prep_time': 5},
            {'name': '龙虾沙拉', 'category': '沙拉', 'price': 188, 'description': '新鲜龙虾肉配混合蔬菜，清爽开胃', 'prep_time': 10},
            {'name': '烟熏三文鱼沙拉', 'category': '沙拉', 'price': 128, 'description': '烟熏三文鱼配酸豆和洋葱，北欧风味', 'prep_time': 8},
            
            # 配菜 (Sides) - 精致配菜
            {'name': '松露薯条', 'category': '配菜', 'price': 88, 'description': '手切薯条配黑松露和帕尔马芝士', 'prep_time': 12},
            {'name': '蒜蓉芦笋', 'category': '配菜', 'price': 58, 'description': '新鲜芦笋配蒜蓉黄油，清香爽脆', 'prep_time': 8},
            {'name': '烤时蔬', 'category': '配菜', 'price': 48, 'description': '季节性蔬菜烤制，保持原味营养', 'prep_time': 15},
            {'name': '奶油菠菜', 'category': '配菜', 'price': 38, 'description': '经典西餐配菜，菠菜配奶油调味', 'prep_time': 10},
            {'name': '烤土豆', 'category': '配菜', 'price': 28, 'description': '香烤土豆配迷迭香和海盐', 'prep_time': 25},
            
            # 甜品 (Desserts) - 精致甜品
            {'name': '提拉米苏', 'category': '甜品', 'price': 68, 'description': '经典意式甜品，马斯卡彭芝士配咖啡', 'prep_time': 5},
            {'name': '法式舒芙蕾', 'category': '甜品', 'price': 88, 'description': '现做舒芙蕾，轻盈如云，香草味浓郁', 'prep_time': 20},
            {'name': '巧克力熔岩蛋糕', 'category': '甜品', 'price': 78, 'description': '温热巧克力蛋糕配香草冰淇淋', 'prep_time': 15},
            {'name': '芝士蛋糕', 'category': '甜品', 'price': 58, 'description': '纽约风味芝士蛋糕，浓郁香滑', 'prep_time': 5},
            {'name': '法式马卡龙', 'category': '甜品', 'price': 48, 'description': '精致法式马卡龙，多种口味选择', 'prep_time': 5},
            
            # 红酒 (Wines) - 精选酒款
            {'name': '法国波尔多红酒', 'category': '红酒', 'price': 388, 'description': '法国波尔多产区红酒，单宁丰富，适合搭配牛排', 'prep_time': 2},
            {'name': '意大利基安帝红酒', 'category': '红酒', 'price': 288, 'description': '意大利托斯卡纳红酒，果香浓郁，口感平衡', 'prep_time': 2},
            {'name': '澳洲设拉子红酒', 'category': '红酒', 'price': 228, 'description': '澳洲经典红酒，口感浓郁，香料味突出', 'prep_time': 2},
            {'name': '智利赤霞珠红酒', 'category': '红酒', 'price': 168, 'description': '智利优质红酒，性价比高，适合日常饮用', 'prep_time': 2},
            {'name': '香槟', 'category': '红酒', 'price': 488, 'description': '法国香槟，庆祝必备，气泡细腻持久', 'prep_time': 2},
            
            # 咖啡茶饮 (Beverages) - 精品饮品
            {'name': '意式浓缩咖啡', 'category': '咖啡茶饮', 'price': 28, 'description': '经典意式浓缩，香浓醇厚', 'prep_time': 3},
            {'name': '卡布奇诺', 'category': '咖啡茶饮', 'price': 38, 'description': '意式咖啡配奶泡，口感丰富', 'prep_time': 5},
            {'name': '拿铁咖啡', 'category': '咖啡茶饮', 'price': 38, 'description': '咖啡配蒸奶，口感顺滑', 'prep_time': 5},
            {'name': '英式下午茶', 'category': '咖啡茶饮', 'price': 48, 'description': '精选英式红茶，配柠檬和蜂蜜', 'prep_time': 5},
            {'name': '鲜榨橙汁', 'category': '咖啡茶饮', 'price': 38, 'description': '新鲜橙子现榨，维C丰富', 'prep_time': 3},
        ]
        
        # 添加菜品
        added_count = 0
        for item_data in menu_items:
            if item_data['category'] in categories:
                menu_item = MenuItem(
                    name=item_data['name'],
                    category_id=categories[item_data['category']],
                    price=item_data['price'],
                    description=item_data['description'],
                    preparation_time=item_data['prep_time']
                )
                db.session.add(menu_item)
                added_count += 1
        
        db.session.commit()
        
        print(f"成功更新 {added_count} 道菜品！")
        
        # 统计各分类菜品数量和价格范围
        category_stats = {}
        for item_data in menu_items:
            category = item_data['category']
            if category not in category_stats:
                category_stats[category] = {'count': 0, 'min_price': float('inf'), 'max_price': 0}
            
            category_stats[category]['count'] += 1
            category_stats[category]['min_price'] = min(category_stats[category]['min_price'], item_data['price'])
            category_stats[category]['max_price'] = max(category_stats[category]['max_price'], item_data['price'])
        
        print("\n菜品分类统计:")
        for category, stats in category_stats.items():
            print(f"- {category}: {stats['count']}道菜品, 价格区间: ¥{stats['min_price']}-{stats['max_price']}")
        
        print(f"\n总菜品数: {sum(stats['count'] for stats in category_stats.values())} 道")
        print("定价策略: 符合高端牛排餐厅标准，价格合理分层")

if __name__ == '__main__':
    update_steakhouse_menu()
