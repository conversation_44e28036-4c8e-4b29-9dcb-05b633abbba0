<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 全面图片诊断</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .diagnosis-card {
            border: 3px solid #dc3545;
            margin-bottom: 2rem;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        .diagnosis-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.2);
        }
        .diagnosis-banner {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 3rem;
            border-radius: 1rem;
            text-align: center;
            margin-bottom: 3rem;
            box-shadow: 0 15px 30px rgba(0,0,0,0.3);
        }
        .image-test {
            border: 2px solid #dee2e6;
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 1rem 0;
            text-align: center;
        }
        .image-success {
            border-color: #28a745;
            background: #d4edda;
        }
        .image-error {
            border-color: #dc3545;
            background: #f8d7da;
        }
        .test-image {
            max-width: 200px;
            max-height: 150px;
            object-fit: cover;
            border-radius: 0.25rem;
        }
        .test-result {
            padding: 0.5rem;
            margin: 0.25rem 0;
            border-radius: 0.25rem;
            font-family: monospace;
            font-size: 0.875rem;
        }
        .test-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .test-error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .loading {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <!-- Diagnosis Banner -->
        <div class="diagnosis-banner">
            <h1 class="display-2">🔍 全面图片诊断</h1>
            <h2 class="mb-4">检测所有图片显示问题</h2>
            <p class="lead">找出具体哪些图片无法正确显示</p>
        </div>

        <!-- Auto Diagnosis -->
        <div class="card diagnosis-card">
            <div class="card-header bg-danger text-white">
                <h4><i class="fas fa-search"></i> 自动图片检测</h4>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5>全面图片测试</h5>
                    <button class="btn btn-danger btn-lg" onclick="runComprehensiveTest()">
                        <i class="fas fa-play"></i> 开始全面检测
                    </button>
                </div>
                
                <div id="comprehensiveResults">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> 点击按钮开始检测所有图片资源
                    </div>
                </div>
            </div>
        </div>

        <!-- Visual Image Tests -->
        <div class="card diagnosis-card">
            <div class="card-header bg-warning text-dark">
                <h4><i class="fas fa-images"></i> 可视化图片测试</h4>
            </div>
            <div class="card-body">
                <h5>精选赛事轮播图测试</h5>
                <div class="row" id="featuredImagesTest">
                    <!-- 动态生成 -->
                </div>

                <h5 class="mt-4">分类浏览图片测试</h5>
                <div class="row" id="categoryImagesTest">
                    <!-- 动态生成 -->
                </div>

                <h5 class="mt-4">赛事详情图片测试</h5>
                <div class="row" id="eventImagesTest">
                    <!-- 动态生成 -->
                </div>
            </div>
        </div>

        <!-- Network Analysis -->
        <div class="card diagnosis-card">
            <div class="card-header bg-info text-white">
                <h4><i class="fas fa-network-wired"></i> 网络请求分析</h4>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <h6><i class="fas fa-lightbulb"></i> 如何查看具体的图片加载错误:</h6>
                    <ol>
                        <li>按 <kbd>F12</kbd> 打开开发者工具</li>
                        <li>切换到 <strong>Network</strong> 标签</li>
                        <li>在Filter中选择 <strong>Img</strong> 只显示图片请求</li>
                        <li>刷新页面 (<kbd>F5</kbd>)</li>
                        <li>查看红色的失败请求</li>
                        <li>点击失败的请求查看详细错误信息</li>
                    </ol>
                </div>

                <div id="networkAnalysis">
                    <button class="btn btn-info" onclick="analyzeNetworkRequests()">
                        <i class="fas fa-search"></i> 分析网络请求
                    </button>
                </div>
            </div>
        </div>

        <!-- Quick Fixes -->
        <div class="card diagnosis-card">
            <div class="card-header bg-success text-white">
                <h4><i class="fas fa-tools"></i> 快速修复工具</h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="text-center">
                            <button class="btn btn-success btn-lg mb-2 w-100" onclick="regenerateAllImages()">
                                <i class="fas fa-redo"></i><br>重新生成图片
                            </button>
                            <p class="small">重新创建所有占位图片</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <button class="btn btn-warning btn-lg mb-2 w-100" onclick="clearAllCache()">
                                <i class="fas fa-sync"></i><br>清除缓存
                            </button>
                            <p class="small">强制清除浏览器缓存</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <button class="btn btn-info btn-lg mb-2 w-100" onclick="checkFileSystem()">
                                <i class="fas fa-folder"></i><br>检查文件系统
                            </button>
                            <p class="small">验证文件是否存在</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <button class="btn btn-primary btn-lg mb-2 w-100" onclick="testMainPage()">
                                <i class="fas fa-home"></i><br>测试主页
                            </button>
                            <p class="small">检查主页图片显示</p>
                        </div>
                    </div>
                </div>
                
                <div id="quickFixResults" class="mt-3"></div>
            </div>
        </div>

        <!-- Detailed Results -->
        <div class="card diagnosis-card">
            <div class="card-header bg-secondary text-white">
                <h4><i class="fas fa-list"></i> 详细测试结果</h4>
            </div>
            <div class="card-body">
                <div id="detailedResults">
                    <p class="text-muted">运行测试后这里会显示详细结果</p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 所有需要测试的图片路径
        const imageTests = {
            featured: [
                '/static/images/homepage/featured/fifa_world_cup_featured.jpg',
                '/static/images/homepage/featured/champions_league_featured.jpg',
                '/static/images/homepage/featured/nba_finals_featured.jpg',
                '/static/images/homepage/featured/wimbledon_featured.jpg',
                '/static/images/homepage/featured/olympic_swimming_featured.jpg',
                '/static/images/homepage/featured/masters_golf_featured.jpg',
                '/static/images/homepage/featured/monaco_gp_featured.jpg'
            ],
            categories: [
                '/static/images/homepage/categories/football_category.jpg',
                '/static/images/homepage/categories/basketball_category.jpg',
                '/static/images/homepage/categories/tennis_category.jpg',
                '/static/images/homepage/categories/swimming_category.jpg',
                '/static/images/homepage/categories/athletics_category.jpg',
                '/static/images/homepage/categories/golf_category.jpg',
                '/static/images/homepage/categories/formula_1_category.jpg'
            ],
            events: [
                '/static/images/events/fifa_world_cup.jpg',
                '/static/images/events/champions_league.jpg',
                '/static/images/events/premier_league.jpg',
                '/static/images/events/la_liga.jpg',
                '/static/images/events/serie_a.jpg',
                '/static/images/events/nba_finals.jpg',
                '/static/images/events/wimbledon.jpg',
                '/static/images/events/us_open_tennis.jpg',
                '/static/images/events/french_open.jpg',
                '/static/images/events/australian_open.jpg'
            ]
        };

        // 运行全面测试
        async function runComprehensiveTest() {
            const resultsDiv = document.getElementById('comprehensiveResults');
            resultsDiv.innerHTML = '<div class="alert alert-info"><i class="fas fa-spinner fa-spin"></i> 正在进行全面图片检测...</div>';
            
            let allResults = '';
            let totalImages = 0;
            let successCount = 0;
            let errorCount = 0;
            
            // 测试所有图片类别
            for (const [category, images] of Object.entries(imageTests)) {
                allResults += `<h6 class="mt-3">${getCategoryName(category)} (${images.length}张图片):</h6>`;
                
                for (const imageUrl of images) {
                    totalImages++;
                    try {
                        const response = await fetch(imageUrl, { method: 'HEAD' });
                        const success = response.ok;
                        const resultClass = success ? 'test-success' : 'test-error';
                        const resultIcon = success ? '✅' : '❌';
                        const statusText = `${response.status} ${response.statusText}`;
                        
                        if (success) {
                            successCount++;
                        } else {
                            errorCount++;
                        }
                        
                        allResults += `
                            <div class="test-result ${resultClass}">
                                ${resultIcon} ${getImageName(imageUrl)} - ${statusText}
                            </div>
                        `;
                    } catch (error) {
                        errorCount++;
                        allResults += `
                            <div class="test-result test-error">
                                ❌ ${getImageName(imageUrl)} - Network Error: ${error.message}
                            </div>
                        `;
                    }
                }
            }
            
            const successRate = Math.round((successCount / totalImages) * 100);
            const summaryClass = successRate >= 80 ? 'alert-success' : successRate >= 50 ? 'alert-warning' : 'alert-danger';
            const summaryIcon = successRate >= 80 ? '✅' : successRate >= 50 ? '⚠️' : '❌';
            
            const summary = `
                <div class="alert ${summaryClass}">
                    <h5>${summaryIcon} 全面检测完成</h5>
                    <div class="row">
                        <div class="col-md-4">
                            <strong>总计:</strong> ${totalImages} 张图片
                        </div>
                        <div class="col-md-4">
                            <strong>成功:</strong> ${successCount} (${successRate}%)
                        </div>
                        <div class="col-md-4">
                            <strong>失败:</strong> ${errorCount}
                        </div>
                    </div>
                </div>
            `;
            
            resultsDiv.innerHTML = summary + allResults;
            
            // 同时生成可视化测试
            generateVisualTests();
        }

        // 生成可视化测试
        function generateVisualTests() {
            generateImageTests('featuredImagesTest', imageTests.featured, '精选赛事');
            generateImageTests('categoryImagesTest', imageTests.categories, '分类');
            generateImageTests('eventImagesTest', imageTests.events.slice(0, 6), '赛事'); // 只显示前6个
        }

        function generateImageTests(containerId, images, type) {
            const container = document.getElementById(containerId);
            let html = '';
            
            images.forEach((imageUrl, index) => {
                const imageName = getImageName(imageUrl);
                html += `
                    <div class="col-md-4 mb-3">
                        <div class="image-test" id="test-${containerId}-${index}">
                            <h6>${imageName}</h6>
                            <img src="${imageUrl}" 
                                 class="test-image" 
                                 alt="${imageName}"
                                 onload="markImageSuccess('test-${containerId}-${index}', '${imageUrl}')"
                                 onerror="markImageError('test-${containerId}-${index}', '${imageUrl}')">
                            <div class="test-result loading mt-2">
                                <i class="fas fa-spinner fa-spin"></i> 加载中...
                            </div>
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }

        function markImageSuccess(testId, imageUrl) {
            const testElement = document.getElementById(testId);
            testElement.classList.add('image-success');
            testElement.querySelector('.test-result').innerHTML = '✅ 加载成功';
            testElement.querySelector('.test-result').className = 'test-result test-success mt-2';
        }

        function markImageError(testId, imageUrl) {
            const testElement = document.getElementById(testId);
            testElement.classList.add('image-error');
            testElement.querySelector('.test-result').innerHTML = `❌ 加载失败: ${imageUrl}`;
            testElement.querySelector('.test-result').className = 'test-result test-error mt-2';
        }

        // 辅助函数
        function getCategoryName(category) {
            const names = {
                'featured': '精选赛事轮播图',
                'categories': '分类浏览图片',
                'events': '赛事详情图片'
            };
            return names[category] || category;
        }

        function getImageName(url) {
            const filename = url.split('/').pop();
            return filename.replace(/\.(jpg|jpeg|png|gif)$/i, '').replace(/_/g, ' ');
        }

        // 快速修复功能
        function regenerateAllImages() {
            const resultsDiv = document.getElementById('quickFixResults');
            resultsDiv.innerHTML = `
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> 
                    要重新生成所有图片，请在命令行运行以下命令：<br>
                    <code>python create_homepage_images.py</code><br>
                    <code>python create_event_images.py</code>
                </div>
            `;
        }

        function clearAllCache() {
            const resultsDiv = document.getElementById('quickFixResults');
            resultsDiv.innerHTML = `
                <div class="alert alert-warning">
                    <i class="fas fa-sync"></i> 
                    请按以下步骤清除缓存：<br>
                    1. 按 <kbd>Ctrl+Shift+R</kbd> 强制刷新<br>
                    2. 或按 <kbd>F12</kbd> → 右键刷新按钮 → "Empty Cache and Hard Reload"
                </div>
            `;
        }

        function checkFileSystem() {
            const resultsDiv = document.getElementById('quickFixResults');
            resultsDiv.innerHTML = `
                <div class="alert alert-info">
                    <i class="fas fa-folder"></i> 
                    文件系统检查需要在服务器端进行。请检查以下目录是否存在图片文件：<br>
                    • static/images/homepage/featured/<br>
                    • static/images/homepage/categories/<br>
                    • static/images/events/
                </div>
            `;
        }

        function testMainPage() {
            window.open('/', '_blank');
        }

        // 页面加载时的提示
        window.addEventListener('load', function() {
            console.log('🔍 全面图片诊断工具已加载');
            console.log('💡 请点击"开始全面检测"按钮来检测所有图片');
        });
    </script>
</body>
</html>
