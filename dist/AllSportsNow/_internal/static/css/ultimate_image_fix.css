/* 终极图片显示修复CSS - 覆盖所有冲突 */

/* 最高优先级轮播图修复 */
#featuredEventsCarousel .carousel-item {
    position: relative !important;
    float: left !important;
    width: 100% !important;
    margin-right: -100% !important;
    -webkit-backface-visibility: hidden !important;
    backface-visibility: hidden !important;
    transition: transform 0.6s ease-in-out !important;
    /* 移除所有可能隐藏图片的属性 */
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* 确保只有第一个项目默认显示 */
#featuredEventsCarousel .carousel-item:first-child {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* 确保活动项目始终显示 */
#featuredEventsCarousel .carousel-item.active {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
    z-index: 1 !important;
}

/* 轮播图图片最高优先级设置 */
#featuredEventsCarousel .carousel-item img {
    height: 400px !important;
    width: 100% !important;
    object-fit: cover !important;
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
    /* 移除所有可能影响显示的属性 */
    transition: none !important;
    transform: none !important;
    filter: none !important;
    /* 确保图片立即加载 */
    loading: eager !important;
}

/* 覆盖lazy loading */
#featuredEventsCarousel img[loading="lazy"] {
    loading: eager !important;
}

/* 防止任何动画或过渡效果影响图片 */
#featuredEventsCarousel * {
    -webkit-animation: none !important;
    animation: none !important;
}

/* 确保轮播图容器正确 */
#featuredEventsCarousel {
    overflow: hidden !important;
    position: relative !important;
}

#featuredEventsCarousel .carousel-inner {
    position: relative !important;
    width: 100% !important;
    overflow: hidden !important;
}

/* 防止position-relative容器影响图片 */
#featuredEventsCarousel .position-relative {
    position: relative !important;
}

#featuredEventsCarousel .position-relative img {
    position: static !important;
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* 覆盖所有可能的CSS冲突 */
.carousel-item img {
    height: 400px !important;
    object-fit: cover !important;
    width: 100% !important;
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
    transition: none !important;
}

/* 确保图片在所有状态下都显示 */
.carousel-item.active img,
.carousel-item-next img,
.carousel-item-prev img {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* 防止Bootstrap默认隐藏非活动项 */
.carousel-item:not(.active) {
    display: none !important;
}

.carousel-item.active {
    display: block !important;
}

/* 确保图片加载完成后立即显示 */
img[src*="fifa_world_cup_featured.jpg"] {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
    height: 400px !important;
    object-fit: cover !important;
    width: 100% !important;
}

/* 防止任何JavaScript动态样式覆盖 */
.force-show-image {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* 移除可能的淡入淡出效果 */
.carousel-item {
    -webkit-transition: none !important;
    transition: none !important;
}

/* 确保轮播图指示器正常工作 */
#featuredEventsCarousel .carousel-indicators {
    position: absolute !important;
    bottom: 20px !important;
    z-index: 2 !important;
}

/* 确保轮播图控制按钮正常工作 */
#featuredEventsCarousel .carousel-control-prev,
#featuredEventsCarousel .carousel-control-next {
    position: absolute !important;
    top: 0 !important;
    bottom: 0 !important;
    z-index: 2 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 15% !important;
    color: #fff !important;
    text-align: center !important;
    opacity: 0.5 !important;
    transition: opacity 0.15s ease !important;
}

/* 调试用 - 可以临时启用来检查问题 */
/*
#featuredEventsCarousel .carousel-item {
    border: 3px solid red !important;
}

#featuredEventsCarousel .carousel-item img {
    border: 3px solid blue !important;
}

#featuredEventsCarousel .carousel-item.active {
    border: 3px solid green !important;
}
*/

/* 确保图片在所有浏览器中正确显示 */
@supports (object-fit: cover) {
    #featuredEventsCarousel .carousel-item img {
        object-fit: cover !important;
    }
}

/* 移动端适配 */
@media (max-width: 768px) {
    #featuredEventsCarousel .carousel-item img {
        height: 250px !important;
    }
}

@media (max-width: 576px) {
    #featuredEventsCarousel .carousel-item img {
        height: 200px !important;
    }
}

/* 确保图片在加载时就显示 */
#featuredEventsCarousel img {
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
}

/* 只针对图片元素，不影响其他内容 */
#featuredEventsCarousel img {
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
}

/* 只隐藏非活动的轮播项，但不隐藏图片 */
#featuredEventsCarousel .carousel-item:not(.active) {
    display: none !important;
}

#featuredEventsCarousel .carousel-item.active {
    display: block !important;
}

#featuredEventsCarousel .carousel-item.active * {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* 最终保险措施 - 强制显示FIFA图片 */
img[alt*="FIFA"],
img[alt*="World Cup"],
img[src*="fifa"] {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
    height: 400px !important;
    width: 100% !important;
    object-fit: cover !important;
}
