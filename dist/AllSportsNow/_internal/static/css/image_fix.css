/* 图片显示修复CSS */

/* 修复轮播图图片显示问题 */
.carousel-item img {
    height: 400px !important;
    object-fit: cover !important;
    width: 100% !important;
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
    transition: none !important;
}

.carousel-item {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
}

.carousel-item.active {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* 修复分类卡片图片显示 */
.category-card img,
.card-img-top {
    height: 200px !important;
    object-fit: cover !important;
    width: 100% !important;
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
    transition: none !important;
}

/* 修复赛事卡片图片显示 */
.event-card img,
.table img,
.card img {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
    transition: none !important;
}

/* 修复小图片显示 */
.table img {
    width: 40px !important;
    height: 40px !important;
    object-fit: cover !important;
}

/* 语言切换平滑过渡 */
body {
    transition: opacity 0.3s ease-in-out;
}

.language-transition {
    opacity: 0.7;
}

/* 防止图片闪烁 */
img {
    image-rendering: auto;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
}

/* 图片加载状态 */
.image-loading {
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 200px;
}

.image-loading::before {
    content: "Loading...";
    color: #6c757d;
    font-size: 0.9rem;
}

/* 图片错误处理 */
img[src=""],
img:not([src]) {
    opacity: 0;
    visibility: hidden;
}

/* 确保Bootstrap轮播图正常工作 */
.carousel {
    overflow: hidden;
}

.carousel-inner {
    position: relative;
    width: 100%;
    overflow: hidden;
}

/* 移除冲突的display: none规则，让Bootstrap自己处理 */
.carousel-item {
    position: relative;
    float: left;
    width: 100%;
    margin-right: -100%;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    transition: transform 0.6s ease-in-out;
}

/* 确保活动项目显示 */
.carousel-item.active {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
}

.carousel-item-next,
.carousel-item-prev {
    display: block !important;
}

/* 修复图片容器 */
.position-relative img {
    position: relative;
    z-index: 1;
}

/* 确保图片在所有设备上正确显示 */
@media (max-width: 768px) {
    .carousel-item img {
        height: 250px !important;
    }

    .category-card img {
        height: 150px !important;
    }
}

@media (max-width: 576px) {
    .carousel-item img {
        height: 200px !important;
    }

    .category-card img {
        height: 120px !important;
    }
}

/* 防止图片在语言切换时消失 */
.language-switching img {
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
}

/* 强制显示所有图片 */
.force-show-images img {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* 修复可能的CSS冲突 - 移除height: auto冲突 */
.carousel-item img,
.card-img-top,
.category-card img,
.event-card img {
    max-width: 100% !important;
    vertical-align: middle !important;
}

/* 确保图片在特定容器中正确显示 */
.carousel-item img {
    height: 400px !important;
    object-fit: cover !important;
}

.category-card img {
    height: 200px !important;
    object-fit: cover !important;
}

/* 修复图片加载延迟问题 */
img[loading="lazy"] {
    opacity: 1 !important;
    visibility: visible !important;
}

/* 防止图片被其他样式覆盖 */
.carousel-item > .position-relative > img {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* 确保轮播图控制按钮正常工作 */
.carousel-control-prev,
.carousel-control-next {
    z-index: 2;
}

/* 修复可能的JavaScript冲突 */
.no-js img {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
}
