/* 整合后的精选赛事样式 - 只保留滚动功能 */

/* 精选赛事滚动容器 */
.featured-events-scroll-container {
    position: relative;
    overflow: hidden;
    padding: 0.5rem 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 0.75rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    max-height: 280px; /* 限制最大高度 */
}

/* 滚动区域 */
.featured-events-scroll {
    display: flex;
    gap: 1rem;
    padding: 0.75rem;
    overflow-x: auto;
    scroll-behavior: smooth;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
    height: 260px; /* 固定高度 */
}

.featured-events-scroll::-webkit-scrollbar {
    display: none; /* Chrome, Safari and Opera */
}

/* 精选赛事卡片 */
.featured-event-card {
    flex: 0 0 320px;
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.08);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: 1px solid #e9ecef;
    height: 240px; /* 固定卡片高度 */
}

.featured-event-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

/* 卡片图片区域 */
.featured-event-image {
    position: relative;
    height: 120px;
    overflow: hidden;
}

.featured-event-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.featured-event-card:hover .featured-event-image img {
    transform: scale(1.05);
}

/* 分类标签 */
.event-category-badge {
    position: absolute;
    top: 1rem;
    left: 1rem;
    z-index: 2;
}

.event-category-badge .badge {
    font-size: 0.8rem;
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    background: rgba(0,123,255,0.9) !important;
    backdrop-filter: blur(10px);
}

/* 下一个大赛事标签 */
.next-event-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    z-index: 2;
}

.next-event-badge .badge {
    font-size: 0.75rem;
    padding: 0.4rem 0.8rem;
    border-radius: 1.5rem;
    background: rgba(255,193,7,0.95) !important;
    backdrop-filter: blur(10px);
    font-weight: 600;
}

/* 卡片内容区域 */
.featured-event-content {
    padding: 0.75rem;
    height: 120px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

/* 赛事标题 */
.event-title {
    font-size: 1rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    line-height: 1.2;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* 赛事描述 */
.event-description {
    color: #6c757d;
    font-size: 0.8rem;
    line-height: 1.4;
    margin-bottom: 0.5rem;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    flex: 1;
}

/* 赛事元数据 */
.event-meta {
    margin-bottom: 0.5rem;
}

.event-meta-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.25rem;
    font-size: 0.75rem;
    color: #495057;
}

.event-meta-item i {
    width: 14px;
    margin-right: 0.5rem;
    font-size: 0.75rem;
}

/* 操作按钮 */
.event-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.event-actions .btn {
    flex: 1;
    min-width: 80px;
    font-size: 0.75rem;
    padding: 0.4rem 0.6rem;
    border-radius: 0.375rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.event-actions .btn-primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
    border: none;
    box-shadow: 0 4px 15px rgba(0,123,255,0.3);
}

.event-actions .btn-primary:hover {
    background: linear-gradient(135deg, #0056b3, #004085);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,123,255,0.4);
}

.event-actions .btn-outline-secondary {
    border: 2px solid #6c757d;
    color: #6c757d;
    background: transparent;
}

.event-actions .btn-outline-secondary:hover {
    background: #6c757d;
    color: white;
    transform: translateY(-2px);
}

/* 滚动导航按钮 */
.scroll-navigation {
    margin-top: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.scroll-navigation .btn {
    margin: 0 0.25rem;
    border-radius: 1.5rem;
    padding: 0.4rem 1rem;
    font-weight: 600;
    font-size: 0.85rem;
    transition: all 0.3s ease;
    border: 2px solid #007bff;
}

.scroll-navigation .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0,123,255,0.3);
}

/* 滚动指示器 */
.scroll-indicator {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
    padding: 0.5rem 1rem;
    background: rgba(248,249,250,0.8);
    border-radius: 1.5rem;
    border: 1px solid #dee2e6;
}

.scroll-indicator .current-slide {
    color: #007bff;
    font-weight: 700;
}

/* 无赛事卡片样式 */
.no-events-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 2px dashed #dee2e6;
    min-height: 240px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.no-events-card .featured-event-content {
    padding: 2rem;
    height: auto;
}

.no-events-card .event-title {
    color: #6c757d;
    font-size: 1.1rem;
    margin-bottom: 1rem;
}

.no-events-card .event-description {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .featured-event-card {
        flex: 0 0 280px;
        height: 220px;
    }

    .featured-event-image {
        height: 100px;
    }

    .featured-event-content {
        padding: 0.6rem;
        height: 120px;
    }

    .event-title {
        font-size: 0.9rem;
    }

    .event-description {
        font-size: 0.75rem;
    }

    .event-actions .btn {
        font-size: 0.7rem;
        padding: 0.3rem 0.5rem;
    }
}

@media (max-width: 576px) {
    .featured-event-card {
        flex: 0 0 260px;
        height: 200px;
    }

    .featured-events-scroll {
        gap: 0.75rem;
        padding: 0.5rem;
        height: 220px;
    }

    .featured-events-scroll-container {
        margin: 0 -0.5rem;
        border-radius: 0.5rem;
        max-height: 240px;
    }

    .featured-event-image {
        height: 80px;
    }

    .featured-event-content {
        height: 120px;
        padding: 0.5rem;
    }
}

/* 滚动指示器 */
.featured-events-scroll-container::before,
.featured-events-scroll-container::after {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    width: 30px;
    z-index: 2;
    pointer-events: none;
}

.featured-events-scroll-container::before {
    left: 0;
    background: linear-gradient(to right, rgba(248,249,250,0.8), transparent);
}

.featured-events-scroll-container::after {
    right: 0;
    background: linear-gradient(to left, rgba(248,249,250,0.8), transparent);
}

/* 动画效果 */
.featured-event-card {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 图片替换提示 */
.featured-event-image::after {
    content: '📸 点击替换图片';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0,0,0,0.7);
    color: white;
    text-align: center;
    padding: 0.5rem;
    font-size: 0.8rem;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.featured-event-card:hover .featured-event-image::after {
    opacity: 1;
}

/* 强制显示图片的类 */
.force-show-image {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
}
