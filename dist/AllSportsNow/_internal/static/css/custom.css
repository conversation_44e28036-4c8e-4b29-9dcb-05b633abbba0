/* Modern UI Design System for Sports Events Website */

:root {
  /* Color Palette */
  --primary: #1a73e8;
  --primary-dark: #0d47a1;
  --primary-light: #e8f0fe;
  --secondary: #ff5722;
  --secondary-light: #ffccbc;
  --success: #34a853;
  --warning: #fbbc04;
  --danger: #ea4335;
  --dark: #202124;
  --light: #f8f9fa;
  --gray: #5f6368;
  --gray-light: #dadce0;

  /* Typography */
  --font-primary: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', Oxygen, Ubuntu, Cantarell, 'Open Sans', sans-serif;
  --font-heading: 'Montserrat', var(--font-primary);

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2.5rem;

  /* Border Radius */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 16px;
  --radius-xl: 24px;

  /* Shadows */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
}

/* Base Styles */
body {
  font-family: var(--font-primary);
  color: var(--dark);
  background-color: var(--light);
  line-height: 1.6;
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-heading);
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: var(--spacing-md);
}

a {
  color: var(--primary);
  text-decoration: none;
  transition: color 0.2s ease-in-out;
}

a:hover {
  color: var(--primary-dark);
  text-decoration: none;
}

/* Navbar Styling */
.navbar {
  box-shadow: var(--shadow-sm);
  padding: var(--spacing-md) 0;
}

.navbar-brand {
  font-weight: 700;
  font-size: 1.5rem;
}

.navbar-brand i {
  color: var(--secondary);
  margin-right: var(--spacing-sm);
}

.navbar-dark .navbar-nav .nav-link {
  color: rgba(255, 255, 255, 0.85);
  font-weight: 500;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-sm);
  transition: all 0.2s ease;
}

.navbar-dark .navbar-nav .nav-link:hover,
.navbar-dark .navbar-nav .nav-link:focus {
  color: white;
  background-color: rgba(255, 255, 255, 0.1);
}

.navbar-dark .navbar-nav .active > .nav-link {
  background-color: rgba(255, 255, 255, 0.15);
  color: white;
}

/* Search Bar */
.search-form .form-control {
  border-radius: var(--radius-md);
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--gray-light);
  box-shadow: var(--shadow-sm);
}

.search-form .btn {
  border-radius: var(--radius-md);
  padding: var(--spacing-sm) var(--spacing-lg);
}

/* Hero Section */
.hero-section {
  background-color: var(--primary-light);
  padding: var(--spacing-xl) 0;
  margin-bottom: var(--spacing-xl);
  border-radius: var(--radius-lg);
}

.hero-section h1 {
  font-size: 2.5rem;
  margin-bottom: var(--spacing-md);
}

.hero-section .lead {
  font-size: 1.2rem;
  margin-bottom: var(--spacing-lg);
}

/* Cards */
.card {
  border: none;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-md);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  margin-bottom: var(--spacing-lg);
  overflow: hidden;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.card-img-top {
  height: 200px;
  object-fit: cover;
}

.card-body {
  padding: var(--spacing-lg);
}

.card-title {
  font-weight: 700;
  margin-bottom: var(--spacing-sm);
}

.card-text {
  color: var(--gray);
  margin-bottom: var(--spacing-md);
}

.card-footer {
  background-color: white;
  border-top: 1px solid var(--gray-light);
  padding: var(--spacing-md) var(--spacing-lg);
}

/* Category Cards */
.category-card {
  text-align: center;
  height: 100%;
}

.category-card .card-img-top {
  height: 160px;
}

.category-card .card-body {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
}

/* Event Cards */
.event-card .card-img-top {
  height: 180px;
}

.event-card .badge {
  font-size: 0.8rem;
  padding: 0.4rem 0.6rem;
  margin-right: var(--spacing-xs);
}

/* Carousel */
.carousel {
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  margin-bottom: var(--spacing-xl);
}

.carousel-item img {
  height: 500px;
  object-fit: cover;
}

.carousel-caption {
  background: linear-gradient(to top, rgba(0,0,0,0.8), rgba(0,0,0,0));
  border-radius: var(--radius-md);
  padding: var(--spacing-xl) var(--spacing-lg) var(--spacing-lg);
  bottom: 0;
  left: 0;
  right: 0;
  text-align: left;
}

.carousel-caption h3 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: var(--spacing-sm);
}

.carousel-caption p {
  font-size: 1.1rem;
  margin-bottom: var(--spacing-md);
}

.carousel-indicators {
  margin-bottom: var(--spacing-lg);
}

.carousel-indicators li {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin: 0 5px;
}

/* Buttons */
.btn {
  font-weight: 500;
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--radius-md);
  transition: all 0.2s ease;
}

.btn-primary {
  background-color: var(--primary);
  border-color: var(--primary);
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
}

.btn-outline-primary {
  color: var(--primary);
  border-color: var(--primary);
}

.btn-outline-primary:hover {
  background-color: var(--primary);
  border-color: var(--primary);
}

.btn-secondary {
  background-color: var(--secondary);
  border-color: var(--secondary);
}

.btn-secondary:hover {
  background-color: #e64a19;
  border-color: #e64a19;
}

/* Badges */
.badge {
  font-weight: 500;
  padding: 0.4em 0.8em;
  border-radius: var(--radius-sm);
}

.badge-primary {
  background-color: var(--primary);
}

.badge-secondary {
  background-color: var(--secondary);
}

.badge-success {
  background-color: var(--success);
}

.badge-warning {
  background-color: var(--warning);
  color: var(--dark);
}

/* Filters */
.filter-card {
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-md);
  margin-bottom: var(--spacing-lg);
}

.filter-card .card-header {
  background-color: white;
  border-bottom: 1px solid var(--gray-light);
  padding: var(--spacing-md) var(--spacing-lg);
}

.filter-card .list-group-item {
  border: none;
  padding: var(--spacing-md) var(--spacing-lg);
  transition: all 0.2s ease;
}

.filter-card .list-group-item:hover {
  background-color: var(--primary-light);
}

.filter-card .list-group-item.active {
  background-color: var(--primary);
  color: white;
}

/* Event Details */
.event-header {
  margin-bottom: var(--spacing-xl);
}

.event-header h1 {
  font-size: 2.5rem;
  margin-bottom: var(--spacing-sm);
}

.event-header .lead {
  font-size: 1.2rem;
  color: var(--gray);
  margin-bottom: var(--spacing-md);
}

.event-image {
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
}

.event-image img {
  width: 100%;
  height: auto;
}

/* Tabs */
.nav-tabs {
  border-bottom: 2px solid var(--gray-light);
  margin-bottom: var(--spacing-lg);
}

.nav-tabs .nav-link {
  border: none;
  color: var(--gray);
  font-weight: 500;
  padding: var(--spacing-md) var(--spacing-lg);
  margin-right: var(--spacing-md);
  transition: all 0.2s ease;
}

.nav-tabs .nav-link:hover {
  color: var(--primary);
  border-bottom: 2px solid var(--primary-light);
}

.nav-tabs .nav-link.active {
  color: var(--primary);
  background-color: transparent;
  border-bottom: 2px solid var(--primary);
}

/* Tables */
.table {
  box-shadow: var(--shadow-sm);
  border-radius: var(--radius-md);
  overflow: hidden;
}

.table thead th {
  background-color: var(--primary-light);
  color: var(--primary-dark);
  font-weight: 600;
  border-top: none;
  padding: var(--spacing-md) var(--spacing-lg);
}

.table tbody td {
  padding: var(--spacing-md) var(--spacing-lg);
  vertical-align: middle;
}

.table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(0, 0, 0, 0.02);
}

/* Accordion */
.accordion {
  box-shadow: var(--shadow-md);
  border-radius: var(--radius-md);
  overflow: hidden;
}

.accordion-item {
  border: none;
  border-bottom: 1px solid var(--gray-light);
}

.accordion-item:last-child {
  border-bottom: none;
}

.accordion-button {
  padding: var(--spacing-lg);
  font-weight: 500;
  background-color: white;
}

.accordion-button:not(.collapsed) {
  color: var(--primary);
  background-color: var(--primary-light);
}

.accordion-body {
  padding: var(--spacing-lg);
}

/* Footer */
footer {
  background-color: var(--dark);
  color: white;
  padding: var(--spacing-xl) 0;
  margin-top: var(--spacing-xl);
}

footer h5 {
  font-size: 1.2rem;
  margin-bottom: var(--spacing-lg);
  color: white;
}

footer a {
  color: rgba(255, 255, 255, 0.8);
  transition: color 0.2s ease;
}

footer a:hover {
  color: white;
  text-decoration: none;
}

footer hr {
  background-color: rgba(255, 255, 255, 0.1);
  margin: var(--spacing-lg) 0;
}

/* Responsive Adjustments */
@media (max-width: 992px) {
  .carousel-item img {
    height: 400px;
  }

  .carousel-caption h3 {
    font-size: 1.8rem;
  }

  .hero-section h1 {
    font-size: 2rem;
  }
}

@media (max-width: 768px) {
  .carousel-item img {
    height: 300px;
  }

  .carousel-caption {
    padding: var(--spacing-lg) var(--spacing-md) var(--spacing-md);
  }

  .carousel-caption h3 {
    font-size: 1.5rem;
  }

  .carousel-caption p {
    font-size: 1rem;
  }

  .hero-section {
    padding: var(--spacing-lg) 0;
  }

  .hero-section h1 {
    font-size: 1.8rem;
  }

  .hero-section .lead {
    font-size: 1.1rem;
  }

  .card-img-top {
    height: 180px;
  }
}

@media (max-width: 576px) {
  .carousel-item img {
    height: 250px;
  }

  .carousel-caption h3 {
    font-size: 1.3rem;
  }

  .hero-section h1 {
    font-size: 1.5rem;
  }

  .card-img-top {
    height: 160px;
  }

  .navbar .form-inline {
    display: none;
  }
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.fade-in {
  animation: fadeIn 0.5s ease-out forwards;
}

/* Loading Spinner */
.spinner-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.spinner-border {
  width: 3rem;
  height: 3rem;
  color: var(--primary);
}

/* Tooltips */
.tooltip {
  font-family: var(--font-primary);
}

.tooltip-inner {
  background-color: var(--dark);
  border-radius: var(--radius-sm);
  padding: var(--spacing-sm) var(--spacing-md);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--light);
}

::-webkit-scrollbar-thumb {
  background: var(--gray-light);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gray);
}

/* Accessibility Focus Styles */
a:focus, button:focus, input:focus, select:focus, textarea:focus {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

/* Skip to content link for accessibility */
.skip-link {
  position: absolute;
  top: -40px;
  left: 0;
  background: var(--primary);
  color: white;
  padding: 8px;
  z-index: 100;
}

.skip-link:focus {
  top: 0;
}

/* Dark mode toggle */
.dark-mode-toggle {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background-color: var(--dark);
  color: white;
  border: none;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: var(--shadow-md);
  z-index: 1000;
  transition: all 0.3s ease;
}

.dark-mode-toggle:hover {
  transform: scale(1.1);
}

/* Custom Styles for Event Cards */
.event-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.event-card .card-body {
  flex: 1;
}

.event-card .card-footer {
  margin-top: auto;
}

/* Event Details Page Enhancements */
.event-meta {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: var(--spacing-lg);
}

.event-meta-item {
  display: flex;
  align-items: center;
  margin-right: var(--spacing-lg);
  margin-bottom: var(--spacing-sm);
  color: var(--gray);
}

.event-meta-item i {
  margin-right: var(--spacing-xs);
  color: var(--primary);
}

/* Share buttons */
.share-buttons {
  display: flex;
  gap: var(--spacing-sm);
}

.share-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  transition: transform 0.2s ease;
}

.share-button:hover {
  transform: scale(1.1);
}

.share-facebook {
  background-color: #3b5998;
}

.share-twitter {
  background-color: #1da1f2;
}

.share-instagram {
  background: linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888);
}

.share-email {
  background-color: #34a853;
}

/* Countdown Timer */
.countdown {
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
  margin: var(--spacing-lg) 0;
}

.countdown-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: var(--primary);
  color: white;
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  min-width: 80px;
}

.countdown-number {
  font-size: 2rem;
  font-weight: 700;
  line-height: 1;
}

.countdown-label {
  font-size: 0.8rem;
  text-transform: uppercase;
  margin-top: var(--spacing-xs);
}

/* Back to top button */
.back-to-top {
  position: fixed;
  bottom: 20px;
  right: 80px;
  background-color: var(--primary);
  color: white;
  border: none;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: var(--shadow-md);
  z-index: 1000;
  transition: all 0.3s ease;
  opacity: 0;
  visibility: hidden;
}

.back-to-top.visible {
  opacity: 1;
  visibility: visible;
}

.back-to-top:hover {
  transform: translateY(-5px);
}

/* Cookie consent banner */
.cookie-banner {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  box-shadow: var(--shadow-lg);
  padding: var(--spacing-lg);
  z-index: 1001;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.cookie-banner p {
  margin-bottom: 0;
  margin-right: var(--spacing-lg);
}

.cookie-banner .btn-group {
  flex-shrink: 0;
}

/* Newsletter signup */
.newsletter-form {
  display: flex;
  margin-top: var(--spacing-md);
}

.newsletter-form .form-control {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-right: none;
}

.newsletter-form .btn {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

/* Notification badge */
.notification-badge {
  position: absolute;
  top: 0;
  right: 0;
  background-color: var(--danger);
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: 700;
  transform: translate(50%, -50%);
}

/* Skeleton loading */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: var(--radius-sm);
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.skeleton-text {
  height: 1rem;
  margin-bottom: 0.5rem;
  width: 100%;
}

.skeleton-text:last-child {
  width: 80%;
}

.skeleton-image {
  height: 200px;
  width: 100%;
}

/* Hover effects */
.hover-zoom {
  transition: transform 0.3s ease;
}

.hover-zoom:hover {
  transform: scale(1.05);
}

.hover-shadow {
  transition: box-shadow 0.3s ease;
}

.hover-shadow:hover {
  box-shadow: var(--shadow-lg);
}

/* Custom checkboxes and radio buttons */
.custom-control-input:checked ~ .custom-control-label::before {
  background-color: var(--primary);
  border-color: var(--primary);
}

/* Utility classes */
.bg-primary-light {
  background-color: var(--primary-light);
}

.text-primary {
  color: var(--primary) !important;
}

.rounded-lg {
  border-radius: var(--radius-lg) !important;
}

.shadow-sm {
  box-shadow: var(--shadow-sm) !important;
}

.shadow-md {
  box-shadow: var(--shadow-md) !important;
}

.shadow-lg {
  box-shadow: var(--shadow-lg) !important;
}

.font-weight-bold {
  font-weight: 700 !important;
}

.text-truncate-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.text-truncate-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Timeline for Event Schedule */
.timeline {
  position: relative;
  padding: 20px 0;
}

.timeline::before {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  left: 80px;
  width: 4px;
  background-color: var(--primary-light);
  border-radius: 2px;
}

.timeline-item {
  position: relative;
  display: flex;
  margin-bottom: 30px;
}

.timeline-date {
  width: 80px;
  min-width: 80px;
  margin-right: 30px;
  z-index: 1;
}

.timeline-content {
  flex-grow: 1;
  position: relative;
  padding-left: 20px;
}

.timeline-content::before {
  content: '';
  position: absolute;
  top: 15px;
  left: -8px;
  width: 16px;
  height: 16px;
  background-color: var(--primary);
  border-radius: 50%;
  z-index: 1;
}

@media (max-width: 768px) {
  .timeline::before {
    left: 60px;
  }

  .timeline-date {
    width: 60px;
    min-width: 60px;
    margin-right: 20px;
  }

  .timeline-content {
    padding-left: 15px;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }

  body {
    font-size: 12pt;
  }

  a[href]:after {
    content: " (" attr(href) ")";
  }

  abbr[title]:after {
    content: " (" attr(title) ")";
  }

  .container {
    max-width: 100% !important;
  }
}
