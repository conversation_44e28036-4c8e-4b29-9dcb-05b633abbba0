<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚨 错误诊断页面</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .error-card {
            border: 3px solid #dc3545;
            margin-bottom: 2rem;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        .error-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.2);
        }
        .error-banner {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 4rem;
            border-radius: 1rem;
            text-align: center;
            margin-bottom: 3rem;
            box-shadow: 0 15px 30px rgba(0,0,0,0.3);
        }
        .status-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 1rem 0;
        }
        .success-status {
            background: #d4edda;
            border-color: #c3e6cb;
        }
        .error-status {
            background: #f8d7da;
            border-color: #f5c6cb;
        }
        .test-button {
            margin: 0.5rem;
            box-shadow: 0 6px 12px rgba(0,0,0,0.3);
            transition: all 0.3s;
            font-weight: bold;
        }
        .test-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 16px rgba(0,0,0,0.4);
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <!-- Error Banner -->
        <div class="error-banner">
            <h1 class="display-2">🚨 错误诊断页面</h1>
            <h2 class="mb-4">帮助您识别和解决网页错误</h2>
            <p class="lead">系统化诊断强制刷新后出现的错误</p>
        </div>

        <!-- System Status Check -->
        <div class="card error-card">
            <div class="card-header bg-danger text-white">
                <h4><i class="fas fa-heartbeat"></i> 系统状态检查</h4>
            </div>
            <div class="card-body">
                <h5>基础服务状态:</h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="status-box success-status">
                            <h6><i class="fas fa-check-circle text-success"></i> Flask应用</h6>
                            <p class="small mb-0">✅ 运行正常 - http://127.0.0.1:5000</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="status-box success-status">
                            <h6><i class="fas fa-check-circle text-success"></i> 静态文件服务</h6>
                            <p class="small mb-0">✅ 图片文件可访问</p>
                        </div>
                    </div>
                </div>

                <h5 class="mt-4">快速测试链接:</h5>
                <div class="d-flex flex-wrap">
                    <a href="/" target="_blank" class="btn btn-primary test-button">
                        <i class="fas fa-home"></i> 主页测试
                    </a>
                    <a href="/events" target="_blank" class="btn btn-success test-button">
                        <i class="fas fa-calendar"></i> 赛事列表
                    </a>
                    <a href="/events/1" target="_blank" class="btn btn-info test-button">
                        <i class="fas fa-trophy"></i> 单个赛事
                    </a>
                    <a href="/static/images/homepage/featured/fifa_world_cup_featured.jpg" target="_blank" class="btn btn-warning test-button">
                        <i class="fas fa-image"></i> 图片直接访问
                    </a>
                </div>
            </div>
        </div>

        <!-- Error Types -->
        <div class="card error-card">
            <div class="card-header bg-warning text-dark">
                <h4><i class="fas fa-exclamation-triangle"></i> 常见错误类型识别</h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>🔴 500 内部服务器错误</h6>
                        <div class="status-box error-status">
                            <p class="small"><strong>症状:</strong> 页面显示"Internal Server Error"</p>
                            <p class="small"><strong>原因:</strong> Python代码错误、导入问题</p>
                            <p class="small"><strong>解决:</strong> 检查控制台错误日志</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>🔴 404 页面未找到</h6>
                        <div class="status-box error-status">
                            <p class="small"><strong>症状:</strong> 页面显示"Not Found"</p>
                            <p class="small"><strong>原因:</strong> 路由配置问题</p>
                            <p class="small"><strong>解决:</strong> 检查URL路径</p>
                        </div>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-md-6">
                        <h6>🔴 模板错误</h6>
                        <div class="status-box error-status">
                            <p class="small"><strong>症状:</strong> 页面部分显示异常</p>
                            <p class="small"><strong>原因:</strong> Jinja2模板语法错误</p>
                            <p class="small"><strong>解决:</strong> 检查模板语法</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>🔴 静态文件错误</h6>
                        <div class="status-box error-status">
                            <p class="small"><strong>症状:</strong> 图片、CSS不加载</p>
                            <p class="small"><strong>原因:</strong> 文件路径错误</p>
                            <p class="small"><strong>解决:</strong> 检查文件路径</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Diagnostic Steps -->
        <div class="card error-card">
            <div class="card-header bg-info text-white">
                <h4><i class="fas fa-search"></i> 错误诊断步骤</h4>
            </div>
            <div class="card-body">
                <h5>步骤1: 查看浏览器控制台</h5>
                <div class="status-box">
                    <ol>
                        <li><strong>按F12</strong> 打开开发者工具</li>
                        <li><strong>切换到Console标签</strong></li>
                        <li><strong>查看红色错误信息</strong></li>
                        <li><strong>记录错误详情</strong></li>
                    </ol>
                    <p class="small text-muted">常见错误信息: "Failed to load resource", "Uncaught TypeError", "404 Not Found"</p>
                </div>

                <h5>步骤2: 检查Network标签</h5>
                <div class="status-box">
                    <ol>
                        <li><strong>切换到Network标签</strong></li>
                        <li><strong>刷新页面</strong></li>
                        <li><strong>查看红色的失败请求</strong></li>
                        <li><strong>点击失败的请求查看详情</strong></li>
                    </ol>
                    <p class="small text-muted">关注状态码: 200(成功), 404(未找到), 500(服务器错误)</p>
                </div>

                <h5>步骤3: 查看Flask控制台</h5>
                <div class="status-box">
                    <p>如果您能看到运行Flask的命令行窗口，查看是否有错误信息输出。</p>
                    <p class="small text-muted">常见错误: ImportError, NameError, TemplateNotFound</p>
                </div>
            </div>
        </div>

        <!-- Quick Fixes -->
        <div class="card error-card">
            <div class="card-header bg-success text-white">
                <h4><i class="fas fa-tools"></i> 快速修复方案</h4>
            </div>
            <div class="card-body">
                <h5>方案1: 重启Flask应用</h5>
                <div class="status-box">
                    <ol>
                        <li>在命令行窗口按 <kbd>Ctrl + C</kbd> 停止应用</li>
                        <li>重新运行 <code>python app.py</code></li>
                        <li>等待应用启动完成</li>
                        <li>重新访问主页</li>
                    </ol>
                </div>

                <h5>方案2: 清除所有缓存</h5>
                <div class="status-box">
                    <ol>
                        <li>按 <kbd>Ctrl + Shift + Delete</kbd> 打开清除数据</li>
                        <li>选择"所有时间"</li>
                        <li>勾选所有缓存选项</li>
                        <li>清除后重新访问</li>
                    </ol>
                </div>

                <h5>方案3: 使用隐私模式</h5>
                <div class="status-box">
                    <ol>
                        <li>按 <kbd>Ctrl + Shift + N</kbd> (Chrome) 或 <kbd>Ctrl + Shift + P</kbd> (Firefox)</li>
                        <li>在隐私模式下访问主页</li>
                        <li>这样可以避免缓存问题</li>
                    </ol>
                </div>
            </div>
        </div>

        <!-- Error Reporting -->
        <div class="card error-card">
            <div class="card-header bg-secondary text-white">
                <h4><i class="fas fa-bug"></i> 错误报告指南</h4>
            </div>
            <div class="card-body">
                <h5>如果需要进一步帮助，请提供以下信息:</h5>
                <div class="status-box">
                    <h6>1. 错误截图</h6>
                    <ul class="small">
                        <li>完整的错误页面截图</li>
                        <li>浏览器控制台错误截图</li>
                        <li>Network标签的失败请求截图</li>
                    </ul>

                    <h6>2. 错误详情</h6>
                    <ul class="small">
                        <li>具体的错误信息文本</li>
                        <li>出现错误的具体操作步骤</li>
                        <li>使用的浏览器和版本</li>
                    </ul>

                    <h6>3. 系统信息</h6>
                    <ul class="small">
                        <li>操作系统 (Windows/Mac/Linux)</li>
                        <li>Python版本</li>
                        <li>是否修改过代码</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Test All Links -->
        <div class="text-center mt-5">
            <h3>🎯 测试所有功能</h3>
            <p class="lead">点击下面的按钮测试各个功能是否正常</p>
            
            <div class="d-flex justify-content-center flex-wrap">
                <a href="/" target="_blank" class="btn btn-primary btn-lg test-button">
                    <i class="fas fa-home"></i> 主页
                </a>
                <a href="/events" target="_blank" class="btn btn-success btn-lg test-button">
                    <i class="fas fa-list"></i> 赛事列表
                </a>
                <a href="/categories/Football" target="_blank" class="btn btn-info btn-lg test-button">
                    <i class="fas fa-futbol"></i> 足球分类
                </a>
                <a href="/about" target="_blank" class="btn btn-warning btn-lg test-button">
                    <i class="fas fa-info-circle"></i> 关于页面
                </a>
            </div>

            <div class="mt-3">
                <h5>图片直接访问测试:</h5>
                <div class="d-flex justify-content-center flex-wrap">
                    <a href="/static/images/homepage/featured/fifa_world_cup_featured.jpg" target="_blank" class="btn btn-outline-primary test-button">
                        <i class="fas fa-image"></i> FIFA图片
                    </a>
                    <a href="/static/images/homepage/categories/football_category.jpg" target="_blank" class="btn btn-outline-success test-button">
                        <i class="fas fa-image"></i> 足球分类图片
                    </a>
                    <a href="/static/image_test.html" target="_blank" class="btn btn-outline-info test-button">
                        <i class="fas fa-test-tube"></i> 图片测试页面
                    </a>
                </div>
            </div>
        </div>

        <!-- Emergency Reset -->
        <div class="alert alert-danger mt-5">
            <h5><i class="fas fa-exclamation-triangle"></i> 紧急重置选项</h5>
            <p>如果所有方法都无效，可能需要重置到之前的工作状态。请告诉我具体的错误信息，我会提供针对性的解决方案。</p>
            <p><strong>重要:</strong> 在进行任何重置操作前，请先尝试上述诊断步骤。</p>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 自动检测页面加载状态
        window.addEventListener('load', function() {
            console.log('✅ 错误诊断页面加载成功');
            console.log('🔍 请按F12查看控制台，然后测试其他页面');
        });
    </script>
</body>
</html>
