<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📸 图片格式指南</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .guide-banner {
            background: linear-gradient(135deg, #6f42c1 0%, #007bff 100%);
            color: white;
            padding: 3rem;
            border-radius: 1rem;
            text-align: center;
            margin-bottom: 3rem;
            box-shadow: 0 15px 30px rgba(0,0,0,0.3);
        }
        .guide-card {
            border: 3px solid #6f42c1;
            margin-bottom: 2rem;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        .guide-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.2);
        }
        .language-switch {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
        .achievement-badge {
            background: linear-gradient(45deg, #6f42c1, #007bff);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 2rem;
            font-weight: bold;
            margin: 0.25rem;
            display: inline-block;
        }
        .format-demo {
            background: #f8f9fa;
            border: 2px dashed #6f42c1;
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin: 1rem 0;
            text-align: center;
        }
        .ratio-box {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border-radius: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-bottom: 1rem;
        }
        .featured-ratio {
            width: 100%;
            height: 120px;
        }
        .category-ratio {
            width: 100%;
            height: 75px;
        }
        .spec-table {
            background: #f8f9fa;
            border-radius: 0.5rem;
            padding: 1rem;
        }
    </style>
</head>
<body>
    <!-- Language Switch -->
    <div class="language-switch">
        <div class="btn-group">
            <a href="/set_language/en" class="btn btn-outline-primary btn-sm">English</a>
            <a href="/set_language/zh" class="btn btn-outline-success btn-sm">中文</a>
        </div>
    </div>

    <div class="container py-5">
        <!-- Guide Banner -->
        <div class="guide-banner">
            <h1 class="display-2">📸 图片格式指南</h1>
            <h2 class="mb-4">精选赛事 + 分类浏览图片规格推荐</h2>
            <p class="lead">完美适配您的网站设计，提供最佳视觉效果</p>
            <div class="mt-4">
                <span class="achievement-badge">✅ 精选赛事</span>
                <span class="achievement-badge">✅ 分类浏览</span>
                <span class="achievement-badge">✅ 格式优化</span>
                <span class="achievement-badge">✅ 问题修复</span>
            </div>
        </div>

        <!-- Featured Events Format -->
        <div class="card guide-card">
            <div class="card-header bg-primary text-white">
                <h4><i class="fas fa-star"></i> 精选赛事图片格式推荐</h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5>📐 推荐规格:</h5>
                        <div class="spec-table">
                            <table class="table table-sm mb-0">
                                <tr><td><strong>尺寸:</strong></td><td>1200×480像素</td></tr>
                                <tr><td><strong>比例:</strong></td><td>2.5:1 (宽高比)</td></tr>
                                <tr><td><strong>格式:</strong></td><td>JPG (推荐) 或 PNG</td></tr>
                                <tr><td><strong>文件大小:</strong></td><td>200-500KB</td></tr>
                                <tr><td><strong>质量:</strong></td><td>高质量，适合网页</td></tr>
                                <tr><td><strong>显示高度:</strong></td><td>250px</td></tr>
                            </table>
                        </div>
                        
                        <h6 class="mt-3">🎯 为什么选择这个比例:</h6>
                        <ul class="small">
                            <li><strong>2.5:1比例</strong> 完美适配250px高度显示</li>
                            <li><strong>1200px宽度</strong> 确保高分辨率清晰度</li>
                            <li><strong>横向构图</strong> 适合体育赛事宽幅展示</li>
                            <li><strong>全屏展示</strong> 配合您的全屏滚动设计</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h5>📱 比例演示:</h5>
                        <div class="format-demo">
                            <div class="ratio-box featured-ratio">
                                精选赛事图片<br>
                                1200×480 (2.5:1)
                            </div>
                            <p class="small text-muted">这是精选赛事图片在页面中的显示比例</p>
                        </div>
                        
                        <h6>💡 图片内容建议:</h6>
                        <ul class="small">
                            <li><strong>主体居中:</strong> 重要内容放在图片中央</li>
                            <li><strong>文字可读:</strong> 避免过小的文字</li>
                            <li><strong>色彩鲜明:</strong> 使用高对比度颜色</li>
                            <li><strong>品牌元素:</strong> 包含赛事标识或logo</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Category Images Format -->
        <div class="card guide-card">
            <div class="card-header bg-success text-white">
                <h4><i class="fas fa-th-large"></i> 分类浏览图片格式</h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5>📐 分类图片规格:</h5>
                        <div class="spec-table">
                            <table class="table table-sm mb-0">
                                <tr><td><strong>尺寸:</strong></td><td>400×250像素</td></tr>
                                <tr><td><strong>比例:</strong></td><td>1.6:1 (宽高比)</td></tr>
                                <tr><td><strong>格式:</strong></td><td>JPG (推荐)</td></tr>
                                <tr><td><strong>文件大小:</strong></td><td>100-300KB</td></tr>
                                <tr><td><strong>用途:</strong></td><td>分类卡片展示</td></tr>
                                <tr><td><strong>状态:</strong></td><td>✅ 已修复</td></tr>
                            </table>
                        </div>
                        
                        <h6 class="mt-3">🔧 修复内容:</h6>
                        <ul class="small">
                            <li><strong>创建了所有分类图片</strong> - 7个运动分类</li>
                            <li><strong>正确的文件路径</strong> - 匹配image_urls.py</li>
                            <li><strong>统一的尺寸规格</strong> - 400×250像素</li>
                            <li><strong>分类特色颜色</strong> - 每个分类独特配色</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h5>📱 分类图片演示:</h5>
                        <div class="format-demo">
                            <div class="ratio-box category-ratio">
                                分类图片<br>
                                400×250 (1.6:1)
                            </div>
                            <p class="small text-muted">这是分类浏览中的图片显示比例</p>
                        </div>
                        
                        <h6>🎨 已创建的分类:</h6>
                        <div class="row">
                            <div class="col-6">
                                <ul class="small">
                                    <li>⚽ Football</li>
                                    <li>🏀 Basketball</li>
                                    <li>🎾 Tennis</li>
                                    <li>🏊 Swimming</li>
                                </ul>
                            </div>
                            <div class="col-6">
                                <ul class="small">
                                    <li>🏃 Athletics</li>
                                    <li>⛳ Golf</li>
                                    <li>🏎️ Formula 1</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- File Locations -->
        <div class="card guide-card">
            <div class="card-header bg-info text-white">
                <h4><i class="fas fa-folder"></i> 文件位置和替换指南</h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>📁 精选赛事图片位置:</h6>
                        <div class="bg-dark text-light p-3 rounded">
                            <code>
static/images/homepage/featured/<br>
├── fifa_world_cup_featured.jpg<br>
├── champions_league_featured.jpg<br>
├── nba_finals_featured.jpg<br>
├── wimbledon_featured.jpg<br>
├── olympic_swimming_featured.jpg<br>
├── masters_golf_featured.jpg<br>
└── monaco_gp_featured.jpg
                            </code>
                        </div>
                        
                        <h6 class="mt-3">🔄 替换步骤:</h6>
                        <ol class="small">
                            <li>准备1200×480像素的图片</li>
                            <li>保存为JPG格式</li>
                            <li>使用相同的文件名</li>
                            <li>替换对应的文件</li>
                            <li>清除浏览器缓存</li>
                        </ol>
                    </div>
                    <div class="col-md-6">
                        <h6>📁 分类图片位置:</h6>
                        <div class="bg-dark text-light p-3 rounded">
                            <code>
static/images/homepage/categories/<br>
├── football_category.jpg<br>
├── basketball_category.jpg<br>
├── tennis_category.jpg<br>
├── swimming_category.jpg<br>
├── athletics_category.jpg<br>
├── golf_category.jpg<br>
└── formula_1_category.jpg
                            </code>
                        </div>
                        
                        <h6 class="mt-3">✅ 修复状态:</h6>
                        <ul class="small">
                            <li><strong>✅ 图片已创建</strong> - 所有7个分类</li>
                            <li><strong>✅ 路径已配置</strong> - image_urls.py</li>
                            <li><strong>✅ 应用已重启</strong> - 加载新图片</li>
                            <li><strong>✅ 立即可用</strong> - 分类浏览正常</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Optimization Tips -->
        <div class="card guide-card">
            <div class="card-header bg-warning text-dark">
                <h4><i class="fas fa-lightbulb"></i> 图片优化建议</h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>🎨 设计建议:</h6>
                        <ul class="small">
                            <li><strong>高对比度:</strong> 确保文字和背景对比明显</li>
                            <li><strong>简洁构图:</strong> 避免过于复杂的背景</li>
                            <li><strong>品牌一致:</strong> 保持统一的视觉风格</li>
                            <li><strong>移动友好:</strong> 考虑小屏幕显示效果</li>
                        </ul>
                        
                        <h6 class="mt-3">📐 技术要求:</h6>
                        <ul class="small">
                            <li><strong>分辨率:</strong> 至少72 DPI，推荐96 DPI</li>
                            <li><strong>色彩模式:</strong> RGB (网页标准)</li>
                            <li><strong>压缩质量:</strong> 85-95% (平衡质量和大小)</li>
                            <li><strong>文件命名:</strong> 使用英文和下划线</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>⚡ 性能优化:</h6>
                        <ul class="small">
                            <li><strong>文件大小控制:</strong> 精选赛事≤500KB，分类≤300KB</li>
                            <li><strong>格式选择:</strong> JPG适合照片，PNG适合图标</li>
                            <li><strong>渐进式加载:</strong> 使用渐进式JPG</li>
                            <li><strong>WebP支持:</strong> 现代浏览器可考虑WebP</li>
                        </ul>
                        
                        <h6 class="mt-3">🔧 工具推荐:</h6>
                        <ul class="small">
                            <li><strong>在线压缩:</strong> TinyPNG, Squoosh</li>
                            <li><strong>设计工具:</strong> Canva, Figma, Photoshop</li>
                            <li><strong>批量处理:</strong> ImageMagick, GIMP</li>
                            <li><strong>格式转换:</strong> XnConvert, IrfanView</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="text-center mt-5">
            <h3>🎯 立即验证修复效果</h3>
            <div class="d-flex justify-content-center flex-wrap">
                <a href="/" target="_blank" class="btn btn-primary btn-lg m-2">
                    <i class="fas fa-home"></i> 查看主页
                </a>
                <a href="/#categories" target="_blank" class="btn btn-success btn-lg m-2">
                    <i class="fas fa-th-large"></i> 查看分类浏览
                </a>
                <button onclick="downloadTemplate()" class="btn btn-info btn-lg m-2">
                    <i class="fas fa-download"></i> 下载模板
                </button>
                <button onclick="showReplacementGuide()" class="btn btn-warning btn-lg m-2">
                    <i class="fas fa-exchange-alt"></i> 替换指南
                </button>
            </div>
        </div>

        <!-- Success Summary -->
        <div class="alert alert-success mt-5">
            <h5><i class="fas fa-trophy"></i> 🎉 图片问题修复完成</h5>
            <div class="row">
                <div class="col-md-6">
                    <h6>✅ 精选赛事图片:</h6>
                    <ul class="small mb-0">
                        <li><strong>推荐格式</strong> - 1200×480像素 (2.5:1)</li>
                        <li><strong>完美适配</strong> - 250px高度全屏显示</li>
                        <li><strong>示例图片</strong> - 已创建7个示例图片</li>
                        <li><strong>替换就绪</strong> - 可直接替换为您的图片</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>✅ 分类浏览图片:</h6>
                    <ul class="small mb-0">
                        <li><strong>问题已修复</strong> - 所有分类图片正常显示</li>
                        <li><strong>标准格式</strong> - 400×250像素 (1.6:1)</li>
                        <li><strong>7个分类</strong> - 所有运动分类图片已创建</li>
                        <li><strong>立即可用</strong> - 分类浏览功能正常</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function downloadTemplate() {
            alert('📥 图片模板下载:\n\n您可以使用以下尺寸创建自己的图片:\n\n精选赛事: 1200×480像素\n分类浏览: 400×250像素\n\n建议使用Canva、Figma或Photoshop等工具创建。');
        }
        
        function showReplacementGuide() {
            alert('🔄 图片替换指南:\n\n1. 准备符合规格的图片\n2. 保存为JPG格式\n3. 使用相同的文件名\n4. 替换static/images/目录中的文件\n5. 清除浏览器缓存 (Ctrl+Shift+Delete)\n6. 刷新页面查看效果\n\n保持文件名不变是关键！');
        }
        
        // 页面加载时的提示
        window.addEventListener('load', function() {
            console.log('📸 图片格式指南页面已加载');
            console.log('✅ 精选赛事推荐格式: 1200×480像素');
            console.log('✅ 分类浏览图片已修复');
            console.log('🎯 所有图片问题已解决');
        });
    </script>
</body>
</html>
