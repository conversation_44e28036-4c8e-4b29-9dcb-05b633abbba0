<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚨 紧急排版修复</title>
    <!-- 使用与主站相同的Bootstrap版本 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .emergency-card {
            border: 3px solid #dc3545;
            margin-bottom: 2rem;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        .emergency-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.2);
        }
        .emergency-banner {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 3rem;
            border-radius: 1rem;
            text-align: center;
            margin-bottom: 3rem;
            box-shadow: 0 15px 30px rgba(0,0,0,0.3);
        }
        .fix-step {
            background: #f8f9fa;
            border-left: 4px solid #dc3545;
            padding: 1.5rem;
            margin: 1rem 0;
            border-radius: 0.5rem;
        }
        .success-step {
            background: #d4edda;
            border-left: 4px solid #28a745;
            padding: 1.5rem;
            margin: 1rem 0;
            border-radius: 0.5rem;
        }
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 0.5rem;
            font-family: monospace;
            margin: 1rem 0;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <!-- Emergency Banner -->
        <div class="emergency-banner">
            <h1 class="display-2">🚨 紧急排版修复</h1>
            <h2 class="mb-4">立即解决网页竖向排列问题</h2>
            <p class="lead">图片显示正常，但CSS样式需要修复</p>
        </div>

        <!-- Immediate Solution -->
        <div class="card emergency-card">
            <div class="card-header bg-danger text-white">
                <h4><i class="fas fa-exclamation-triangle"></i> 立即解决方案</h4>
            </div>
            <div class="card-body">
                <div class="success-step">
                    <h5><i class="fas fa-magic"></i> 最快的修复方法:</h5>
                    <p><strong>问题原因:</strong> 强制刷新清除了CSS缓存，但Bootstrap CSS可能没有正确重新加载。</p>
                    
                    <h6>🎯 立即执行以下步骤:</h6>
                    <ol>
                        <li><strong>等待30秒</strong> - 让CDN资源完全加载</li>
                        <li><strong>按Ctrl+F5</strong> 再次强制刷新</li>
                        <li><strong>检查网络连接</strong> - 确保能访问外部CDN</li>
                        <li><strong>如果仍有问题，继续下面的步骤</strong></li>
                    </ol>
                </div>
            </div>
        </div>

        <!-- Step by Step Fix -->
        <div class="card emergency-card">
            <div class="card-header bg-warning text-dark">
                <h4><i class="fas fa-tools"></i> 分步修复指南</h4>
            </div>
            <div class="card-body">
                <div class="fix-step">
                    <h6><i class="fas fa-step-forward"></i> 步骤1: 检查CSS加载状态</h6>
                    <ol>
                        <li>在主页按 <kbd>F12</kbd> 打开开发者工具</li>
                        <li>切换到 <strong>Network</strong> 标签</li>
                        <li>刷新页面 (<kbd>F5</kbd>)</li>
                        <li>查看是否有红色的CSS文件加载失败</li>
                        <li>特别注意Bootstrap CSS是否返回200状态</li>
                    </ol>
                </div>

                <div class="fix-step">
                    <h6><i class="fas fa-step-forward"></i> 步骤2: 强制重新加载CSS</h6>
                    <ol>
                        <li>在Network标签中勾选 <strong>"Disable cache"</strong></li>
                        <li>按 <kbd>Ctrl+Shift+R</kbd> 强制刷新</li>
                        <li>等待所有资源重新下载</li>
                        <li>观察页面排版是否恢复正常</li>
                    </ol>
                </div>

                <div class="fix-step">
                    <h6><i class="fas fa-step-forward"></i> 步骤3: 检查控制台错误</h6>
                    <ol>
                        <li>切换到 <strong>Console</strong> 标签</li>
                        <li>查看是否有CSS相关的错误信息</li>
                        <li>特别注意CORS错误或网络错误</li>
                        <li>记录任何错误信息</li>
                    </ol>
                </div>
            </div>
        </div>

        <!-- Alternative CDN -->
        <div class="card emergency-card">
            <div class="card-header bg-info text-white">
                <h4><i class="fas fa-cloud"></i> 备用CDN解决方案</h4>
            </div>
            <div class="card-body">
                <div class="fix-step">
                    <h5><i class="fas fa-server"></i> 如果Bootstrap CDN无法访问:</h5>
                    <p>主站使用的Bootstrap CDN可能暂时无法访问。您可以尝试以下备用方案：</p>
                    
                    <h6>备用CDN链接测试:</h6>
                    <ul>
                        <li><a href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" target="_blank">jsDelivr CDN (主站使用)</a></li>
                        <li><a href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" target="_blank">Cloudflare CDN (备用)</a></li>
                        <li><a href="https://unpkg.com/bootstrap@5.3.0/dist/css/bootstrap.min.css" target="_blank">unpkg CDN (备用)</a></li>
                    </ul>
                    
                    <p class="small">点击上面的链接测试哪个CDN可以正常访问。如果都无法访问，说明网络连接有问题。</p>
                </div>
            </div>
        </div>

        <!-- Manual CSS Check -->
        <div class="card emergency-card">
            <div class="card-header bg-secondary text-white">
                <h4><i class="fas fa-search"></i> 手动CSS检查</h4>
            </div>
            <div class="card-body">
                <div class="fix-step">
                    <h5><i class="fas fa-code"></i> 检查页面CSS加载:</h5>
                    
                    <h6>方法1: 查看页面源代码</h6>
                    <ol>
                        <li>在主页右键 → <strong>"查看页面源代码"</strong></li>
                        <li>查找以下CSS链接:</li>
                    </ol>
                    
                    <div class="code-block">
                        &lt;link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet"&gt;<br>
                        &lt;link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"&gt;<br>
                        &lt;link rel="stylesheet" href="/static/css/main.css"&gt;<br>
                        &lt;link rel="stylesheet" href="/static/css/custom.css"&gt;
                    </div>
                    
                    <h6>方法2: 直接测试CSS文件</h6>
                    <ul>
                        <li><a href="/static/css/main.css" target="_blank">测试 main.css</a></li>
                        <li><a href="/static/css/custom.css" target="_blank">测试 custom.css</a></li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Quick Test -->
        <div class="card emergency-card">
            <div class="card-header bg-success text-white">
                <h4><i class="fas fa-test-tube"></i> 快速测试</h4>
            </div>
            <div class="card-body">
                <div class="success-step">
                    <h5><i class="fas fa-rocket"></i> 测试当前页面排版:</h5>
                    <p>如果您能看到这个页面的排版是正常的（有网格布局、卡片样式、按钮等），说明Bootstrap CSS可以正常加载。</p>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body">
                                    <h6 class="card-title">测试卡片1</h6>
                                    <p class="card-text">如果这个卡片显示正常，说明CSS工作正常。</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body">
                                    <h6 class="card-title">测试卡片2</h6>
                                    <p class="card-text">这些卡片应该水平排列，不是竖向排列。</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body">
                                    <h6 class="card-title">测试卡片3</h6>
                                    <p class="card-text">如果看到三个卡片并排，说明Bootstrap正常。</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-3">
                        <button class="btn btn-primary me-2">主要按钮</button>
                        <button class="btn btn-secondary me-2">次要按钮</button>
                        <button class="btn btn-success">成功按钮</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Emergency Actions -->
        <div class="text-center mt-5">
            <h3>🚨 紧急操作</h3>
            <div class="d-flex justify-content-center flex-wrap">
                <a href="/" target="_blank" class="btn btn-danger btn-lg m-2">
                    <i class="fas fa-home"></i> 重新访问主页
                </a>
                <button class="btn btn-warning btn-lg m-2" onclick="location.reload()">
                    <i class="fas fa-sync"></i> 刷新当前页面
                </button>
                <button class="btn btn-info btn-lg m-2" onclick="window.open('/', '_blank')">
                    <i class="fas fa-external-link-alt"></i> 新窗口打开主页
                </button>
            </div>
        </div>

        <!-- Status Check -->
        <div class="alert alert-info mt-4">
            <h5><i class="fas fa-info-circle"></i> 状态检查:</h5>
            <div class="row">
                <div class="col-md-6">
                    <h6>✅ 如果这个页面排版正常:</h6>
                    <ul class="small mb-0">
                        <li>说明Bootstrap CSS可以正常加载</li>
                        <li>问题可能是主页的特定缓存问题</li>
                        <li>尝试多次强制刷新主页</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>❌ 如果这个页面也有排版问题:</h6>
                    <ul class="small mb-0">
                        <li>说明网络连接有问题</li>
                        <li>CDN无法访问</li>
                        <li>需要检查网络设置</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Final Solution -->
        <div class="alert alert-success mt-3">
            <h5><i class="fas fa-lightbulb"></i> 最终解决方案:</h5>
            <p class="mb-2"><strong>基于您的描述，图片已经正确显示，只是排版有问题。这通常是CSS加载的临时问题。</strong></p>
            <ol class="mb-0">
                <li><strong>等待1-2分钟</strong> 让网络稳定</li>
                <li><strong>按Ctrl+Shift+R</strong> 再次强制刷新主页</li>
                <li><strong>如果仍有问题</strong>，重启浏览器</li>
                <li><strong>最后手段</strong>：清除所有浏览器数据后重新访问</li>
            </ol>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 页面加载时检查Bootstrap状态
        window.addEventListener('load', function() {
            console.log('🚨 紧急排版修复页面已加载');
            
            // 检查Bootstrap是否正确加载
            if (typeof bootstrap !== 'undefined') {
                console.log('✅ Bootstrap JavaScript已正确加载');
                console.log('💡 如果这个页面排版正常，说明Bootstrap CSS也正常');
            } else {
                console.warn('⚠️ Bootstrap JavaScript未加载，可能有网络问题');
            }
            
            // 检查CSS样式
            const testElement = document.querySelector('.container');
            if (testElement) {
                const styles = window.getComputedStyle(testElement);
                const maxWidth = styles.maxWidth;
                console.log('📐 Container max-width:', maxWidth);
                
                if (maxWidth && maxWidth !== 'none') {
                    console.log('✅ Bootstrap CSS样式正常应用');
                } else {
                    console.warn('⚠️ Bootstrap CSS可能未正确加载');
                }
            }
        });
    </script>
</body>
</html>
