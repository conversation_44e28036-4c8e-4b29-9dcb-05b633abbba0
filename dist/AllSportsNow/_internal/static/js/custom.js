// Enhanced UI/UX JavaScript for Sports Events Website

document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href');
            if (targetId === '#') return;
            
            const targetElement = document.querySelector(targetId);
            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth'
                });
            }
        });
    });

    // Back to top button
    const backToTopButton = document.querySelector('.back-to-top');
    if (backToTopButton) {
        window.addEventListener('scroll', () => {
            if (window.pageYOffset > 300) {
                backToTopButton.classList.add('visible');
            } else {
                backToTopButton.classList.remove('visible');
            }
        });

        backToTopButton.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }

    // Dark mode toggle
    const darkModeToggle = document.querySelector('.dark-mode-toggle');
    if (darkModeToggle) {
        const prefersDarkScheme = window.matchMedia('(prefers-color-scheme: dark)');
        const currentTheme = localStorage.getItem('theme');
        
        if (currentTheme === 'dark' || (!currentTheme && prefersDarkScheme.matches)) {
            document.body.classList.add('dark-mode');
            darkModeToggle.innerHTML = '<i class="fas fa-sun"></i>';
        } else {
            darkModeToggle.innerHTML = '<i class="fas fa-moon"></i>';
        }
        
        darkModeToggle.addEventListener('click', () => {
            if (document.body.classList.contains('dark-mode')) {
                document.body.classList.remove('dark-mode');
                localStorage.setItem('theme', 'light');
                darkModeToggle.innerHTML = '<i class="fas fa-moon"></i>';
            } else {
                document.body.classList.add('dark-mode');
                localStorage.setItem('theme', 'dark');
                darkModeToggle.innerHTML = '<i class="fas fa-sun"></i>';
            }
        });
    }

    // Cookie consent banner
    const cookieBanner = document.querySelector('.cookie-banner');
    if (cookieBanner && !localStorage.getItem('cookieConsent')) {
        cookieBanner.style.display = 'flex';
        
        document.querySelector('.accept-cookies').addEventListener('click', () => {
            localStorage.setItem('cookieConsent', 'true');
            cookieBanner.style.display = 'none';
        });
        
        document.querySelector('.decline-cookies').addEventListener('click', () => {
            localStorage.setItem('cookieConsent', 'false');
            cookieBanner.style.display = 'none';
        });
    }

    // Countdown timer
    const countdownElement = document.querySelector('.countdown');
    if (countdownElement) {
        const eventDate = new Date(countdownElement.dataset.eventDate).getTime();
        
        const countdownTimer = setInterval(() => {
            const now = new Date().getTime();
            const distance = eventDate - now;
            
            if (distance < 0) {
                clearInterval(countdownTimer);
                countdownElement.innerHTML = '<div class="alert alert-info">This event has started!</div>';
                return;
            }
            
            const days = Math.floor(distance / (1000 * 60 * 60 * 24));
            const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((distance % (1000 * 60)) / 1000);
            
            countdownElement.innerHTML = `
                <div class="countdown-item">
                    <span class="countdown-number">${days}</span>
                    <span class="countdown-label">Days</span>
                </div>
                <div class="countdown-item">
                    <span class="countdown-number">${hours}</span>
                    <span class="countdown-label">Hours</span>
                </div>
                <div class="countdown-item">
                    <span class="countdown-number">${minutes}</span>
                    <span class="countdown-label">Minutes</span>
                </div>
                <div class="countdown-item">
                    <span class="countdown-number">${seconds}</span>
                    <span class="countdown-label">Seconds</span>
                </div>
            `;
        }, 1000);
    }

    // Lazy loading images
    if ('loading' in HTMLImageElement.prototype) {
        const images = document.querySelectorAll('img[loading="lazy"]');
        images.forEach(img => {
            img.src = img.dataset.src;
        });
    } else {
        // Fallback for browsers that don't support lazy loading
        const script = document.createElement('script');
        script.src = 'https://cdnjs.cloudflare.com/ajax/libs/lazysizes/5.3.2/lazysizes.min.js';
        document.body.appendChild(script);
    }

    // Animate elements when they come into view
    const animateOnScroll = () => {
        const elements = document.querySelectorAll('.animate-on-scroll');
        
        elements.forEach(element => {
            const elementPosition = element.getBoundingClientRect().top;
            const windowHeight = window.innerHeight;
            
            if (elementPosition < windowHeight - 50) {
                element.classList.add('animated');
            }
        });
    };
    
    window.addEventListener('scroll', animateOnScroll);
    animateOnScroll(); // Run once on page load

    // Form validation
    const forms = document.querySelectorAll('.needs-validation');
    Array.from(forms).forEach(form => {
        form.addEventListener('submit', event => {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    });

    // Search autocomplete
    const searchInput = document.querySelector('.search-autocomplete');
    if (searchInput) {
        const searchResults = document.querySelector('.search-results');
        
        searchInput.addEventListener('input', () => {
            const query = searchInput.value.toLowerCase();
            
            if (query.length < 2) {
                searchResults.style.display = 'none';
                return;
            }
            
            // This would typically be an API call to get search results
            // For demo purposes, we'll use a simple array
            const allEvents = [
                'FIFA World Cup 2026',
                'UEFA Champions League Final 2025',
                'Premier League 2025-26',
                'NBA Finals 2025',
                'Wimbledon 2025',
                'Formula 1 Monaco Grand Prix 2026'
            ];
            
            const filteredEvents = allEvents.filter(event => 
                event.toLowerCase().includes(query)
            );
            
            if (filteredEvents.length > 0) {
                searchResults.innerHTML = filteredEvents.map(event => 
                    `<div class="search-result-item">${event}</div>`
                ).join('');
                
                searchResults.style.display = 'block';
                
                // Add click event to search results
                document.querySelectorAll('.search-result-item').forEach(item => {
                    item.addEventListener('click', () => {
                        searchInput.value = item.textContent;
                        searchResults.style.display = 'none';
                    });
                });
            } else {
                searchResults.style.display = 'none';
            }
        });
        
        // Hide search results when clicking outside
        document.addEventListener('click', event => {
            if (!searchInput.contains(event.target) && !searchResults.contains(event.target)) {
                searchResults.style.display = 'none';
            }
        });
    }

    // Share buttons functionality
    document.querySelectorAll('.share-button').forEach(button => {
        button.addEventListener('click', event => {
            event.preventDefault();
            
            const url = window.location.href;
            const title = document.title;
            
            if (button.classList.contains('share-facebook')) {
                window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`, 'facebook-share', 'width=580,height=296');
            } else if (button.classList.contains('share-twitter')) {
                window.open(`https://twitter.com/intent/tweet?text=${encodeURIComponent(title)}&url=${encodeURIComponent(url)}`, 'twitter-share', 'width=550,height=235');
            } else if (button.classList.contains('share-email')) {
                window.location.href = `mailto:?subject=${encodeURIComponent(title)}&body=${encodeURIComponent(url)}`;
            }
        });
    });

    // Event details page - tab persistence
    const eventTabs = document.getElementById('eventTabs');
    if (eventTabs) {
        const hash = window.location.hash;
        if (hash) {
            const tab = new bootstrap.Tab(document.querySelector(`#eventTabs a[href="${hash}"]`));
            tab.show();
        }

        eventTabs.addEventListener('shown.bs.tab', function (event) {
            window.location.hash = event.target.getAttribute('href');
        });
    }

    // Add fade-in animation to cards
    document.querySelectorAll('.card').forEach((card, index) => {
        card.classList.add('fade-in');
        card.style.animationDelay = `${index * 0.1}s`;
    });

    // Newsletter form submission
    const newsletterForm = document.querySelector('.newsletter-form');
    if (newsletterForm) {
        newsletterForm.addEventListener('submit', event => {
            event.preventDefault();
            
            const emailInput = newsletterForm.querySelector('input[type="email"]');
            const email = emailInput.value;
            
            if (email) {
                // This would typically be an API call to subscribe the user
                // For demo purposes, we'll just show a success message
                newsletterForm.innerHTML = '<div class="alert alert-success">Thank you for subscribing!</div>';
            }
        });
    }

    // Filter functionality for event listings
    const filterForm = document.querySelector('.filter-form');
    if (filterForm) {
        filterForm.addEventListener('submit', event => {
            event.preventDefault();
            
            // Get filter values
            const category = filterForm.querySelector('#filterCategory').value;
            const status = filterForm.querySelector('#filterStatus').value;
            const search = filterForm.querySelector('#searchEvent').value;
            
            // Build query string
            let queryString = '';
            if (category) queryString += `category=${category}&`;
            if (status) queryString += `status=${status}&`;
            if (search) queryString += `q=${encodeURIComponent(search)}&`;
            
            // Remove trailing &
            if (queryString.endsWith('&')) {
                queryString = queryString.slice(0, -1);
            }
            
            // Redirect to filtered results
            window.location.href = `?${queryString}`;
        });
        
        // Reset filters
        filterForm.querySelector('button[type="reset"]').addEventListener('click', () => {
            window.location.href = window.location.pathname;
        });
    }
});
