// 终极图片显示修复JavaScript

(function() {
    'use strict';

    console.log('🔧 Ultimate Image Fix Script Loading...');

    // 强制显示图片的函数
    function forceShowImages() {
        console.log('🖼️ Forcing images to show...');

        // 获取所有轮播图图片
        const carouselImages = document.querySelectorAll('#featuredEventsCarousel img');

        carouselImages.forEach((img, index) => {
            // 强制设置样式
            img.style.setProperty('display', 'block', 'important');
            img.style.setProperty('opacity', '1', 'important');
            img.style.setProperty('visibility', 'visible', 'important');
            img.style.setProperty('height', '400px', 'important');
            img.style.setProperty('width', '100%', 'important');
            img.style.setProperty('object-fit', 'cover', 'important');

            // 移除lazy loading
            if (img.hasAttribute('loading')) {
                img.removeAttribute('loading');
            }

            // 添加强制显示类
            img.classList.add('force-show-image');

            console.log(`Image ${index + 1} forced to show:`, img.src);
        });

        // 强制显示轮播项
        const carouselItems = document.querySelectorAll('#featuredEventsCarousel .carousel-item');
        carouselItems.forEach((item, index) => {
            if (index === 0) {
                // 第一个项目设为活动
                item.classList.add('active');
                item.style.setProperty('display', 'block', 'important');
            } else {
                // 其他项目隐藏
                item.classList.remove('active');
                item.style.setProperty('display', 'none', 'important');
            }

            item.style.setProperty('opacity', '1', 'important');
            item.style.setProperty('visibility', 'visible', 'important');
        });
    }

    // 监控图片加载状态
    function monitorImageLoading() {
        const carouselImages = document.querySelectorAll('#featuredEventsCarousel img');

        carouselImages.forEach((img, index) => {
            // 如果图片已经加载
            if (img.complete && img.naturalHeight !== 0) {
                console.log(`✅ Image ${index + 1} already loaded:`, img.src);
                forceShowImages();
            } else {
                // 监听加载事件
                img.addEventListener('load', function() {
                    console.log(`✅ Image ${index + 1} loaded:`, this.src);
                    forceShowImages();
                });

                img.addEventListener('error', function() {
                    console.log(`❌ Image ${index + 1} failed to load:`, this.src);
                });
            }
        });
    }

    // 温和的图片保护机制
    function preventImageHiding() {
        let lastFixTime = 0;
        const observer = new MutationObserver(function(mutations) {
            const now = Date.now();
            // 限制修复频率，避免过度干扰
            if (now - lastFixTime < 1000) return;

            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' &&
                    mutation.attributeName === 'style') {

                    const target = mutation.target;

                    // 只监控图片元素的样式变化
                    if (target.matches('#featuredEventsCarousel .carousel-item.active img')) {
                        const style = target.style;
                        if (style.display === 'none' || style.opacity === '0' || style.visibility === 'hidden') {
                            console.log('🛡️ Protecting active image from hiding');
                            target.style.setProperty('display', 'block', 'important');
                            target.style.setProperty('opacity', '1', 'important');
                            target.style.setProperty('visibility', 'visible', 'important');
                            lastFixTime = now;
                        }
                    }
                }
            });
        });

        // 只观察轮播图容器
        const carousel = document.getElementById('featuredEventsCarousel');
        if (carousel) {
            observer.observe(carousel, {
                attributes: true,
                subtree: true,
                attributeFilter: ['style']
            });
        }
    }

    // 修复轮播图初始化
    function fixCarouselInitialization() {
        const carousel = document.getElementById('featuredEventsCarousel');
        if (!carousel) {
            console.log('❌ Carousel not found');
            return;
        }

        console.log('🎠 Fixing carousel initialization...');

        // 确保第一个项目是活动的
        const firstItem = carousel.querySelector('.carousel-item:first-child');
        if (firstItem) {
            firstItem.classList.add('active');
            console.log('✅ First carousel item set as active');
        }

        // 移除其他项目的active类
        const otherItems = carousel.querySelectorAll('.carousel-item:not(:first-child)');
        otherItems.forEach(item => {
            item.classList.remove('active');
        });

        // 强制显示图片
        forceShowImages();

        // 重新初始化Bootstrap轮播图
        try {
            // 销毁现有实例
            const existingCarousel = bootstrap.Carousel.getInstance(carousel);
            if (existingCarousel) {
                existingCarousel.dispose();
            }

            // 创建新实例
            const newCarousel = new bootstrap.Carousel(carousel, {
                interval: 5000,
                wrap: true,
                keyboard: true,
                pause: 'hover'
            });

            console.log('✅ Carousel reinitialized');

            // 监听轮播事件
            carousel.addEventListener('slide.bs.carousel', function() {
                console.log('🎠 Carousel sliding...');
                setTimeout(forceShowImages, 100);
            });

            carousel.addEventListener('slid.bs.carousel', function() {
                console.log('🎠 Carousel slid complete');
                forceShowImages();
            });

        } catch (error) {
            console.error('❌ Error reinitializing carousel:', error);
        }
    }

    // 主修复函数
    function ultimateImageFix() {
        console.log('🚀 Starting Ultimate Image Fix...');

        // 立即强制显示图片
        forceShowImages();

        // 监控图片加载
        monitorImageLoading();

        // 防止图片被隐藏
        preventImageHiding();

        // 修复轮播图
        fixCarouselInitialization();

        // 减少定期检查频率，避免过度干扰
        setInterval(function() {
            const carousel = document.getElementById('featuredEventsCarousel');
            if (carousel) {
                const activeItem = carousel.querySelector('.carousel-item.active');
                if (activeItem) {
                    const activeImage = activeItem.querySelector('img');
                    if (activeImage && (activeImage.style.display === 'none' || activeImage.style.opacity === '0')) {
                        console.log('🔄 Active image hidden, applying gentle fix...');
                        activeImage.style.setProperty('display', 'block', 'important');
                        activeImage.style.setProperty('opacity', '1', 'important');
                        activeImage.style.setProperty('visibility', 'visible', 'important');
                    }
                }
            }
        }, 3000); // 减少到每3秒检查一次

        console.log('✅ Ultimate Image Fix Complete!');
    }

    // 等待DOM加载完成
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', ultimateImageFix);
    } else {
        ultimateImageFix();
    }

    // 等待页面完全加载
    window.addEventListener('load', function() {
        console.log('🌐 Page fully loaded, applying final fix...');
        setTimeout(ultimateImageFix, 500);
    });

    // 监听语言切换
    document.addEventListener('click', function(e) {
        if (e.target.matches('a[href*="/set_language/"]')) {
            console.log('🌍 Language switching detected');
            // 语言切换后重新应用修复
            setTimeout(ultimateImageFix, 1000);
        }
    });

    // 全局函数，可以在控制台手动调用
    window.forceShowCarouselImages = forceShowImages;
    window.ultimateImageFix = ultimateImageFix;

    console.log('🎯 Ultimate Image Fix Script Loaded!');
    console.log('💡 You can manually call forceShowCarouselImages() or ultimateImageFix() in console');

})();
