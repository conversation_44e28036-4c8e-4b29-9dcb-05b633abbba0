<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 图片显示测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .test-image {
            max-width: 100%;
            height: 200px;
            object-fit: cover;
            border: 2px solid #dee2e6;
            border-radius: 0.5rem;
        }
        .test-card {
            margin-bottom: 2rem;
        }
        .status-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 10;
        }
        .image-container {
            position: relative;
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <h1 class="text-center mb-5">🔍 图片显示测试</h1>
        
        <div class="alert alert-info">
            <h5>测试说明:</h5>
            <p>这个页面直接测试所有创建的图片是否能正确显示。如果图片显示正常，说明图片文件和路径都是正确的。</p>
        </div>

        <!-- Featured Events Images -->
        <h2 class="mb-4">🌟 精选赛事轮播图测试</h2>
        <div class="row">
            <div class="col-md-4">
                <div class="card test-card">
                    <div class="image-container">
                        <img src="/static/images/homepage/featured/fifa_world_cup_featured.jpg" 
                             class="test-image card-img-top" 
                             alt="FIFA世界杯"
                             onload="this.nextElementSibling.textContent='✅ 加载成功'"
                             onerror="this.nextElementSibling.textContent='❌ 加载失败'">
                        <span class="badge bg-secondary status-badge">加载中...</span>
                    </div>
                    <div class="card-body">
                        <h6>FIFA世界杯2026</h6>
                        <small class="text-muted">fifa_world_cup_featured.jpg</small>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card test-card">
                    <div class="image-container">
                        <img src="/static/images/homepage/featured/champions_league_featured.jpg" 
                             class="test-image card-img-top" 
                             alt="欧洲冠军联赛"
                             onload="this.nextElementSibling.textContent='✅ 加载成功'"
                             onerror="this.nextElementSibling.textContent='❌ 加载失败'">
                        <span class="badge bg-secondary status-badge">加载中...</span>
                    </div>
                    <div class="card-body">
                        <h6>欧洲冠军联赛决赛</h6>
                        <small class="text-muted">champions_league_featured.jpg</small>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card test-card">
                    <div class="image-container">
                        <img src="/static/images/homepage/featured/nba_finals_featured.jpg" 
                             class="test-image card-img-top" 
                             alt="NBA总决赛"
                             onload="this.nextElementSibling.textContent='✅ 加载成功'"
                             onerror="this.nextElementSibling.textContent='❌ 加载失败'">
                        <span class="badge bg-secondary status-badge">加载中...</span>
                    </div>
                    <div class="card-body">
                        <h6>NBA总决赛2025</h6>
                        <small class="text-muted">nba_finals_featured.jpg</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-4">
                <div class="card test-card">
                    <div class="image-container">
                        <img src="/static/images/homepage/featured/wimbledon_featured.jpg" 
                             class="test-image card-img-top" 
                             alt="温布尔登"
                             onload="this.nextElementSibling.textContent='✅ 加载成功'"
                             onerror="this.nextElementSibling.textContent='❌ 加载失败'">
                        <span class="badge bg-secondary status-badge">加载中...</span>
                    </div>
                    <div class="card-body">
                        <h6>温布尔登2025</h6>
                        <small class="text-muted">wimbledon_featured.jpg</small>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card test-card">
                    <div class="image-container">
                        <img src="/static/images/homepage/featured/olympic_swimming_featured.jpg" 
                             class="test-image card-img-top" 
                             alt="奥运游泳"
                             onload="this.nextElementSibling.textContent='✅ 加载成功'"
                             onerror="this.nextElementSibling.textContent='❌ 加载失败'">
                        <span class="badge bg-secondary status-badge">加载中...</span>
                    </div>
                    <div class="card-body">
                        <h6>奥运游泳2028</h6>
                        <small class="text-muted">olympic_swimming_featured.jpg</small>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card test-card">
                    <div class="image-container">
                        <img src="/static/images/homepage/featured/masters_golf_featured.jpg" 
                             class="test-image card-img-top" 
                             alt="美国大师赛"
                             onload="this.nextElementSibling.textContent='✅ 加载成功'"
                             onerror="this.nextElementSibling.textContent='❌ 加载失败'">
                        <span class="badge bg-secondary status-badge">加载中...</span>
                    </div>
                    <div class="card-body">
                        <h6>美国大师赛2026</h6>
                        <small class="text-muted">masters_golf_featured.jpg</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Category Images -->
        <h2 class="mb-4 mt-5">🎯 分类浏览卡片图测试</h2>
        <div class="row">
            <div class="col-md-3">
                <div class="card test-card">
                    <div class="image-container">
                        <img src="/static/images/homepage/categories/football_category.jpg" 
                             class="test-image card-img-top" 
                             alt="足球分类"
                             onload="this.nextElementSibling.textContent='✅ 加载成功'"
                             onerror="this.nextElementSibling.textContent='❌ 加载失败'">
                        <span class="badge bg-secondary status-badge">加载中...</span>
                    </div>
                    <div class="card-body">
                        <h6>足球分类</h6>
                        <small class="text-muted">football_category.jpg</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card test-card">
                    <div class="image-container">
                        <img src="/static/images/homepage/categories/basketball_category.jpg" 
                             class="test-image card-img-top" 
                             alt="篮球分类"
                             onload="this.nextElementSibling.textContent='✅ 加载成功'"
                             onerror="this.nextElementSibling.textContent='❌ 加载失败'">
                        <span class="badge bg-secondary status-badge">加载中...</span>
                    </div>
                    <div class="card-body">
                        <h6>篮球分类</h6>
                        <small class="text-muted">basketball_category.jpg</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card test-card">
                    <div class="image-container">
                        <img src="/static/images/homepage/categories/tennis_category.jpg" 
                             class="test-image card-img-top" 
                             alt="网球分类"
                             onload="this.nextElementSibling.textContent='✅ 加载成功'"
                             onerror="this.nextElementSibling.textContent='❌ 加载失败'">
                        <span class="badge bg-secondary status-badge">加载中...</span>
                    </div>
                    <div class="card-body">
                        <h6>网球分类</h6>
                        <small class="text-muted">tennis_category.jpg</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card test-card">
                    <div class="image-container">
                        <img src="/static/images/homepage/categories/swimming_category.jpg" 
                             class="test-image card-img-top" 
                             alt="游泳分类"
                             onload="this.nextElementSibling.textContent='✅ 加载成功'"
                             onerror="this.nextElementSibling.textContent='❌ 加载失败'">
                        <span class="badge bg-secondary status-badge">加载中...</span>
                    </div>
                    <div class="card-body">
                        <h6>游泳分类</h6>
                        <small class="text-muted">swimming_category.jpg</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-3">
                <div class="card test-card">
                    <div class="image-container">
                        <img src="/static/images/homepage/categories/athletics_category.jpg" 
                             class="test-image card-img-top" 
                             alt="田径分类"
                             onload="this.nextElementSibling.textContent='✅ 加载成功'"
                             onerror="this.nextElementSibling.textContent='❌ 加载失败'">
                        <span class="badge bg-secondary status-badge">加载中...</span>
                    </div>
                    <div class="card-body">
                        <h6>田径分类</h6>
                        <small class="text-muted">athletics_category.jpg</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card test-card">
                    <div class="image-container">
                        <img src="/static/images/homepage/categories/golf_category.jpg" 
                             class="test-image card-img-top" 
                             alt="高尔夫分类"
                             onload="this.nextElementSibling.textContent='✅ 加载成功'"
                             onerror="this.nextElementSibling.textContent='❌ 加载失败'">
                        <span class="badge bg-secondary status-badge">加载中...</span>
                    </div>
                    <div class="card-body">
                        <h6>高尔夫分类</h6>
                        <small class="text-muted">golf_category.jpg</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card test-card">
                    <div class="image-container">
                        <img src="/static/images/homepage/categories/formula_1_category.jpg" 
                             class="test-image card-img-top" 
                             alt="F1分类"
                             onload="this.nextElementSibling.textContent='✅ 加载成功'"
                             onerror="this.nextElementSibling.textContent='❌ 加载失败'">
                        <span class="badge bg-secondary status-badge">加载中...</span>
                    </div>
                    <div class="card-body">
                        <h6>F1赛车分类</h6>
                        <small class="text-muted">formula_1_category.jpg</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Results -->
        <div class="alert alert-warning mt-5">
            <h5>测试结果分析:</h5>
            <ul>
                <li><strong>如果所有图片都显示"✅ 加载成功":</strong> 说明图片文件和路径都正确，问题可能在于主页模板或浏览器缓存</li>
                <li><strong>如果有图片显示"❌ 加载失败":</strong> 说明对应的图片文件有问题，需要重新生成</li>
                <li><strong>如果图片显示但主页不显示:</strong> 说明模板渲染有问题，需要检查模板代码</li>
            </ul>
        </div>

        <div class="text-center mt-4">
            <a href="/" class="btn btn-primary btn-lg">
                <i class="fas fa-home"></i> 返回主页测试
            </a>
            <a href="/static/homepage_images_guide.html" class="btn btn-outline-primary btn-lg ms-2">
                <i class="fas fa-book"></i> 查看图片指南
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
