<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网站状态检查</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .status-good { color: #28a745; }
        .status-warning { color: #ffc107; }
        .status-error { color: #dc3545; }
        .test-card {
            margin-bottom: 1rem;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="text-center mb-5">
            <h1 class="display-4">🔍 网站状态检查</h1>
            <p class="lead">检查体育赛事网站的各项功能</p>
        </div>

        <!-- 基本功能测试 -->
        <div class="row">
            <div class="col-md-6">
                <div class="card test-card">
                    <div class="card-header">
                        <h5><i class="fas fa-server"></i> 服务器状态</h5>
                    </div>
                    <div class="card-body">
                        <p><i class="fas fa-check-circle status-good"></i> Flask应用运行正常</p>
                        <p><i class="fas fa-check-circle status-good"></i> 端口5000监听中</p>
                        <p><i class="fas fa-check-circle status-good"></i> HTTP响应200 OK</p>
                        <p><strong>访问地址：</strong></p>
                        <ul>
                            <li>本地：<a href="http://127.0.0.1:5000" target="_blank">http://127.0.0.1:5000</a></li>
                            <li>局域网：<a href="http://10.194.242.36:5000" target="_blank">http://10.194.242.36:5000</a></li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card test-card">
                    <div class="card-header">
                        <h5><i class="fas fa-database"></i> 数据状态</h5>
                    </div>
                    <div class="card-body">
                        <p><i class="fas fa-check-circle status-good"></i> 21个体育赛事已加载</p>
                        <p><i class="fas fa-check-circle status-good"></i> 7个运动类别已定义</p>
                        <p><i class="fas fa-check-circle status-good"></i> 亮点数据已完善</p>
                        <p><i class="fas fa-check-circle status-good"></i> 2024年历史数据已更新</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 功能测试 -->
        <div class="row mt-4">
            <div class="col-md-4">
                <div class="card test-card">
                    <div class="card-header">
                        <h5><i class="fas fa-home"></i> 主页功能</h5>
                    </div>
                    <div class="card-body">
                        <a href="/" class="btn btn-primary btn-sm mb-2" target="_blank">测试主页</a>
                        <ul class="small">
                            <li>特色赛事轮播</li>
                            <li>运动类别展示</li>
                            <li>搜索功能</li>
                            <li>导航菜单</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card test-card">
                    <div class="card-header">
                        <h5><i class="fas fa-calendar"></i> 赛事列表</h5>
                    </div>
                    <div class="card-body">
                        <a href="/events" class="btn btn-success btn-sm mb-2" target="_blank">测试赛事列表</a>
                        <ul class="small">
                            <li>所有赛事展示</li>
                            <li>类别筛选</li>
                            <li>搜索筛选</li>
                            <li>分页功能</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card test-card">
                    <div class="card-header">
                        <h5><i class="fas fa-info-circle"></i> 赛事详情</h5>
                    </div>
                    <div class="card-body">
                        <a href="/events/1" class="btn btn-info btn-sm mb-2" target="_blank">测试FIFA世界杯</a>
                        <ul class="small">
                            <li>赛事基本信息</li>
                            <li>运动亮点</li>
                            <li>历史数据</li>
                            <li>场馆信息</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- 2024年更新内容 -->
        <div class="card test-card mt-4">
            <div class="card-header bg-success text-white">
                <h5><i class="fas fa-trophy"></i> 2024年最新更新内容</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <h6>⚽ 足球</h6>
                        <ul class="small">
                            <li>2024年西甲：皇家马德里 🏆</li>
                            <li>2024年意甲：国际米兰 🏆</li>
                            <li>2024年欧冠：皇家马德里 🏆</li>
                        </ul>
                    </div>
                    <div class="col-md-3">
                        <h6>🎾 网球</h6>
                        <ul class="small">
                            <li>2024年温网：阿尔卡拉斯 🏆</li>
                            <li>2024年澳网：辛纳 🏆</li>
                            <li>2024年法网：阿尔卡拉斯 🏆</li>
                            <li>2024年美网：辛纳 🏆</li>
                        </ul>
                    </div>
                    <div class="col-md-3">
                        <h6>🏀 篮球</h6>
                        <ul class="small">
                            <li>2024年NBA：波士顿凯尔特人 🏆</li>
                        </ul>
                    </div>
                    <div class="col-md-3">
                        <h6>🏎️ 其他</h6>
                        <ul class="small">
                            <li>2024年F1：维斯塔潘 🏆</li>
                            <li>2024年高尔夫：舍夫勒 🏆</li>
                            <li>2024年巴黎奥运 🏆</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快速链接 -->
        <div class="text-center mt-5">
            <h4>🔗 快速访问链接</h4>
            <div class="d-flex justify-content-center gap-3 flex-wrap mt-3">
                <a href="/" class="btn btn-primary">🏠 主页</a>
                <a href="/events" class="btn btn-success">📅 赛事列表</a>
                <a href="/events?category=1" class="btn btn-warning">⚽ 足球赛事</a>
                <a href="/events?category=2" class="btn btn-info">🏀 篮球赛事</a>
                <a href="/events?category=3" class="btn btn-secondary">🎾 网球赛事</a>
                <a href="/static/highlights_summary.html" class="btn btn-outline-primary">📊 亮点总结</a>
            </div>
        </div>

        <!-- 故障排除 -->
        <div class="card test-card mt-4">
            <div class="card-header bg-warning text-dark">
                <h5><i class="fas fa-tools"></i> 如果遇到问题</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>常见问题：</h6>
                        <ul class="small">
                            <li><strong>页面加载慢：</strong> 刷新浏览器或清除缓存</li>
                            <li><strong>图片不显示：</strong> 检查网络连接</li>
                            <li><strong>功能异常：</strong> 重启Flask应用</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>调试工具：</h6>
                        <ul class="small">
                            <li><a href="/static/debug_test.html">调试测试页面</a></li>
                            <li><a href="/static/test_images.html">图片测试页面</a></li>
                            <li>浏览器开发者工具 (F12)</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
