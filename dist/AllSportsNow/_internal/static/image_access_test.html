<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 图片访问测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .test-image {
            width: 200px;
            height: 150px;
            object-fit: cover;
            border: 3px solid #007bff;
            margin: 10px;
        }
        .test-item {
            border: 2px solid #dee2e6;
            border-radius: 0.5rem;
            padding: 1rem;
            margin-bottom: 1rem;
            text-align: center;
        }
        .status-success { color: #28a745; font-weight: bold; }
        .status-error { color: #dc3545; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container py-5">
        <h1>🔍 图片访问测试</h1>
        <p class="lead">直接测试图片文件的访问情况</p>
        
        <div class="alert alert-info">
            <h5>📋 测试说明</h5>
            <p class="mb-0">这个页面直接使用HTML img标签测试图片访问，如果图片显示正常，说明文件存在且可访问。</p>
        </div>
        
        <h3>📂 分类图片测试</h3>
        <div class="row">
            <div class="col-md-3">
                <div class="test-item">
                    <h6>⚽ Football</h6>
                    <img src="/static/images/homepage/categories/football_category.jpg" 
                         class="test-image" 
                         alt="Football"
                         onload="this.nextElementSibling.innerHTML='<span class=\\"status-success\\">✅ 加载成功</span>'"
                         onerror="this.nextElementSibling.innerHTML='<span class=\\"status-error\\">❌ 加载失败</span>'">
                    <div class="mt-2">加载中...</div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="test-item">
                    <h6>🏀 Basketball</h6>
                    <img src="/static/images/homepage/categories/basketball_category.jpg" 
                         class="test-image" 
                         alt="Basketball"
                         onload="this.nextElementSibling.innerHTML='<span class=\\"status-success\\">✅ 加载成功</span>'"
                         onerror="this.nextElementSibling.innerHTML='<span class=\\"status-error\\">❌ 加载失败</span>'">
                    <div class="mt-2">加载中...</div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="test-item">
                    <h6>🎾 Tennis</h6>
                    <img src="/static/images/homepage/categories/tennis_category.jpg" 
                         class="test-image" 
                         alt="Tennis"
                         onload="this.nextElementSibling.innerHTML='<span class=\\"status-success\\">✅ 加载成功</span>'"
                         onerror="this.nextElementSibling.innerHTML='<span class=\\"status-error\\">❌ 加载失败</span>'">
                    <div class="mt-2">加载中...</div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="test-item">
                    <h6>🏊 Swimming</h6>
                    <img src="/static/images/homepage/categories/swimming_category.jpg" 
                         class="test-image" 
                         alt="Swimming"
                         onload="this.nextElementSibling.innerHTML='<span class=\\"status-success\\">✅ 加载成功</span>'"
                         onerror="this.nextElementSibling.innerHTML='<span class=\\"status-error\\">❌ 加载失败</span>'">
                    <div class="mt-2">加载中...</div>
                </div>
            </div>
        </div>
        
        <h3 class="mt-5">🏆 赛事图片测试</h3>
        <div class="row">
            <div class="col-md-3">
                <div class="test-item">
                    <h6>FIFA World Cup 2026</h6>
                    <img src="/static/images/events/fifa_world_cup_2026.jpg" 
                         class="test-image" 
                         alt="FIFA World Cup 2026"
                         onload="this.nextElementSibling.innerHTML='<span class=\\"status-success\\">✅ 加载成功</span>'"
                         onerror="this.nextElementSibling.innerHTML='<span class=\\"status-error\\">❌ 加载失败</span>'">
                    <div class="mt-2">加载中...</div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="test-item">
                    <h6>UEFA Champions League</h6>
                    <img src="/static/images/events/uefa_champions_league_final_2025.jpg" 
                         class="test-image" 
                         alt="UEFA Champions League"
                         onload="this.nextElementSibling.innerHTML='<span class=\\"status-success\\">✅ 加载成功</span>'"
                         onerror="this.nextElementSibling.innerHTML='<span class=\\"status-error\\">❌ 加载失败</span>'">
                    <div class="mt-2">加载中...</div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="test-item">
                    <h6>NBA Finals 2025</h6>
                    <img src="/static/images/events/nba_finals_2025.jpg" 
                         class="test-image" 
                         alt="NBA Finals 2025"
                         onload="this.nextElementSibling.innerHTML='<span class=\\"status-success\\">✅ 加载成功</span>'"
                         onerror="this.nextElementSibling.innerHTML='<span class=\\"status-error\\">❌ 加载失败</span>'">
                    <div class="mt-2">加载中...</div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="test-item">
                    <h6>Wimbledon 2025</h6>
                    <img src="/static/images/events/wimbledon_2025.jpg" 
                         class="test-image" 
                         alt="Wimbledon 2025"
                         onload="this.nextElementSibling.innerHTML='<span class=\\"status-success\\">✅ 加载成功</span>'"
                         onerror="this.nextElementSibling.innerHTML='<span class=\\"status-error\\">❌ 加载失败</span>'">
                    <div class="mt-2">加载中...</div>
                </div>
            </div>
        </div>
        
        <div class="mt-5">
            <h3>🔧 故障排除</h3>
            <div class="alert alert-warning">
                <h5>如果图片不显示：</h5>
                <ol>
                    <li><strong>检查控制台:</strong> 按F12查看Console标签的错误信息</li>
                    <li><strong>检查网络:</strong> 按F12查看Network标签的图片请求</li>
                    <li><strong>清除缓存:</strong> Ctrl+Shift+Delete</li>
                    <li><strong>硬刷新:</strong> Ctrl+F5</li>
                </ol>
            </div>
        </div>
        
        <div class="text-center mt-4">
            <h4>🎯 快速访问</h4>
            <div class="d-flex justify-content-center flex-wrap">
                <a href="/" target="_blank" class="btn btn-primary m-2">
                    <i class="fas fa-home"></i> 主页
                </a>
                <a href="/events" target="_blank" class="btn btn-success m-2">
                    <i class="fas fa-list"></i> 赛事列表
                </a>
                <button onclick="checkImageStatus()" class="btn btn-info m-2">
                    <i class="fas fa-chart-bar"></i> 检查状态
                </button>
            </div>
        </div>
    </div>

    <script>
        function checkImageStatus() {
            const images = document.querySelectorAll('.test-image');
            let loadedCount = 0;
            let failedCount = 0;
            
            images.forEach(img => {
                if (img.complete) {
                    if (img.naturalWidth > 0) {
                        loadedCount++;
                    } else {
                        failedCount++;
                    }
                }
            });
            
            const total = images.length;
            const pending = total - loadedCount - failedCount;
            
            alert(`📊 图片加载状态:\n\n✅ 成功: ${loadedCount}\n❌ 失败: ${failedCount}\n⏳ 加载中: ${pending}\n📊 总计: ${total}\n\n${loadedCount === total ? '🎉 所有图片加载成功！' : '⚠️ 有图片加载问题，请检查控制台'}`);
        }
        
        // 页面加载完成后自动检查
        window.addEventListener('load', function() {
            console.log('🔍 图片访问测试页面已加载');
            
            setTimeout(() => {
                checkImageStatus();
            }, 3000);
        });
    </script>
</body>
</html>
