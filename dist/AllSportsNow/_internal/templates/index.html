{% extends 'base.html' %}

{% block title %}{{ get_text('home_title') }} - {{ get_text('nav_home') }}{% endblock %}
{% block og_description %}Find and explore upcoming sports events, tournaments, and championships from around the world.{% endblock %}

{% block extra_css %}
<style>
/* 单个赛事全屏滚动样式 */
.featured-events-scroll-container {
    position: relative;
    overflow: hidden;
    border-radius: 1rem;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.featured-events-scroll {
    display: flex;
    overflow-x: auto;
    scroll-behavior: smooth;
    scroll-snap-type: x mandatory;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.featured-events-scroll::-webkit-scrollbar {
    display: none;
}

.featured-event-card {
    flex: 0 0 100%;
    scroll-snap-align: start;
    background: white;
    overflow: hidden;
    min-height: 400px;
    display: flex;
    flex-direction: column;
}

.featured-event-image {
    height: 280px;
    overflow: hidden;
    position: relative;
    border-radius: 1rem 1rem 0 0;
}

.featured-event-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
}

.featured-event-card:hover .featured-event-image img {
    transform: scale(1.05);
}

.featured-event-content {
    padding: 2rem;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.event-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1rem;
    line-height: 1.3;
}

.event-description {
    color: #6c757d;
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: 1.5rem;
    flex: 1;
}

.event-meta {
    margin-bottom: 1.5rem;
}

.event-meta-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
    font-size: 1rem;
    color: #495057;
}

.event-meta-item i {
    width: 24px;
    margin-right: 1rem;
    font-size: 1rem;
    color: #007bff;
}

.event-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.event-actions .btn {
    flex: 1;
    min-width: 150px;
    font-size: 1rem;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.event-category-badge {
    position: absolute;
    top: 1.5rem;
    left: 1.5rem;
    z-index: 2;
}

.event-category-badge .badge {
    font-size: 1rem;
    padding: 0.75rem 1.5rem;
    border-radius: 2rem;
    background: rgba(0,123,255,0.9);
    backdrop-filter: blur(10px);
    font-weight: 600;
}

.next-event-badge {
    position: absolute;
    top: 1.5rem;
    right: 1.5rem;
    z-index: 2;
}

.next-event-badge .badge {
    font-size: 0.9rem;
    padding: 0.6rem 1.2rem;
    border-radius: 1.5rem;
    background: rgba(255,193,7,0.95);
    backdrop-filter: blur(10px);
    font-weight: 600;
    color: #000;
}

/* 滚动导航样式 */
.scroll-navigation {
    margin-top: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}

.scroll-navigation .btn {
    padding: 0.75rem 1.5rem;
    border-radius: 2rem;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    border: 2px solid #007bff;
    min-width: 120px;
}

.scroll-navigation .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,123,255,0.3);
}

.scroll-indicator {
    font-size: 1rem;
    color: #6c757d;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    background: rgba(248,249,250,0.9);
    border-radius: 2rem;
    border: 2px solid #dee2e6;
    backdrop-filter: blur(10px);
}

.scroll-indicator .current-slide {
    color: #007bff;
    font-weight: 700;
    font-size: 1.1rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .featured-event-card {
        min-height: 350px;
    }

    .featured-event-image {
        height: 200px;
    }

    .featured-event-content {
        padding: 1.5rem;
    }

    .event-title {
        font-size: 1.25rem;
    }

    .event-description {
        font-size: 0.9rem;
    }

    .event-actions {
        flex-direction: column;
    }

    .event-actions .btn {
        flex: none;
        width: 100%;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container">
    <!-- Hero Section -->
    <section class="hero-section rounded-lg shadow-lg mb-5">
        <div class="container py-5">
            <div class="row py-lg-5 align-items-center">
                <div class="col-lg-7 col-md-8 mx-auto text-center">
                    <h1 class="fw-bold mb-4 animate-on-scroll">{{ get_text('home_subtitle') }}</h1>
                    <p class="lead mb-4 animate-on-scroll">{{ get_text('home_description') }}</p>
                    <div class="d-flex justify-content-center animate-on-scroll">
                        <form class="search-form w-100" action="{{ url_for('events_list') }}" method="get">
                            <div class="input-group input-group-lg">
                                <input class="form-control" type="search" name="q" placeholder="{{ get_text('search_placeholder') }}" aria-label="Search">
                                <button class="btn btn-primary" type="submit">
                                    <i class="fas fa-search me-2"></i>{{ get_text('search_button') }}
                                </button>
                            </div>
                        </form>
                    </div>
                    <div class="mt-4 d-flex justify-content-center gap-2 animate-on-scroll">
                        <span class="badge bg-primary">{{ get_text('football') }}</span>
                        <span class="badge bg-primary">{{ get_text('basketball') }}</span>
                        <span class="badge bg-primary">{{ get_text('tennis') }}</span>
                        <span class="badge bg-primary">{{ get_text('formula_1') }}</span>
                        <span class="badge bg-primary">{{ get_text('golf') }}</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 删除原有的轮播图精选赛事区域 -->

    <!-- Unified Featured Events Section -->
    <section class="mb-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="mb-0">{{ get_text('featured_events') }}</h2>
            <a href="{{ url_for('events_list') }}" class="btn btn-outline-primary btn-sm">
                {{ get_text('view_all') }} <i class="fas fa-arrow-right ms-1"></i>
            </a>
        </div>

        <!-- Unified Horizontal Scrolling Container -->
        <div class="featured-events-scroll-container">
            <div class="featured-events-scroll">
                {% if featured_events %}
                    {% for event in featured_events %}
                        <div class="featured-event-card">
                            <div class="featured-event-image">
                                <img src="{{ event.get_featured_image_url() }}" alt="{{ event.name }}" class="img-fluid">
                                <div class="event-category-badge">
                                    <span class="badge bg-primary">{{ get_text(event.category.name.lower()) }}</span>
                                </div>
                                {% if loop.first %}
                                    <div class="next-event-badge">
                                        <span class="badge bg-warning text-dark">{{ get_text('next_big_event') }}</span>
                                    </div>
                                {% endif %}
                            </div>
                            <div class="featured-event-content">
                                <h4 class="event-title">{{ get_text(event.name) }}</h4>
                                <p class="event-description">{{ get_text(event.description) }}</p>
                                <div class="event-meta">
                                    <div class="event-meta-item">
                                        <i class="fas fa-map-marker-alt text-primary"></i>
                                        <span>{{ get_text(event.location) }}</span>
                                    </div>
                                    <div class="event-meta-item">
                                        <i class="fas fa-calendar-alt text-primary"></i>
                                        <span>{{ format_date(event.start_date, 'full') }}</span>
                                    </div>
                                    {% if loop.first %}
                                        <div class="event-meta-item">
                                            <i class="fas fa-clock text-warning"></i>
                                            <span class="countdown-text" data-event-date="{{ event.start_date.strftime('%Y-%m-%d') }}">
                                                {{ get_text('coming_soon') }}
                                            </span>
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="event-actions">
                                    <a href="{{ url_for('event_details', event_id=event.id) }}" class="btn btn-primary">
                                        <i class="fas fa-info-circle me-1"></i> {{ get_text('view_details') }}
                                    </a>
                                    <a href="{{ url_for('event_details', event_id=event.id) }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-ticket-alt me-1"></i> {{ get_text('get_tickets') }}
                                    </a>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                {% else %}
                    <div class="featured-event-card no-events-card">
                        <div class="featured-event-content text-center">
                            <i class="fas fa-calendar-alt fa-3x text-muted mb-3"></i>
                            <h4 class="event-title">{{ get_text('no_featured_events') }}</h4>
                            <p class="event-description">{{ get_text('check_back_soon') }}</p>
                            <div class="event-actions">
                                <a href="{{ url_for('events_list') }}" class="btn btn-primary">
                                    <i class="fas fa-list me-1"></i> {{ get_text('browse_all_events') }}
                                </a>
                            </div>
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Enhanced Scroll Navigation -->
        <div class="scroll-navigation text-center mt-3">
            <button class="btn btn-outline-primary btn-sm scroll-left">
                <i class="fas fa-chevron-left"></i> {{ get_text('previous') }}
            </button>
            <span class="scroll-indicator mx-3">
                <span class="current-slide">1</span> / <span class="total-slides">{{ featured_events|length if featured_events else 0 }}</span>
            </span>
            <button class="btn btn-outline-primary btn-sm scroll-right">
                <i class="fas fa-chevron-right"></i> {{ get_text('next') }}
            </button>
        </div>
    </section>

    <!-- Categories Section -->
    <section class="mb-5" id="categories">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="mb-0">{{ get_text('browse_categories') }}</h2>
        </div>
        <div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 row-cols-lg-4 g-4">
            {% for category in categories %}
                <div class="col animate-on-scroll" style="animation-delay: {{ loop.index0 * 0.1 }}s">
                    <div class="card h-100 category-card hover-shadow">
                        <img src="{{ category.get_image_url() }}"
                             class="card-img-top category-image"
                             alt="{{ category.name }}"
                             style="height: 220px; object-fit: cover; display: block !important; opacity: 1 !important; visibility: visible !important; border-radius: 0.5rem 0.5rem 0 0;"
                             onerror="console.log('Category image failed:', this.src); this.style.background='linear-gradient(135deg, #f8f9fa, #e9ecef)'; this.style.display='block';"
                             onload="console.log('Category image loaded:', this.src); this.style.display='block'; this.style.opacity='1';">
                        <div class="card-body text-center">
                            <h5 class="card-title">{{ get_text(category.name.lower()) }}</h5>
                            <p class="card-text text-muted">{{ get_text('explore_events') }}</p>
                            <a href="{{ url_for('events_list', category=category.id) }}" class="btn btn-outline-primary w-100">
                                <i class="fas fa-list me-1"></i> {{ get_text('nav_events') }}
                            </a>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>

        <!-- Debug Info -->
        <div class="mt-4">
            <details>
                <summary class="btn btn-outline-secondary btn-sm">🔍 调试信息</summary>
                <div class="mt-3 p-3 bg-light rounded">
                    <h6>分类数量: {{ categories|length }}</h6>
                    {% for category in categories %}
                        <div class="small mb-2">
                            <strong>{{ category.name }}:</strong> {{ category.get_image_url() }}
                        </div>
                    {% endfor %}
                    <div class="mt-3">
                        <a href="/debug/categories" target="_blank" class="btn btn-sm btn-info">查看分类数据</a>
                        <a href="/static/category_debug.html" target="_blank" class="btn btn-sm btn-warning">图片测试页面</a>
                    </div>
                </div>
            </details>
        </div>
    </section>

    <!-- Upcoming Events Section -->
    <section class="mb-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="mb-0">{{ get_text('upcoming_events') }}</h2>
            <a href="{{ url_for('events_list') }}" class="btn btn-outline-primary btn-sm">
                {{ get_text('view_all') }} <i class="fas fa-arrow-right ms-1"></i>
            </a>
        </div>
        <div class="row">
            <div class="col-lg-8">
                <div class="card shadow-sm mb-4">
                    <div class="card-body p-0">
                        {% if featured_events %}
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead>
                                        <tr>
                                            <th>{{ get_text('event') }}</th>
                                            <th>{{ get_text('date') }}</th>
                                            <th>{{ get_text('location') }}</th>
                                            <th>{{ get_text('category') }}</th>
                                            <th></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for event in featured_events[:5] %}
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <img src="{{ event.get_image_url() }}" class="rounded me-2" width="40" height="40" alt="{{ get_text(event.name) }}" style="object-fit: cover;">
                                                        <span>{{ get_text(event.name) }}</span>
                                                    </div>
                                                </td>
                                                <td>{{ format_date(event.start_date, 'full') }}</td>
                                                <td>{{ get_text(event.location) }}</td>
                                                <td><span class="badge bg-primary">{{ get_text(event.category.name.lower()) }}</span></td>
                                                <td>
                                                    <a href="{{ url_for('event_details', event_id=event.id) }}" class="btn btn-sm btn-outline-primary">
                                                        {{ get_text('view_details') }}
                                                    </a>
                                                </td>
                                            </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <div class="p-4 text-center">
                                <i class="fas fa-calendar-alt fa-3x text-muted mb-3"></i>
                                <h5>{{ get_text('no_upcoming_events') }}</h5>
                                <p class="text-muted">{{ get_text('check_back_soon') }}.</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">{{ get_text('next_big_event') }}</h5>
                    </div>
                    <div class="card-body">
                        {% if featured_events %}
                            {% set next_event = featured_events[0] %}
                            <h5 class="card-title">{{ get_text(next_event.name) }}</h5>
                            <p class="card-text">{{ get_text(next_event.description)[:100] }}{% if get_text(next_event.description)|length > 100 %}...{% endif %}</p>
                            <div class="mb-3">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-calendar-alt text-primary me-2"></i>
                                    <span>{{ next_event.start_date.strftime('%B %d, %Y') }}</span>
                                </div>
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-map-marker-alt text-primary me-2"></i>
                                    <span>{{ get_text(next_event.location) }}</span>
                                </div>
                            </div>
                            <a href="{{ url_for('event_details', event_id=next_event.id) }}" class="btn btn-primary w-100 mt-3">
                                <i class="fas fa-ticket-alt me-1"></i> {{ get_text('view_event') }}
                            </a>
                        {% else %}
                            <div class="alert alert-info mb-3">
                                <i class="fas fa-info-circle me-2"></i> {{ get_text('no_upcoming_events') }}.
                            </div>
                            <a href="{{ url_for('events_list') }}" class="btn btn-primary w-100">
                                <i class="fas fa-calendar-alt me-1"></i> {{ get_text('browse_all_events') }}
                            </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Initialize featured events scroll functionality
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Featured Events Scroll initialized');

        // Add animation classes to elements when they come into view
        const animateOnScroll = () => {
            const elements = document.querySelectorAll('.animate-on-scroll');

            elements.forEach(element => {
                const elementPosition = element.getBoundingClientRect().top;
                const windowHeight = window.innerHeight;

                if (elementPosition < windowHeight - 50) {
                    element.classList.add('animated', 'fade-in');
                }
            });
        };

        window.addEventListener('scroll', animateOnScroll);
        animateOnScroll(); // Run once on page load

        // Full-width Featured Events Scroll Functionality
        const scrollContainer = document.querySelector('.featured-events-scroll');
        const scrollLeftBtn = document.querySelector('.scroll-left');
        const scrollRightBtn = document.querySelector('.scroll-right');
        const currentSlideSpan = document.querySelector('.current-slide');
        const totalSlidesSpan = document.querySelector('.total-slides');

        if (scrollContainer && scrollLeftBtn && scrollRightBtn) {
            const cards = scrollContainer.querySelectorAll('.featured-event-card');
            const totalCards = cards.length;

            // Update total slides display
            if (totalSlidesSpan) {
                totalSlidesSpan.textContent = totalCards;
            }

            // Calculate scroll amount based on container width (full width per card)
            const getScrollAmount = () => scrollContainer.clientWidth;

            scrollLeftBtn.addEventListener('click', () => {
                scrollContainer.scrollBy({
                    left: -getScrollAmount(),
                    behavior: 'smooth'
                });
            });

            scrollRightBtn.addEventListener('click', () => {
                scrollContainer.scrollBy({
                    left: getScrollAmount(),
                    behavior: 'smooth'
                });
            });

            // Update button states and slide indicator based on scroll position
            const updateScrollButtons = () => {
                const scrollAmount = getScrollAmount();
                const currentScrollLeft = scrollContainer.scrollLeft;
                const maxScrollLeft = scrollContainer.scrollWidth - scrollContainer.clientWidth;

                const isAtStart = currentScrollLeft <= 10; // Small tolerance
                const isAtEnd = currentScrollLeft >= maxScrollLeft - 10; // Small tolerance

                scrollLeftBtn.disabled = isAtStart;
                scrollRightBtn.disabled = isAtEnd;

                scrollLeftBtn.style.opacity = isAtStart ? '0.5' : '1';
                scrollRightBtn.style.opacity = isAtEnd ? '0.5' : '1';

                // Update current slide indicator (more accurate calculation)
                if (currentSlideSpan && totalCards > 0) {
                    const currentSlide = Math.round(currentScrollLeft / scrollAmount) + 1;
                    currentSlideSpan.textContent = Math.min(Math.max(currentSlide, 1), totalCards);
                }
            };

            // Add scroll event listener with debouncing
            let scrollTimeout;
            scrollContainer.addEventListener('scroll', () => {
                clearTimeout(scrollTimeout);
                scrollTimeout = setTimeout(updateScrollButtons, 50);
            });

            // Initial state and window resize handler
            updateScrollButtons();
            window.addEventListener('resize', updateScrollButtons);

            // Touch/swipe support for mobile
            let isDown = false;
            let startX;
            let scrollLeft;

            scrollContainer.addEventListener('mousedown', (e) => {
                isDown = true;
                scrollContainer.style.cursor = 'grabbing';
                startX = e.pageX - scrollContainer.offsetLeft;
                scrollLeft = scrollContainer.scrollLeft;
            });

            scrollContainer.addEventListener('mouseleave', () => {
                isDown = false;
                scrollContainer.style.cursor = 'grab';
            });

            scrollContainer.addEventListener('mouseup', () => {
                isDown = false;
                scrollContainer.style.cursor = 'grab';
            });

            scrollContainer.addEventListener('mousemove', (e) => {
                if (!isDown) return;
                e.preventDefault();
                const x = e.pageX - scrollContainer.offsetLeft;
                const walk = (x - startX) * 2;
                scrollContainer.scrollLeft = scrollLeft - walk;
            });
        }

        // Countdown functionality for next big event
        const countdownElements = document.querySelectorAll('.countdown-text[data-event-date]');

        function updateCountdowns() {
            countdownElements.forEach(element => {
                const eventDate = new Date(element.dataset.eventDate + 'T00:00:00');
                const now = new Date();
                const timeDiff = eventDate.getTime() - now.getTime();

                if (timeDiff > 0) {
                    const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
                    const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                    const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));

                    if (days > 0) {
                        element.textContent = `${days} {{ get_text('days_left') }}`;
                    } else if (hours > 0) {
                        element.textContent = `${hours} {{ get_text('hours_left') }}`;
                    } else if (minutes > 0) {
                        element.textContent = `${minutes} {{ get_text('minutes_left') }}`;
                    } else {
                        element.textContent = '{{ get_text('starting_soon') }}';
                    }
                } else {
                    element.textContent = '{{ get_text('event_started') }}';
                }
            });
        }

        // Update countdowns every minute
        if (countdownElements.length > 0) {
            updateCountdowns();
            setInterval(updateCountdowns, 60000);
        }
    });
</script>
{% endblock %}
