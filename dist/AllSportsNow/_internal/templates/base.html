<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Find and explore upcoming sports events, tournaments, and championships from around the world.">
    <meta name="keywords" content="sports, events, tournaments, championships, football, basketball, tennis, swimming, athletics, golf, formula 1">
    <title>{% block title %}{{ get_text('home_title') }}{% endblock %}</title>
    <!-- Favicon -->
    <link rel="icon" href="{{ url_for('static', filename='favicon.ico') }}" type="image/x-icon">
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/custom.css') }}">
    <!-- Integrated Featured Events CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/integrated_featured_events.css') }}">
    {% block extra_css %}{% endblock %}
    <!-- Open Graph tags for social sharing -->
    <meta property="og:title" content="{% block og_title %}{{ get_text('home_title') }}{% endblock %}">
    <meta property="og:description" content="{% block og_description %}Find and explore upcoming sports events, tournaments, and championships from around the world.{% endblock %}">
    <meta property="og:image" content="{% block og_image %}{{ url_for('static', filename='images/og-image.jpg', _external=True) }}{% endblock %}">
    <meta property="og:url" content="{{ request.url }}">
    <meta property="og:type" content="website">
</head>
<body>
    <!-- Skip to content link for accessibility -->
    <a href="#main-content" class="skip-link">Skip to content</a>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <img src="{{ url_for('static', filename='images/allsportsnow-logo.svg') }}" alt="AllSportsNow Logo" style="width: 24px; height: 24px; margin-right: 8px; vertical-align: middle;">
                {{ get_text('home_title') }}
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link {% if request.path == url_for('index') %}active{% endif %}" href="{{ url_for('index') }}" aria-current="{% if request.path == url_for('index') %}page{% endif %}">
                            <i class="fas fa-home me-1"></i> {{ get_text('nav_home') }}
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.path == url_for('events_list') %}active{% endif %}" href="{{ url_for('events_list') }}" aria-current="{% if request.path == url_for('events_list') %}page{% endif %}">
                            <i class="fas fa-calendar-alt me-1"></i> {{ get_text('nav_events') }}
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="categoriesDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-list me-1"></i> {{ get_text('nav_categories') }}
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="categoriesDropdown">
                            <li><a class="dropdown-item" href="{{ url_for('events_list', category=1) }}">{{ get_text('football') }}</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('events_list', category=2) }}">{{ get_text('basketball') }}</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('events_list', category=3) }}">{{ get_text('tennis') }}</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('events_list', category=4) }}">{{ get_text('swimming') }}</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('events_list', category=5) }}">{{ get_text('athletics') }}</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('events_list', category=6) }}">{{ get_text('golf') }}</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('events_list', category=7) }}">{{ get_text('formula_1') }}</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Coming Soon">
                            <i class="fas fa-newspaper me-1"></i> {{ get_text('News') }}
                        </a>
                    </li>
                </ul>
                <div class="d-flex align-items-center">
                    <div class="search-container position-relative me-3">
                        <form class="search-form" action="{{ url_for('events_list') }}" method="get">
                            <div class="input-group">
                                <input class="form-control search-autocomplete" type="search" name="q" placeholder="{{ get_text('Search events') }}" aria-label="{{ get_text('Search events') }}" autocomplete="off">
                                <button class="btn btn-primary" type="submit">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </form>
                        <div class="search-results position-absolute w-100 bg-white shadow-lg rounded-bottom p-2" style="display: none; z-index: 1000;"></div>
                    </div>
                    <ul class="navbar-nav">
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('login') }}" data-bs-toggle="tooltip" data-bs-placement="bottom" title="{{ get_text('Login') }}">
                                <i class="fas fa-sign-in-alt"></i>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('register') }}" data-bs-toggle="tooltip" data-bs-placement="bottom" title="{{ get_text('Register') }}">
                                <i class="fas fa-user-plus"></i>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Language Switcher -->
                <div class="navbar-nav ms-auto">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="languageDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-globe me-1"></i>
                            {% if current_language == 'zh' %}中文{% else %}English{% endif %}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="languageDropdown">
                            <li>
                                <a class="dropdown-item {% if current_language == 'en' %}active{% endif %}" href="{{ url_for('set_language_route', language='en') }}">
                                    <i class="fas fa-flag-usa me-2"></i> English
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item {% if current_language == 'zh' %}active{% endif %}" href="{{ url_for('set_language_route', language='zh') }}">
                                    <i class="fas fa-flag me-2" style="color: #de2910;"></i> 中文
                                </a>
                            </li>
                        </ul>
                    </li>
                </div>
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="container mt-3">
                {% for category, message in messages %}
                    <div class="alert alert-{{ category if category != 'message' else 'info' }} alert-dismissible fade show shadow-sm">
                        {% if category == 'success' %}
                            <i class="fas fa-check-circle me-2"></i>
                        {% elif category == 'danger' or category == 'error' %}
                            <i class="fas fa-exclamation-circle me-2"></i>
                        {% elif category == 'warning' %}
                            <i class="fas fa-exclamation-triangle me-2"></i>
                        {% else %}
                            <i class="fas fa-info-circle me-2"></i>
                        {% endif %}
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}

    <!-- Main Content -->
    <main id="main-content" class="py-4">
        {% block content %}{% endblock %}
    </main>

    <!-- Back to top button -->
    <button class="back-to-top" aria-label="{{ get_text('back_to_top') }}">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- Dark mode toggle -->
    <button class="dark-mode-toggle" aria-label="Toggle dark mode">
        <i class="fas fa-moon"></i>
    </button>

    <!-- Cookie consent banner -->
    <div class="cookie-banner" style="display: none;">
        <p>We use cookies to enhance your experience. By continuing to visit this site you agree to our use of cookies.</p>
        <div class="btn-group">
            <button class="btn btn-primary accept-cookies">Accept</button>
            <button class="btn btn-outline-secondary decline-cookies">Decline</button>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-5 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 col-md-6 mb-4 mb-md-0">
                    <h5 class="mb-3">
                        <img src="{{ url_for('static', filename='images/allsportsnow-logo.svg') }}" alt="AllSportsNow Logo" style="width: 20px; height: 20px; margin-right: 8px; vertical-align: middle;">
                        {{ get_text('home_title') }}
                    </h5>
                    <p class="mb-3">{{ get_text('Your one-stop platform for sports event information and updates from around the world.') }}</p>
                    <div class="social-links mb-4">
                        <a href="#" class="me-2 text-white" aria-label="Facebook"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="me-2 text-white" aria-label="Twitter"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="me-2 text-white" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="me-2 text-white" aria-label="YouTube"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>
                <div class="col-lg-2 col-md-6 mb-4 mb-md-0">
                    <h5 class="mb-3">{{ get_text('quick_links') }}</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="{{ url_for('index') }}" class="text-white"><i class="fas fa-chevron-right me-1"></i>{{ get_text('nav_home') }}</a></li>
                        <li class="mb-2"><a href="{{ url_for('events_list') }}" class="text-white"><i class="fas fa-chevron-right me-1"></i>{{ get_text('nav_events') }}</a></li>
                        <li class="mb-2"><a href="#" class="text-white"><i class="fas fa-chevron-right me-1"></i>{{ get_text('nav_about') }}</a></li>
                        <li class="mb-2"><a href="#" class="text-white"><i class="fas fa-chevron-right me-1"></i>{{ get_text('contact') }}</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-6 mb-4 mb-md-0">
                    <h5 class="mb-3">{{ get_text('nav_categories') }}</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="{{ url_for('events_list', category=1) }}" class="text-white"><i class="fas fa-chevron-right me-1"></i>{{ get_text('football') }}</a></li>
                        <li class="mb-2"><a href="{{ url_for('events_list', category=2) }}" class="text-white"><i class="fas fa-chevron-right me-1"></i>{{ get_text('basketball') }}</a></li>
                        <li class="mb-2"><a href="{{ url_for('events_list', category=3) }}" class="text-white"><i class="fas fa-chevron-right me-1"></i>{{ get_text('tennis') }}</a></li>
                        <li class="mb-2"><a href="{{ url_for('events_list', category=7) }}" class="text-white"><i class="fas fa-chevron-right me-1"></i>{{ get_text('formula_1') }}</a></li>
                    </ul>
                </div>
                <div class="col-lg-4 col-md-6 mb-4 mb-md-0">
                    <h5 class="mb-3">{{ get_text('newsletter') }}</h5>
                    <p class="mb-3">{{ get_text('subscribe_newsletter') }}</p>
                    <form class="newsletter-form">
                        <div class="input-group">
                            <input type="email" class="form-control" placeholder="{{ get_text('enter_email') }}" aria-label="{{ get_text('enter_email') }}" required>
                            <button class="btn btn-primary" type="submit">{{ get_text('subscribe') }}</button>
                        </div>
                    </form>
                    <div class="mt-4">
                        <h5 class="mb-3">{{ get_text('contact') }}</h5>
                        <address class="mb-0">
                            <p class="mb-2"><i class="fas fa-map-marker-alt me-2"></i>{{ get_text('company_address') }}</p>
                            <p class="mb-2"><i class="fas fa-phone me-2"></i>{{ get_text('company_phone') }}</p>
                            <p class="mb-0"><i class="fas fa-envelope me-2"></i>{{ get_text('company_email') }}</p>
                        </address>
                    </div>
                </div>
            </div>
            <hr class="my-4">
            <div class="row align-items-center">
                <div class="col-md-6 text-center text-md-start">
                    <p class="mb-md-0">&copy; 2025 {{ get_text('home_title') }}. {{ get_text('all_rights_reserved') }}.</p>
                </div>
                <div class="col-md-6 text-center text-md-end">
                    <ul class="list-inline mb-0">
                        <li class="list-inline-item"><a href="#" class="text-white">{{ get_text('privacy_policy') }}</a></li>
                        <li class="list-inline-item">|</li>
                        <li class="list-inline-item"><a href="#" class="text-white">{{ get_text('terms_of_service') }}</a></li>
                        <li class="list-inline-item">|</li>
                        <li class="list-inline-item"><a href="#" class="text-white">{{ get_text('cookie_policy') }}</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Ultimate Image Display Fix - Prevent Disappearing -->
    <style>
        /* 强制显示所有图片 - 最高优先级 */
        img, .card-img-top, .event-list-image, .event-detail-image, .category-image, .related-event-image {
            display: block !important;
            opacity: 1 !important;
            visibility: visible !important;
            transition: none !important;
            animation: none !important;
            max-width: 100% !important;
            height: auto !important;
        }

        /* 特定图片类型的强制显示 */
        .category-card img,
        .event-card img,
        .card img,
        img[src*="/static/images/"] {
            display: block !important;
            opacity: 1 !important;
            visibility: visible !important;
            transition: none !important;
            animation: none !important;
        }

        /* 防止任何隐藏机制 */
        .force-show-images img,
        .force-show-images .card-img-top,
        .force-show-images .event-list-image,
        .force-show-images .event-detail-image,
        .force-show-images .category-image,
        .force-show-images .related-event-image {
            display: block !important;
            opacity: 1 !important;
            visibility: visible !important;
            transition: none !important;
            animation: none !important;
        }

        /* 覆盖可能的Bootstrap或其他CSS */
        .d-none img, .invisible img, .opacity-0 img {
            display: block !important;
            opacity: 1 !important;
            visibility: visible !important;
        }

        /* 确保图片容器也显示 */
        .category-card, .event-card, .card {
            display: block !important;
            opacity: 1 !important;
            visibility: visible !important;
        }
    </style>

    <script>
        // 简化的图片显示修复 - 避免性能问题
        (function() {
            'use strict';

            // 立即添加保护类
            document.documentElement.classList.add('force-show-images');

            // 简化的图片保护函数
            function fixImages() {
                try {
                    const images = document.querySelectorAll('img');
                    images.forEach(img => {
                        // 只设置基本的显示样式，不锁定属性
                        if (img.style.display === 'none' || img.style.opacity === '0' || img.style.visibility === 'hidden') {
                            img.style.display = 'block';
                            img.style.opacity = '1';
                            img.style.visibility = 'visible';
                        }

                        // 移除可能的隐藏类
                        img.classList.remove('d-none', 'invisible', 'opacity-0');
                    });
                } catch (error) {
                    console.log('Image fix error:', error);
                }
            }

            // 页面加载时执行
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', fixImages);
            } else {
                fixImages();
            }

            // 页面完全加载后执行
            window.addEventListener('load', fixImages);

            // 延迟执行一次，确保所有动态内容加载完成
            setTimeout(fixImages, 1000);
        })();
    </script>
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    <script src="{{ url_for('static', filename='js/custom.js') }}"></script>
    <!-- Page-specific JavaScript -->
    {% block extra_js %}{% endblock %}

    <!-- Accessibility script -->
    <script>
        // Add 'aria-current' to active navigation items
        document.addEventListener('DOMContentLoaded', function() {
            const currentPath = window.location.pathname;
            document.querySelectorAll('.navbar-nav .nav-link').forEach(link => {
                if (link.getAttribute('href') === currentPath) {
                    link.setAttribute('aria-current', 'page');
                    link.classList.add('active');
                }
            });
        });
    </script>
</body>
</html>
